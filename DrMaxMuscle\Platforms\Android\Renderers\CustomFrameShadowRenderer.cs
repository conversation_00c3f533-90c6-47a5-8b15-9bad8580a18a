﻿using Android.Content;
using Android.OS;
using DrMaxMuscle.Controls;
using DrMaxMuscle.Platforms.Android.Renderers;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Handlers.Compatibility;
using Microsoft.Maui.Controls.Platform;
using Microsoft.Maui.Platform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

[assembly: ExportRenderer(typeof(CustomFrame), typeof(CustomFrameShadowRenderer))]
namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class CustomFrameShadowRenderer : FrameRenderer
    {
        public CustomFrameShadowRenderer(Context context) : base(context)
        {
        }

        //protected override void OnElementChanged(ElementChangedEventArgs<Frame> e)
        //{
        //    base.OnElementChanged(e);
        //    var element = e.NewElement as CustomFrame;


        //    if (element == null) return;
        //    if (e.NewElement != null)
        //    {
        //        ViewGroup.SetBackgroundResource(Resource.Drawable.Shadow);
        //    }
        //}


        protected override void OnElementChanged(ElementChangedEventArgs<Frame> e)
        {
            base.OnElementChanged(e);
            try
            {
                CardElevation = 10;
                if (e.NewElement != null && Build.VERSION.SdkInt >= BuildVersionCodes.P)
                {
                    SetOutlineSpotShadowColor(Microsoft.Maui.Graphics.Colors.Gray.ToPlatform());
                }
            }
            catch (Exception ex)
            {

            }

        }

    }
}
