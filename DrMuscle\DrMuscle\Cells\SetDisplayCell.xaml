﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="DrMuscle.Cells.SetDisplayCell">
    <ViewCell.View>
        <StackLayout BackgroundColor="White" HorizontalOptions="FillAndExpand" Padding="20,10" Margin="5,0">
            <Grid                      HorizontalOptions="FillAndExpand">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="30" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <Label Text="{Binding SetNo}" Grid.Row="0" Grid.Column="0" VerticalOptions="Center" />
                <Image Source="done.png" HeightRequest="25" WidthRequest="25" Aspect="AspectFit" Grid.Row="0" Grid.Column="1" HorizontalOptions="Center" IsVisible="{Binding IsFinished}" />
                <StackLayout Grid.Row="0" Grid.Column="2" HorizontalOptions="Start" Spacing="0">
                    <Label Text="{Binding Reps}" x:Name="LblReps" HorizontalOptions="Start" HorizontalTextAlignment="Start" />
                    <Label Text="Reps" x:Name="LblRepsText" HorizontalOptions="Start" FontSize="10" />
                </StackLayout>
                <Label Text="*" Grid.Row="0" Grid.Column="3" HorizontalOptions="Center" HorizontalTextAlignment="Center" VerticalOptions="Center" />
                <StackLayout Grid.Row="0" Grid.Column="4" HorizontalOptions="End" Spacing="0">
                    <Label x:Name="LblWeight" HorizontalOptions="End" HorizontalTextAlignment="End" />
                    <Label Text="Kgs" x:Name="LblMassUnit" HorizontalOptions="Start" FontSize="10" />
                </StackLayout>
            </Grid>
        </StackLayout>
    </ViewCell.View>
</ViewCell>
