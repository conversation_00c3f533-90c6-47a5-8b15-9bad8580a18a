﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static DrMaxMuscle.Screens.History.HistoryPage;

namespace DrMaxMuscle.Screens.History
{
    public class HistoryDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate HistoryDateTemplate { get; set; }
        public DataTemplate HistoryExerciseTemplate { get; set; }
        public DataTemplate HistorySetTemplate { get; set; }
        public DataTemplate HistoryStatisticTemplate { get; set; }

        protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
        {
            switch (((HistoryItem)item).ItemType)
            {
                case HistoryItemType.DateType: return HistoryDateTemplate;
                case HistoryItemType.ExerciseType: return HistoryExerciseTemplate;
                case HistoryItemType.StatisticType: return HistoryStatisticTemplate;
                default: return HistorySetTemplate;
            }
        }
    }
}
