﻿using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Util;
using Android.Widget;
using DrMaxMuscle.Views;
using Microsoft.Maui.Controls.Platform;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Color = Android.Graphics.Color;
using Paint = Android.Graphics.Paint;
using Font = Microsoft.Maui.Font;
using Android.Views;

using Java.Lang.Reflect;
using Microsoft.Maui.Controls.Handlers.Compatibility;

//[assembly: ExportRenderer(typeof(PickerView), typeof(PickerViewRenderer))]
namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class PickerViewRenderer : ViewRenderer<PickerView, NumberPicker> // Replace NumberPicker with appropriate iOS control if needed
    {
        public PickerViewRenderer(Context context) : base(context)
        {
        }

        protected override void OnElementChanged(ElementChangedEventArgs<PickerView> e)
        {
            base.OnElementChanged(e);

            try
            {
                if (Control == null)
                {
                    SetNativeControl(new NumberPicker(Context));
                }
                else
                {
                    Control.ValueChanged -= Control_ValueChanged;
                }

                if (e.NewElement != null)
                {
                    Control.ValueChanged += Control_ValueChanged;

                    UpdateItemsSource();
                    UpdateSelectedIndex();
                    UpdateFont();
                }
            }
            catch (Exception ex)
            {

            }
        }



        protected override void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            base.OnElementPropertyChanged(sender, e);

            try
            {
                if (e.PropertyName == PickerView.ItemsSourceProperty.PropertyName)
                {
                    UpdateItemsSource();
                }
                else if (e.PropertyName == PickerView.SelectedIndexProperty.PropertyName)
                {
                    UpdateSelectedIndex();
                }
                else if (e.PropertyName == PickerView.FontFamilyProperty.PropertyName)
                {
                    UpdateFont();
                }
                else if (e.PropertyName == PickerView.FontSizeProperty.PropertyName)
                {
                    UpdateFont();
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void UpdateItemsSource()
        {
            try
            {
                var arr = new List<string>();
                if (Element.ItemsSource != null)
                {
                    foreach (var item in Element.ItemsSource)
                    {
                        arr.Add(item.ToString());
                    }

                }

                if (arr.Count > 0)
                {
                    int newMax = arr.Count - 1;
                    if (newMax < Control.Value)
                    {
                        Element.SelectedIndex = newMax;
                    }

                    var extend = Control.MaxValue <= newMax;

                    if (extend)
                    {
                        Control.SetDisplayedValues(arr.ToArray());
                    }

                    Control.MaxValue = newMax;
                    Control.MinValue = 0;

                    if (!extend)
                    {
                        Control.SetDisplayedValues(arr.ToArray());
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void UpdateSelectedIndex()
        {
            try
            {
                if (Element.SelectedIndex < Control.MinValue || Element.SelectedIndex > Control.MaxValue)
                {
                    return;
                }

                Control.Value = Element.SelectedIndex;
                SetTextSize(Control, Element.FontFamily, (float)(Element.FontSize * Context.Resources.DisplayMetrics.Density));
                Control.Invalidate();
                //if (Element.SelectedIndex < Control.MinValue || Element.SelectedIndex >= Control.MaxValue)
                //{
                //    return;
                //}

                //Control.Value = Element.SelectedIndex;
            }
            catch (Exception ex)
            {

            }
        }

        void UpdateFont()
        {
            var font = string.IsNullOrEmpty(Element.FontFamily) ?
                Font.SystemFontOfSize(Element.FontSize) :
                Font.OfSize(Element.FontFamily, Element.FontSize);
            // uncomment code please
            SetTextSize(Control, font.Family, (float)(Element.FontSize * Context.Resources.DisplayMetrics.Density));
        }

        private void SetTextSize(NumberPicker numberPicker, string fontFamily, float textSizeInSp)
        {
            try
            {
                int count = numberPicker.ChildCount;
                for (int i = 0; i < count; i++)
                {
                    var child = numberPicker.GetChildAt(i);
                    var editText = child as EditText;

                    if (editText != null)
                    {
                        try
                        {
                            if (Build.VERSION.SdkInt >= BuildVersionCodes.Q)
                            {

                                numberPicker.SetTextColor(Color.Black);
                                numberPicker.DescendantFocusability = DescendantFocusability.BlockDescendants;
                                numberPicker.WrapSelectorWheel = false;
                                return;
                            }
                            Field selectorWheelPaintField = numberPicker.Class.GetDeclaredField("mSelectorWheelPaint");
                            selectorWheelPaintField.Accessible = true;

                            ((Paint)selectorWheelPaintField.Get(numberPicker)).TextSize = textSizeInSp;
                            ((Paint)selectorWheelPaintField.Get(numberPicker)).Color = Color.Black;

                            //editText.Typeface = fontFamily;
                            editText.SetTextSize(ComplexUnitType.Px, textSizeInSp);
                            editText.SetTextColor(Color.Black);
                            editText.Enabled = false;
                            numberPicker.Invalidate();
                        }
                        catch (System.Exception e)
                        {
                            System.Diagnostics.Debug.WriteLine("SetNumberPickerTextColor failed.", e);
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        void Control_ValueChanged(object sender, NumberPicker.ValueChangeEventArgs e)
        {
            try
            {
                Element.SelectedIndex = e.NewVal;
                UpdateFont();
            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// NumberPicker の文字サイズを変更するハック
        /// </summary>
        /// <see cref="http://stackoverflow.com/questions/22962075/change-the-text-color-of-numberpicker"/>
        /// <param name="numberPicker">Number picker.</param>
        /// <param name="textSizeInSp">Text size in pixel.</param>
        /// // uncomment code please
        private static void SetTextSize(NumberPicker numberPicker, Typeface fontFamily, float textSizeInSp)
        {
            int count = numberPicker.ChildCount;
            for (int i = 0; i < count; i++)
            {
                var child = numberPicker.GetChildAt(i);
                var editText = child as EditText;

                if (editText != null)
                {
                    try
                    {
                        if (Build.VERSION.SdkInt >= BuildVersionCodes.Q)
                        {
                            numberPicker.SetTextColor(Color.Black);
                            numberPicker.DescendantFocusability = DescendantFocusability.BlockDescendants;
                            numberPicker.WrapSelectorWheel = false;
                            return;
                        }
                        Field selectorWheelPaintField = numberPicker.Class
                            .GetDeclaredField("mSelectorWheelPaint");
                        selectorWheelPaintField.Accessible = true;
                        ((Paint)selectorWheelPaintField.Get(numberPicker)).TextSize = textSizeInSp;
                        ((Paint)selectorWheelPaintField.Get(numberPicker)).Color = Color.Black;

                        editText.Typeface = fontFamily;
                        editText.SetTextSize(ComplexUnitType.Px, textSizeInSp);
                        editText.SetTextColor(Color.Black);
                        editText.Enabled = false;
                        numberPicker.Invalidate();
                    }
                    catch (System.Exception e)
                    {
                        System.Diagnostics.Debug.WriteLine("SetNumberPickerTextColor failed.", e);
                    }
                }
            }
        }
    }
}