﻿using System;
using System.Collections.Generic;
using DrMuscle.Screens.User;
using Plugin.Connectivity;
using Xamarin.Forms;
using DrMuscle.Resx;
using Xamarin.Forms.PlatformConfiguration.AndroidSpecific;
using Xamarin.Forms.PlatformConfiguration;
using System.Threading.Tasks;

namespace DrMuscle.Controls
{
    public partial class ChatInputBarView : ContentView
    {
        public event EventHandler Tapped;

        public ChatInputBarView()
        {
            InitializeComponent();
            chatTextInput.BindingContext = this;
            
            RefreshLocalized();
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) => {
                RefreshLocalized();
            });
        }
        void RefreshLocalized()
        {
            //BtnSend.Text = AppResources.Send;
        }
        //public void Handle_Completed(object sender, EventArgs e)
        //{
            //if (Tapped != null)
            //{
            //    Tapped.Invoke(this, e);
            //}
            //chatTextInput.Focus();
        //}

        public async void UnFocusEntry()
        {
            chatTextInput?.Unfocus();
        }

        void Handle_Completed(object sender, System.EventArgs e)
        {
            if (Tapped != null)
            {
                Tapped.Invoke(this, e);
            }
            if (CrossConnectivity.Current.IsConnected)
                chatTextInput.Text = "";
            //chatTextInput.Focus();
        }

        public static readonly BindableProperty MessageTextProperty = BindableProperty.Create("MessageText", typeof(string), typeof(ChatInputBarView), string.Empty, BindingMode.TwoWay, null, (bindable, oldValue, newValue) =>
        {
            ((ChatInputBarView)bindable).SetMessageText();
        });

        private void SetMessageText()
        {
            this.MessageText = chatTextInput.Text;
            frmSendMessage.IsEnabled = MessageText.Length > 0 ;
        }

        public string MessageText
        {
            get { return (string)GetValue(MessageTextProperty); }

            set
            {
                SetValue(MessageTextProperty, value);
            }
        }

        void chatTextInput_TextChanged(System.Object sender, Xamarin.Forms.TextChangedEventArgs e)
        {
            if (!string.IsNullOrEmpty(chatTextInput.Text))
                    { 
                    if (chatTextInput.Text.Length == 1)
                    chatTextInput.Text = char.ToUpper(chatTextInput.Text[0]) + "";
            else if (chatTextInput.Text.Length > 1)
                    chatTextInput.Text = char.ToUpper(chatTextInput.Text[0]) + chatTextInput.Text.Substring(1);
            }
        }

        public void chatTextInput_Focused(object sender, FocusEventArgs e)
        {
            var screenHeight = App.ScreenHeight;
            if (Device.RuntimePlatform == Device.Android)
            {
                // Code specific to Android platform
                App.Current.On<Android>().UseWindowSoftInputModeAdjust(WindowSoftInputModeAdjust.Resize);
            }
            else if (Device.RuntimePlatform == Device.iOS)
            {
                // Code specific to iOS platform
                if(screenHeight <= 736)
                {
                    frameGrid.Margin = new Thickness(10, 10, 10, 10);
                }
                else
                    frameGrid.Margin = new Thickness(10, 10, 10, -20);


            }
            

            
        }

        public void chatTextInput_Unfocused(object sender, FocusEventArgs e)
        {
            if (Device.RuntimePlatform == Device.Android)
            {
                // Code specific to Android platform
                App.Current.On<Android>().UseWindowSoftInputModeAdjust(WindowSoftInputModeAdjust.Pan);
            }
            else if (Device.RuntimePlatform == Device.iOS)
            {
                // Code specific to iOS platform
                frameGrid.Margin = new Thickness(10, 10, 10, 20);
            }
            
        }
    }
}
