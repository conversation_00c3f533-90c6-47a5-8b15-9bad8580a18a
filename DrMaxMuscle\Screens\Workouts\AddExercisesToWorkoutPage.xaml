﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.Workouts.AddExercisesToWorkoutPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:DrMaxMuscle.Constants"
    xmlns:cells="clr-namespace:DrMaxMuscle.Cells"
    xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
    xmlns:control="clr-namespace:DrMaxMuscle.Controls"
    xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
    xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:local="clr-namespace:DrMaxMuscle"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    Title="AddExercisesToWorkoutPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="20,0,20,0">
        <StackLayout HorizontalOptions="FillAndExpand"
                     VerticalOptions="FillAndExpand">
            <Frame x:Name="SearchContainer"
                   Margin="8"
                   Padding="0"
                   BackgroundColor="#f1f1f1"
                   CornerRadius="4"
                   HasShadow="False"
                   IsClippedToBounds="true">
                <StackLayout Orientation="Horizontal"
                             Spacing="0">
                    <Image x:Name="ImgSearch"
                           Margin="10,0,2,0"
                           Aspect="AspectFit"
                           HeightRequest="18"
                           Source="icon_search_gray.png"
                           VerticalOptions="Center"
                           WidthRequest="15" />
                    <t:DrMuscleEntry x:Name="SearchEntry"
                                     Margin="0"
                                     BackgroundColor="Transparent"
                                     HeightRequest="40"
                                     HorizontalOptions="FillAndExpand"
                                     Keyboard="Plain"
                                     TextChanged="Handle_SearchTextChanged"
                                     TextColor="Black"
                                     Focused="SearchEntry_Focused"
                                     Unfocused="SearchEntry_Unfocused" />
                    <Label x:Name="BtnCancel"
                           Margin="0,0,7,0"
                           IsVisible="false"
                           VerticalOptions="FillAndExpand"
                           VerticalTextAlignment="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Tapped="Handle_CancelTapped" />
                        </Label.GestureRecognizers>
                    </Label>
                </StackLayout>
            </Frame>

            <t:DrMuscleListView x:Name="ExerciseListView"
                                ios:ListView.SeparatorStyle="FullWidth"
                                ios:ListView.GroupHeaderStyle="Grouped"
                                BackgroundColor="Transparent"
                                HasUnevenRows="True"
                                IsGroupingEnabled="True"
                                Scrolled="ExerciseListView_Scrolled"
                                SeparatorColor="#264457"
                                SeparatorVisibility="Default"
                                VerticalOptions="FillAndExpand">
                <t:DrMuscleListView.GroupHeaderTemplate>
                    <DataTemplate>
                        <ViewCell Height="70"
                                  ios:Cell.DefaultBackgroundColor="Transparent">
                              <StackLayout Orientation="Vertical" 
                                         BackgroundColor="Transparent"
                                         Padding="0">
                            <StackLayout Padding="0,5,0,5"
                                         effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"
                                         effects:TooltipEffect.HasShowTooltip="{Binding IsTooltipVisible}"
                                         effects:TooltipEffect.HasTooltip="True"
                                         effects:TooltipEffect.Position="Bottom"
                                         effects:TooltipEffect.Text="Tap to select other exercises"
                                         effects:TooltipEffect.TextColor="White"
                                         BackgroundColor="Transparent"
                                         HorizontalOptions="FillAndExpand"
                                         Orientation="Horizontal">
                                <ffimageloading:CachedImage Aspect="AspectFit"
                                                            ErrorPlaceholder="backgroundblack.png"
                                                            HeightRequest="60"
                                                            Source="{Binding Id, Converter={StaticResource IdToBodyConverter}}"
                                                            WidthRequest="45" />
                                <Label BackgroundColor="Transparent"
                                       HorizontalOptions="StartAndExpand"
                                       Style="{StaticResource BoldLabelStyle}"
                                       Text="{Binding Name}"
                                       TextColor="{x:Static constnats:AppThemeConstants.OffBlackColor}"
                                       VerticalOptions="Center"
                                       VerticalTextAlignment="Center" />
                                <Image x:Name="StateImage"
                                       Aspect="AspectFit"
                                       BackgroundColor="Transparent"
                                       HeightRequest="32"
                                       HorizontalOptions="End"
                                       PropertyChanged="StateImage_PropertyChanged"
                                       Source="{Binding StateIcon}"
                                       VerticalOptions="CenterAndExpand"
                                       WidthRequest="25" />

                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer CommandParameter="{Binding .}"
                                                          NumberOfTapsRequired="1"
                                                          Tapped="Section_Tapped" />
                                </StackLayout.GestureRecognizers>

                            </StackLayout>
                                    <BoxView HeightRequest="1"
                                         HorizontalOptions="FillAndExpand"
                                         VerticalOptions="End"
                                         BackgroundColor="#264457">       
                                </BoxView>
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </t:DrMuscleListView.GroupHeaderTemplate>
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell Height="44"
                                  BindingContextChanged="OnBindingContextChanged">
                            <StackLayout Padding="0,0,0,0"
                                         BackgroundColor="Transparent"
                                         Orientation="Horizontal">
                                <!--<StackLayout HorizontalOptions="StartAndExpand" Orientation="Horizontal">-->
                                <Label HorizontalOptions="FillAndExpand"
                                       Style="{StaticResource LabelStyle}"
                                       Text="{Binding Label}"
                                       VerticalTextAlignment="Center"
                                       LineBreakMode="WordWrap"
                                       MaxLines="3"
                                       Margin="0,0,10,0" />
                                <!--</StackLayout>
                            <StackLayout HorizontalOptions="EndAndExpand" Orientation="Horizontal">-->
                                <Switch x:Name="ToggleSwitch"
                                        HorizontalOptions="End"
                                        IsToggled="{Binding IsSelected, Mode=TwoWay}"
                                        Toggled="Handle_Toggled"
                                        VerticalOptions="Center"
                                        WidthRequest="50"
                                        Margin="0,0,5,0"/>
                                <!--</StackLayout>-->

                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <t:DrMuscleListView.Footer>
                    <StackLayout HeightRequest="180"
                                 BackgroundColor="Transparent" />
                </t:DrMuscleListView.Footer>
            </t:DrMuscleListView>
            <!--<StackLayout x:Name="StackNext"
                         Grid.Row="1"
                         Padding="0,0,0,20"
                         effects:TooltipEffect.BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"
                         effects:TooltipEffect.HasShowTooltip="{Binding IsTooltipVisible}"
                         effects:TooltipEffect.HasTooltip="True"
                         effects:TooltipEffect.Position="Top"
                         effects:TooltipEffect.Text="Tap to Continue"
                         effects:TooltipEffect.TextColor="White"
                         Orientation="Horizontal"
                         VerticalOptions="End">
                <t:DrMuscleButton x:Name="NextButton"
                                  BorderColor="#195377"
                                  HeightRequest="58"
                                  HorizontalOptions="FillAndExpand"
                                  TextTransform="Uppercase"
                                  FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}"
                                  Style="{StaticResource highEmphasisButtonStyle}" />


            </StackLayout>-->
        </StackLayout>
        <StackLayout Orientation="Vertical"
                     VerticalOptions="End"
                     HorizontalOptions="FillAndExpand">
            <Frame Padding="0"
                   CornerRadius="6"
                   VerticalOptions="End"
                   HeightRequest="68"
                   Margin="0,0,0,10"
                   BorderColor="Transparent"
                   BackgroundColor="White"
                   HorizontalOptions="FillAndExpand">
                <t:DrMuscleButton BorderColor="#195377"
                                  HeightRequest="68"
                                  CornerRadius="6"
                                  Text="CREATE CUSTOM EXERCISE"
                                  HorizontalOptions="FillAndExpand"
                                  Style="{StaticResource buttonStyle}"
                                  Clicked="NewExerciseTapped" />
            </Frame>
            <Frame x:Name="StackNext"
                   Padding="0"
                   Margin="0,0,0,20"
                   IsClippedToBounds="true"
                   CornerRadius="6"
                   VerticalOptions="End"
                   HorizontalOptions="FillAndExpand"
                   HeightRequest="68"
                   BorderColor="Transparent"
                   Style="{StaticResource GradientStackStyleBlue}">
                <t:DrMuscleButton x:Name="NextButton"
                                  CornerRadius="6"
                                  TextTransform="Uppercase"
                                  VerticalOptions="Fill"
                                  HeightRequest="68"
                                  FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}"
                                  HorizontalOptions="FillAndExpand"
                                  Text="CREATE CUSTOM EXERCISE"
                                  Style="{StaticResource highEmphasisButtonStyle}"
                                  BackgroundColor="Transparent"
                                  BorderColor="Transparent"
                                  TextColor="White" />
            </Frame>
        </StackLayout>
    </Grid>
</ContentPage>
