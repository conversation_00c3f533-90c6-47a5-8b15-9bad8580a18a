<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Cells.NewRecordCell">
    <controls:CustomFrame
    x:Name="WeightProgress2"
    Margin="10,11,10,10"
    Padding="10,10,10,10"
    CornerRadius="12"
    BorderColor="Transparent"
    HasShadow="true">
    <controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />      
</controls:CustomFrame.Shadow>
        <controls:CustomFrame.Triggers>
            <DataTrigger
            Binding="{Binding IsNewRecordAvailable}"
            Value="True"
            TargetType="Frame">
                <Setter
                Property="Margin"
                Value="10,1,10,10" />
            </DataTrigger>
            <DataTrigger
            Binding="{Binding IsNewRecordAvailable}"
            Value="False"
            TargetType="Frame">
                <Setter
                Property="Margin"
                Value="10,11,10,10" />
            </DataTrigger>
        </controls:CustomFrame.Triggers>
        <StackLayout
        Spacing="0">
            <Label
            x:Name="LblAnswer"
            Text="{Binding Question}"
            IsVisible="true"
            FontAttributes="Bold"
FontSize="20"
            Style="{StaticResource LabelStyle}"
            TextColor="Black"
            Padding="10,10,0,5"
            Margin="0" />
            <StackLayout
            x:Name="SLWorkoutStats"
            BackgroundColor="White"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center">
                <Grid
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                Padding="10,15,10,15">
                    <Grid.RowDefinitions>
                        <RowDefinition
                        Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition
                        Width="*" />
                        <ColumnDefinition
                        Width="*" />
                        <ColumnDefinition
                        Width="*" />
                    </Grid.ColumnDefinitions>
                    <StackLayout
                    Grid.Column="0"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        Source="records.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        x:Name="lblResult3"
                        Text="{Binding RecordCount}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontAttributes="Bold"
FontSize="17"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />
                        <Label
                        x:Name="lblResult33"
                        Text="New records"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                            <Label.Triggers>
                                <DataTrigger
                                TargetType="Label"
                                Binding="{Binding RecordCount}"
                                Value="1">
                                    <Setter
                                    Property="Text"
                                    Value="New record" />
                                </DataTrigger>
                            </Label.Triggers>
                        </Label>
                    </StackLayout>
                    <StackLayout
                    Grid.Column="1"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        Source="fire.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        Text="{Binding CaloriesBurned}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        Style="{StaticResource LabelStyle}"
                        FontAttributes="Bold"
FontSize="17"
                        TextColor="Black" />
                        <Label
                        Text="Calories"
                        HorizontalTextAlignment="Center"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                        </Label>
                    </StackLayout>
                    <StackLayout
                    Grid.Column="2"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        x:Name="IconResultImage"
                        Source="chain.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        x:Name="lblResult4"
                        Text="{Binding ChainCount}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontAttributes="Bold"
FontSize="17"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />
                        <Label
                        x:Name="lblResult44"
                        Text="Weeks streak"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                            <Label.Triggers>
                                <DataTrigger
                                TargetType="Label"
                                Binding="{Binding ChainCount}"
                                Value="1">
                                    <Setter
                                    Property="Text"
                                    Value="Week streak" />
                                </DataTrigger>
                            </Label.Triggers>
                        </Label>
                    </StackLayout>
                </Grid>
                <Grid
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                Padding="10,15,10,15">
                    <Grid.RowDefinitions>
                        <RowDefinition
                        Height="*" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition
                        Width="*" />
                        <ColumnDefinition
                        Width="*" />
                        <ColumnDefinition
                        Width="*" />
                    </Grid.ColumnDefinitions>
                    <StackLayout
                    Grid.Column="0"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        Source="clock.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        Text="{Binding MinuteCount}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontAttributes="Bold"
FontSize="17"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />
                        <Label
                        Text="Minutes"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                            <Label.Triggers>
                                <DataTrigger
                                TargetType="Label"
                                Binding="{Binding MinuteCount}"
                                Value="1">
                                    <Setter
                                    Property="Text"
                                    Value="Minute" />
                                </DataTrigger>
                            </Label.Triggers>
                        </Label>
                    </StackLayout>
                    <StackLayout
                    Grid.Column="1"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        Source="exercise.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        Text="{Binding ExerciseCount}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        Style="{StaticResource LabelStyle}"
                        FontAttributes="Bold"
FontSize="17"
                        TextColor="Black" />
                        <Label
                        x:Name="lblResult211"
                        Text="Exercises"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                            <Label.Triggers>
                                <DataTrigger
                                TargetType="Label"
                                Binding="{Binding ExerciseCount}"
                                Value="1">
                                    <Setter
                                    Property="Text"
                                    Value="Exercise" />
                                </DataTrigger>
                            </Label.Triggers>
                        </Label>
                    </StackLayout>
                    <StackLayout
                    Grid.Column="2"
                    HorizontalOptions="FillAndExpand">
                        <Image
                        Source="workoutnow.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                        <Label
                        Text="{Binding WorksetCount}"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontAttributes="Bold"
FontSize="17"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />
                        <Label
                        Text="Work sets"
                        IsVisible="true"
                        HorizontalOptions="Center"
                        FontSize="17"
                        TextColor="#AA000000">
                            <Label.Triggers>
                                <DataTrigger
                                TargetType="Label"
                                Binding="{Binding WorksetCount}"
                                Value="1">
                                    <Setter
                                    Property="Text"
                                    Value="Work set" />
                                </DataTrigger>
                            </Label.Triggers>
                        </Label>
                    </StackLayout>
                </Grid>
            </StackLayout>

            <!--Buttons Grid-->
            <Grid
            HorizontalOptions="FillAndExpand"
            Margin="1,20,1,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!--AI CHAT Button-->
                <t:DrMuscleButton
                x:Name="BtnAIChat"
                Grid.Column="0"
                Text="AI ANALYSIS"
                FontSize="13"
                HeightRequest="45"
                FontAttributes="Bold"
                VerticalOptions="Center"
                HorizontalOptions="FillAndExpand"
                Style="{StaticResource buttonLinkStyle}"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                Clicked="BtnAIChat_Clicked"/>

                <!--Share Button-->
                <Frame
                    Grid.Column="1"
                    Padding="0"
                    Margin="0"
                    IsClippedToBounds="true"
                    CornerRadius="6"
                    VerticalOptions="Center"
                    IsVisible="true"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="45">
                    <StackLayout
                        Padding="0"
                        HeightRequest="45"
                        Margin="0"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="FillAndExpand"
                        >
                        <StackLayout.Background>
                            <LinearGradientBrush EndPoint="1,0">
                                <GradientStop Color="#0C2432" Offset="0.0" />
                                <GradientStop Color="#195276" Offset="1.0" />
                            </LinearGradientBrush>
                        </StackLayout.Background>
                        <t:DrMuscleButton
                            VerticalOptions="Center"
                            HeightRequest="45"
                            FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                            CornerRadius="6"
                            x:Name="BtnShareApp"
                            HorizontalOptions="FillAndExpand"
                            IsVisible="true"
                            Style="{StaticResource highEmphasisButtonStyle}"
                            BackgroundColor="Transparent"
                            BorderColor="Transparent"
                            TextColor="White"
                            TextTransform="Uppercase"
                            Text="SHARE"
                            Clicked="BtnShareApp_Clicked" />
                    </StackLayout>
                </Frame>
                
            </Grid>
        </StackLayout>
    </controls:CustomFrame>
</ContentView>
