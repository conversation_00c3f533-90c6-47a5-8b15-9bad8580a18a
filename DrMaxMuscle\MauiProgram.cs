﻿using CommunityToolkit.Maui;
using Controls.UserDialogs.Maui;
using DrMaxMuscle.Controls;
using DrMaxMuscle.Dependencies;
#if ANDROID
using Plugin.Firebase.Bundled.Platforms.Android;
using DrMaxMuscle.Platforms.Android.Dependencies;
using DrMaxMuscle.Platforms.Android.Renderers;
// using Plugin.GoogleClient.MAUI;
//using Plugin.Firebase.Bundled.Platforms.Android;
#elif IOS
using DrMaxMuscle.Platforms.iOS.Renderer;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using WebKit;
#endif
using FFImageLoading.Maui;
using Microcharts.Maui;
using Microsoft.Extensions.Logging;
using OxyPlot.Maui.Skia;
using RGPopup.Maui.Extensions;
using SkiaSharp.Views.Maui.Controls.Hosting;
using DrMaxMuscle.Views;
using Microsoft.AppCenter;
using Microsoft.AppCenter.Crashes;
using Rollbar;
using Microsoft.Maui.LifecycleEvents;
using RGPopup.Maui.Services;
using System.Diagnostics;
using System.Text.Json;
using System.Threading.Tasks;
using RGPopup.Maui.Contracts;
using Plugin.Firebase.Auth.Google;
using Plugin.Firebase.Bundled.Shared;

namespace DrMaxMuscle
{
    public static class MauiProgram
    {
        public static IServiceProvider ServiceProvider { get; private set; }

        public static MauiApp CreateMauiApp()
        {
            try
            {
                var builder = MauiApp.CreateBuilder();
                builder
                    .UseMauiApp<App>()
                    .UseMicrocharts()
                    .UseMauiCommunityToolkit()
                    .UseUserDialogs()
                    .UseFFImageLoading()
                    .UseMicrocharts()
                    .UseSkiaSharp()
                    .UseOxyPlotSkia()
                    // #if ANDROID 
                    // .UseGoogleLogin()
                    // #endif
                    .UseMauiRGPopup(config =>
                    {
                        config.BackPressHandler = null;
                        config.FixKeyboardOverlap = true;
                    })
                    .ConfigureEffects(effects =>
                    {
#if ANDROID
                        effects.Add<DrMaxMuscle.Effects.ViewShadowEffect, DrMaxMuscle.Platforms.Android.Effects.DropShadowEffect>();
                        effects.Add<Effects.Sorting.ListViewSortableEffect, DrMaxMuscle.Platforms.Android.Effects.ListViewSortableEffect>();
#else
                    effects.Add<DrMaxMuscle.Effects.ViewShadowEffect, DrMaxMuscle.Platforms.iOS.Effects.DropShadowEffect>();
                    effects.Add<Effects.Sorting.ListViewSortableEffect, DrMaxMuscle.Platforms.iOS.Effects.ListViewSortableEffect>();
#endif
                    })
                    .UseSentry(options =>
                    {
                        // The DSN is the only required setting.
                        options.Dsn = "https://<EMAIL>/4507696371400704";

                        // Use debug mode if you want to see what the SDK is doing.
                        // Debug messages are written to stdout with Console.Writeline,
                        // and are viewable in your IDE's debug console or with 'adb logcat', etc.
                        // This option is not recommended when deploying your application.
                        options.Debug = false;
                        // This option is recommended. It enables Sentry's "Release Health" feature.
                        //options.AutoSessionTracking = true;

                        // Set TracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
                        // We recommend adjusting this value in production.
                        options.TracesSampleRate = 0.0;
                        options.AttachScreenshot = true;
                        //options.AttachStacktrace = true;
                        // Other Sentry options can be set here.
                    })
                                    .ConfigureLifecycleEvents(events =>
                {
#if IOS
                    {
                        //events.AddiOS(iOS =>
                        //{
                        //    try
                        //    {
                        //        // Register RGPopup or any other plugins safely
                        //        RGPopup.Maui.IOS.Popup.Init();
                        //    }
                        //    catch (global::System.Exception ex)
                        //    {
                        //    }
                        //});

                        // events.AddiOS(iOS => iOS.FinishedLaunching((App, launchOptions) =>
                        // {
                        //     Task.Delay(1000).Wait();
                        //     CrossFirebase.Initialize(CreateCrossFirebaseSettings());
                        //     return true;
                        // }));
                    }
#else
                    {
                        events.AddAndroid(android => android.OnCreate((activity, _) =>
                        CrossFirebase.Initialize(activity, CreateCrossFirebaseSettings())));
                        FirebaseAuthGoogleImplementation.Initialize("707210235326-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com");
                    }
#endif
                })

                     .ConfigureMauiHandlers((handlers) =>
                     {
#if ANDROID

                         handlers.AddHandler(typeof(ContextMenuButton), typeof(ContextMenuButtonRenderer));
                         handlers.AddHandler(typeof(CustomFrame), typeof(CustomFrameShadowRenderer));
                         handlers.AddHandler(typeof(PickerView), typeof(PickerViewRenderer));
                         handlers.AddHandler(typeof(DropDownPicker), typeof(DropDownPickerRenderer));
                         //handlers.AddHandler(typeof(HyperlinkLabel), typeof(HyperlinkLabelRenderer));
                         handlers.AddHandler(typeof(ExtendedLabel), typeof(ExtendedLabelRenderer));
                         handlers.AddHandler(typeof(ExtendedLabelLink), typeof(ExtendedLabelRenderer));
                         handlers.AddHandler(typeof(ExtendedLightBlueLabel), typeof(ExtendedLightBlueLabelRender));
                         handlers.AddHandler(typeof(Picker), typeof(CustomPickerRenderer));
                         handlers.AddHandler(typeof(CollectionView), typeof(CustomCollectionViewHandler));

#elif IOS
                     handlers.AddHandler(typeof(ContextMenuButton), typeof(ContextMenuButtonRenderer));
                     handlers.AddHandler(typeof(CustomFrame), typeof(CustomFrameShadowRenderer));
                     handlers.AddHandler(typeof(PickerView), typeof(PickerViewRenderer));
                     handlers.AddHandler(typeof(DropDownPicker), typeof(DropDownPickerViewRenderer));
                     handlers.AddHandler(typeof(ExtendedLabel), typeof(ExtendedLabelRenderer));
                     handlers.AddHandler(typeof(ExtendedLabelLink), typeof(ExtendedLabelRenderer));
                     handlers.AddHandler(typeof(ExtendedLightBlueLabel), typeof(ExtendedLightBlueLabelRender));
                     handlers.AddHandler(typeof(CollectionView), typeof(CustomCollectionViewHandler));
                     //handlers.AddHandler(typeof(HyperlinkLabel), typeof(HyperlinkLabelHandler));
#endif
                     });
                //.ConfigureFonts(fonts =>
                //{
                //    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                //    //fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                //});
                //           .ConfigureMauiHandlers((handlers) =>
                //             {
                //#if ANDROID   
                //                 handlers.AddHandler(typeof(MainTabbedPage), typeof(DrMaxMuscle.Platforms.Android.Renderers.GradientTabbedPageRenderer));
                //#endif
                //             });
                // Register ISQLite implementation based on platform
#if ANDROID
                builder.Services.AddSingleton<ISQLite, SQLite_Android>();
                builder.Services.AddSingleton<IStyles, Styles_Droid>();
                builder.Services.AddSingleton<IFirebase, Firebase_Droid>();
                builder.Services.AddSingleton<IDrMuscleSubcription, DrMuscleSubscription_Droid>();
                builder.Services.AddSingleton<IVersionInfoService, VersionInfoService>();
                builder.Services.AddSingleton<CustomPickerRenderer>();
#elif IOS
            builder.Services.AddSingleton<ISQLite, SQLite_iOS>();
                //builder.Services.AddSingleton<IStyles, Styles_iOS>();
                builder.Services.AddSingleton<IFirebase, Firebase_iOS>();
                builder.Services.AddSingleton<IDrMuscleSubcription, DrMuscleSubscription_iOS>();
                builder.Services.AddSingleton<IVersionInfoService, VersionInfoService>();
#endif
                builder.Services.AddSingleton<LocalDBManager>();
#if DEBUG
                builder.Logging.AddDebug();
#endif

#if ANDROID
                DependencyService.Register<IStyles, Styles_Droid>();
                DependencyService.Register<IAudio, AudioService>();
                DependencyService.Register<IKeyboardService, KeyboardService>();
                DependencyService.Register<IKeyboardHelper, DroidKeyboardHelper>();
                DependencyService.Register<ILocalize, DrMaxMuscle.Platforms.Android.Dependencies.Localize>();
                DependencyService.Register<IShareService, DroidShareService>();
                DependencyService.Register<IFirebase, Firebase_Droid>();
                DependencyService.Register<IKillAppService, KillAppService>();
                DependencyService.Register<INotificationsInterface, NotificationsInterface>();
                DependencyService.Register<IAppSettingsHelper, AppSettingsInterface>();
                DependencyService.Register<IAlarmAndNotificationService, DrMaxMuscle.Platforms.Android.Dependencies.AlarmAndNotificationService>();
                DependencyService.Register<IDrMuscleSubcription, DrMuscleSubscription_Droid>();
#elif IOS

            DependencyService.Register<IStyles, Styles_iOS>();
            DependencyService.Register<IFirebase, Firebase_iOS>();
            DependencyService.Register<ILocalize, DrMaxMuscle.Platforms.iOS.Dependencies.Localize>();
            DependencyService.Register<IKeyboardHelper, KeyboardHelpers>();
            DependencyService.Register<IAlarmAndNotificationService, DrMaxMuscle.Platforms.iOS.Dependencies.AlarmAndNotificationService>();
            DependencyService.Register<IShareService, iOSShareService>();
            DependencyService.Register<IOrientationService, OrientationService>();
            DependencyService.Register<IHealthData, HealthData>();
            DependencyService.Register<IAppleSignInService, AppleSignInService>();
            DependencyService.Register<INotificationsInterface, NotificationsInterface>();
            DependencyService.Register<IAppSettingsHelper, AppSettingsInterface>();
            DependencyService.Register<IDrMuscleSubcription, DrMuscleSubscription_iOS>();

            DependencyService.Register<IKillAppService, KillAppService>();
            Microsoft.Maui.Handlers.WebViewHandler.PlatformViewFactory = (handler) =>
{
try 
	{	        
		var config = Microsoft.Maui.Platform.MauiWKWebView.CreateConfiguration();
    
config.AllowsAirPlayForMediaPlayback = false;
config.AllowsInlineMediaPlayback = false;
config.AllowsPictureInPictureMediaPlayback = false;
    config.MediaTypesRequiringUserActionForPlayback = WKAudiovisualMediaTypes.All;

var wv = new Microsoft.Maui.Platform.MauiWKWebView(
	CoreGraphics.CGRect.Empty,
	handler as Microsoft.Maui.Handlers.WebViewHandler,
	config);
    
return wv;
	}
	catch (global::System.Exception ex)
	{
        return null;
	}
};

#endif
                //  RollbarHelper.ConfigureRollbar();
                //  RollbarHelper.RegisterForGlobalExceptionHandling();

                //  RollbarLocator.RollbarInstance
                //.Info("Xamarin.Forms sample: Hello world! Xamarin is here @MainActivity.OnCreate(...) ...");

                //  //subscribe to all known unhandled exception events application-wide
                //  RollbarHelper.RegisterForGlobalExceptionHandling();

                var app = builder.Build();
                ServiceProvider = app.Services;

                return builder.Build();
            }
            catch (System.Exception ex)
            {
                OnException(ex);
                throw;
            }
        }
        private static async Task OnException(System.Exception e)
        {
            if (e == null)
                return;
            var exceptionDetails = new
            {
                Message = e.Message,
                StackTrace = e.StackTrace,
                Source = e.Source,
                InnerException = e.InnerException?.Message
            };

            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                    IgnoreReadOnlyProperties = true
                };
                var serializedException = JsonSerializer.Serialize(exceptionDetails, options);
                Console.WriteLine("ON UNOBSERVED TASK EXCEPTION, App.xaml.cs");
                RollbarLocator.RollbarInstance.Critical(e);
                SentrySdk.CaptureException(new System.Exception(serializedException));
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
            catch (System.Exception ex)
            {
                // Log the serialization failure, but don't let it crash the application.
                Console.WriteLine($"Serialization failed: {ex.Message}");
                RollbarLocator.RollbarInstance.Error(ex);
                SentrySdk.CaptureException(ex);
                SentrySdk.FlushAsync(TimeSpan.FromSeconds(5)).GetAwaiter().GetResult();
            }
        }

        private static CrossFirebaseSettings CreateCrossFirebaseSettings()
        {
           return new CrossFirebaseSettings(isAuthEnabled: true, isCloudMessagingEnabled: true, isCrashlyticsEnabled: false, isAnalyticsEnabled: true);
           //return new CrossFirebaseSettings(isAuthEnabled: true, isCloudMessagingEnabled: true, isCrashlyticsEnabled: true, isAnalyticsEnabled: true);
        }


        private static readonly SemaphoreSlim _popupLock = new(1, 1);
        private static int _isDismissingAllFlag = 0;
        private static int _isDismissingFlag = 0;
        private static DateTime _lastDismissTimestamp = DateTime.MinValue;
        private static readonly TimeSpan _debounceThreshold = TimeSpan.FromMilliseconds(500);

        private static async Task<bool> IsPlatformViewReadyAsync(int maxWaitMs = 1500)
        {
            int waited = 0;
            while (Application.Current?.MainPage?.Handler?.PlatformView == null && waited < maxWaitMs)
            {
                await Task.Delay(100);
                waited += 100;
            }
            return Application.Current?.MainPage?.Handler?.PlatformView != null;
        }

        //public static async Task SafeDismissAllPopups()
        //{
        //    bool IsAbort = false;
        //    Debug.WriteLine($"[SafeDismissAllPopups] Called at {DateTime.UtcNow:HH:mm:ss.fff}, flag: {_isDismissingAllFlag}");

        //    if ((DateTime.UtcNow - _lastDismissTimestamp) < _debounceThreshold)
        //    {
        //        Debug.WriteLine("[SafeDismissAllPopups] Skipped due to debounce.");
        //        return;
        //    }

        //    if (Interlocked.CompareExchange(ref _isDismissingAllFlag, 1, 0) != 0)
        //    {
        //        Debug.WriteLine("[SafeDismissAllPopups] Skipped: tap lock active.");
        //        IsAbort = true;
        //        return;
        //    }

        //    var lockTaken = false;

        //    try
        //    {
        //        await _popupLock.WaitAsync();
        //        lockTaken = true;

        //        var instance = PopupNavigation.Instance;
        //        if (instance == null || instance.PopupStack.Count == 0)
        //        {
        //            Debug.WriteLine("[SafeDismissAllPopups] No popups to dismiss.");
        //            return;
        //        }

        //        // Wait until PlatformView is ready (prevent crash)
        //        if (!await IsPlatformViewReadyAsync())
        //        {
        //            Debug.WriteLine("[SafeDismissAllPopups] PlatformView still null. Aborting dismiss.");
        //            return;
        //        }

        //        await MainThread.InvokeOnMainThreadAsync(async () =>
        //        {
        //            try
        //            {
        //                if (instance.PopupStack.Count > 0)
        //                {
        //                    Debug.WriteLine("[SafeDismissAllPopups] Dismissing all popups...");
        //                    await instance.PopAllAsync(false);
        //                    Debug.WriteLine("[SafeDismissAllPopups] All popups dismissed.");
        //                    IsAbort = false;
        //                }
        //            }
        //            catch (System.Exception ex)
        //            {
        //                Debug.WriteLine($"[SafeDismissAllPopups] Exception during PopAllAsync: {ex.Message}");
        //            }
        //        });

        //        _lastDismissTimestamp = DateTime.UtcNow;
        //    }
        //    catch (System.Exception ex)
        //    {
        //        Debug.WriteLine($"[SafeDismissAllPopups] Outer catch: {ex.Message}");
        //    }
        //    finally
        //    {
        //        if (!IsAbort)
        //            Interlocked.Exchange(ref _isDismissingAllFlag, 0);

        //        if (lockTaken)
        //        {
        //            try
        //            {
        //                _popupLock.Release();
        //            }
        //            catch (SemaphoreFullException ex)
        //            {
        //                Debug.WriteLine($"[SafeDismissAllPopups] Semaphore release error: {ex.Message}");
        //            }
        //        }

        //        Debug.WriteLine("[SafeDismissAllPopups] Finished.");
        //    }
        //}


        private static bool _isDismissingPopup = false;
        private static bool _isDismissingTopPopup = false;
        //private static readonly SemaphoreSlim _popupLock = new(1, 1);
        public static async Task SafeDismissAllPopups()
        {
            System.Diagnostics.Debug.WriteLine("SafeDismissAllPopups Called");
            if (_isDismissingPopup)
            {
                Debug.WriteLine("Dismiss already in progress. Skipping...");
                return;
            }
            _isDismissingPopup = true;
            await _popupLock.WaitAsync();
            try
            {
                System.Diagnostics.Debug.WriteLine("SafeDismissAllPopups try Called");
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        await Task.Delay(100); // Allow animations or transitions to settle
                        var popupStack = PopupNavigation.Instance.PopupStack;
                        if (popupStack?.Any() == true)
                        {
                            // Iterate and safely remove popups one by one
                            foreach (var popup in popupStack.ToList())
                            {
                                if (popup == null)
                                    continue;
                                try
                                {
                                    // Ensure popup is still part of the visual tree and not already disposed
                                    var platformViewExists = popup.Handler?.PlatformView != null;
                                    if (popup.Parent != null && platformViewExists)
                                    {
                                        await PopupNavigation.Instance.RemovePageAsync(popup, true);
                                        Debug.WriteLine("Popup safely removed.");
                                    }
                                    else
                                    {
                                        Debug.WriteLine("Popup skipped: already removed or disposed.");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.WriteLine($"Error removing individual popup: {ex.Message}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Popup dismissal error inside main thread: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"SafeDismissAllPopups failed outside main thread: {ex.Message}");
            }
            finally
            {
                _isDismissingPopup = false;
                _popupLock.Release();
            }
        }

        public static async Task SafeDismissTopPopup()
        {
            Debug.WriteLine("SafeDismissTopPopup Called");
            if (_isDismissingTopPopup)
            {
                Debug.WriteLine("Dismiss already in progress. Skipping...");
                return;
            }
            _isDismissingTopPopup = true;
            await _popupLock.WaitAsync(); // Prevents concurrent access
            try
            {
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        if (PopupNavigation.Instance.PopupStack?.Any() != true)
                        {
                            Debug.WriteLine("No popups found in the stack.");
                            return;
                        }
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup == null)
                        {
                            Debug.WriteLine("Top popup already removed or null.");
                            return;
                        }
                        await Task.Delay(100); // Optional delay to let UI settle
                        if (topPopup != null &&
                            topPopup.Handler != null &&
                            topPopup.Handler.PlatformView != null && // Check native view exists
                            topPopup.Parent != null && // Check it's still in the visual tree
                            PopupNavigation.Instance.PopupStack.Contains(topPopup))
                        {
                            Debug.WriteLine($"Attempting to dismiss top popup: {topPopup.GetType().Name}");
                            await PopupNavigation.Instance.RemovePageAsync(topPopup, false);
                            Debug.WriteLine("Top popup dismissed successfully.");
                        }
                        else
                        {
                            Debug.WriteLine("Top popup was already removed or has no valid handler.");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error while dismissing top popup: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Outer error in SafeDismissTopPopup: {ex.Message}");
            }
            finally
            {
                _isDismissingTopPopup = false;

                try
                {
                    _popupLock.Release();
                }
                catch (SemaphoreFullException)
                {
                    Debug.WriteLine("Semaphore already released.");
                }
            }
        }


        //public static async Task SafeDismissTopPopup()
        //{
        //    if (Interlocked.CompareExchange(ref _isDismissingFlag, 1, 0) != 0)
        //    {
        //        Debug.WriteLine("SafeDismissTopPopup: Already running.");
        //        return;
        //    }

        //    await _popupLock.WaitAsync();


        //    try
        //    {
        //        Debug.WriteLine($"SafeDismissTopPopup called at {DateTime.Now}, flag: {_isDismissingFlag}");

        //        if (!await IsPlatformViewReadyAsync())
        //        {
        //            Debug.WriteLine("SafeDismissTopPopup: PlatformView not ready. Aborting dismiss.");
        //            return;
        //        }

        //        var popupStack = PopupNavigation.Instance.PopupStack.LastOrDefault();


        //        await MainThread.InvokeOnMainThreadAsync(async () =>
        //        {
        //            if (popupStack?.Handler?.PlatformView != null)
        //            {
        //                Debug.WriteLine("Dismissing top popup...");

        //                await PopupNavigation.Instance.RemovePageAsync(popupStack, true);

        //                //await PopupNavigation.Instance.PopAsync();
        //            }
        //        });
        //    }
        //    catch (System.Exception ex)
        //    {
        //        Debug.WriteLine($"SafeDismissTopPopup EXCEPTION: {ex}");
        //    }
        //    finally
        //    {
        //        Interlocked.Exchange(ref _isDismissingFlag, 0);
        //        _popupLock.Release();
        //    }
        //}


    }
}