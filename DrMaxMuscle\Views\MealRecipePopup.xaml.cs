using Acr.UserDialogs;
using RGPopup.Maui.Pages;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Services;
using Microsoft.Maui.Controls;
using Rollbar.DTOs;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui.Core;

namespace DrMaxMuscle.Views;

public partial class MealRecipePopup : Popup
{
    private AppTheme _previousTheme;
    public MealRecipePopup(string image, string title, string body, string btnText)
    {
        InitializeComponent();
        try
        {
            _previousTheme = Application.Current.UserAppTheme;
            var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
            MainFrame.WidthRequest = screenWidth * 0.9; // Assuming the Frame has x:Name="MainFrame"
            Application.Current.UserAppTheme = AppTheme.Light;
            //ImgName.Source = image;
            LblHeading.Text = title;
            //LblSubHead.Text = body;
            OkButton.Text = btnText;
            CreateDynamicLabels(body);
            this.Opened += Popup_Opened;
            this.Closed += Popup_Closed;
        }
        catch (System.Exception ex)
        {
            //ImgName.Source = image;
            LblHeading.Text = title;
            //LblSubHead.Text = body;
            OkButton.Text = btnText;
            CreateDynamicLabels(body);
        }
    }

    private void Popup_Closed(object sender, PopupClosedEventArgs e)
    {
        try
        {
            App.IsMealPlanReceipeOpened = false;
            Application.Current.UserAppTheme = _previousTheme;
        }
        catch (System.Exception ex)
        {

        }
    }
    private void Popup_Opened(object sender, PopupOpenedEventArgs e)
    {
        try
        {
            App.IsMealPlanReceipeOpened = true;
        }
        catch (System.Exception ex)
        {

        }
    }

    public void OnBefore(string keyName)
    {
        try
        {
            var bodyText = LocalDBManager.Instance.GetDBSetting(keyName)?.Value;
            if (!string.IsNullOrEmpty(bodyText))
            {
                //LblSubHead.HorizontalOptions = LayoutOptions.StartAndExpand;
                CreateDynamicLabels(bodyText);
            }
        }
        catch (System.Exception ex)
        {

        }
    }

    void BackToPlan_Clicked(System.Object sender, System.EventArgs e)
    {
        this.CloseAsync();
    }

    private void CreateDynamicLabels(string body)
    {
        try
        {
            if(body == "Loading...")
            {
                mainGrid.Children.Clear();
                mainGrid.RowDefinitions.Clear();
                int row = 0;
                var label = new Label
                {
                    Text = body,
                    FontSize = 17,
                    LineHeight = DeviceInfo.Platform == DevicePlatform.Android ? 1 : 1.2,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                };

                // Add a new row to the grid
                mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                // Add the label to the grid
                mainGrid.Children.Add(label);

                // Specify the row and column for the label
                Grid.SetRow(label, row);
                Grid.SetColumn(label, 0); // Column 0, as there's no multi-column setup in this case

            }
            else
            {
                var splitData = body.Split("\n");
                mainGrid.Children.Clear();
                mainGrid.RowDefinitions.Clear();
                int row = 0;
                foreach (string line in splitData)
                {
                    // Clean and format the data
                    var data = line.Replace("...", "").Trim();

                    // Skip empty lines (optional)
                    if (string.IsNullOrWhiteSpace(data))
                        continue;

                    // Create a new Label for each line of data
                    var label = new Label
                    {
                        Text = data,
                        FontSize = 17,
                        LineHeight = DeviceInfo.Platform == DevicePlatform.Android ? 1 : 1.2,
                        HorizontalOptions = LayoutOptions.Start,
                        VerticalOptions = LayoutOptions.Center
                    };

                    // Add a new row to the grid
                    mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                    // Add the label to the grid
                    mainGrid.Children.Add(label);

                    // Specify the row and column for the label
                    Grid.SetRow(label, row);
                    Grid.SetColumn(label, 0); // Column 0, as there's no multi-column setup in this case

                    // Increment the row index for the next label
                    row++;
                }
                if (DeviceInfo.Platform == DevicePlatform.iOS)
                {
                    MainFrame.IsVisible = false;
                    MainFrame.IsVisible = true;
                }
            }
            
        }
        catch (System.Exception ex)
        {

        }

    }
}
