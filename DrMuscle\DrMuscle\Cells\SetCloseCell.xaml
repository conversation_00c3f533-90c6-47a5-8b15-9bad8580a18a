﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    x:Class="DrMuscle.Cells.SetCloseCell"
    xmlns:behaviours="clr-namespace:DrMuscle.Behaviors"
    xmlns:converters="clr-namespace:DrMuscle.Converters"
    xmlns:app="clr-namespace:DrMuscle.Constants"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:helpers="clr-namespace:DrMuscle.Helpers"
    xmlns:effects="clr-namespace:DrMuscle.Effects"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    xmlns:video="clr-namespace:FormsVideoLibrary"
    xmlns:ffimageloadingsvg="clr-namespace:FFImageLoading.Svg.Forms;assembly=FFImageLoading.Svg.Forms"
    Height="{OnPlatform Android=60, iOS=55}"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms">
    <ViewCell.View>
<Grid BackgroundColor="Transparent">
        <pancakeView:PancakeView
            Padding="15,11,15,1"
            Margin="4,0,4,0"
            IsClippedToBounds="true"
            HorizontalOptions="FillAndExpand"
            OffsetAngle="90"
            VerticalOptions="FillAndExpand"
            Style="{StaticResource PancakeViewStyleBlue}"
            CornerRadius="0">
            
        </pancakeView:PancakeView>
<Grid
    Padding="15,11,15,1"
    Margin="4,0,4,0"
                    IsClippedToBounds="True"
                        ColumnSpacing="0"
                        RowSpacing="0"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand">
                    <Grid.Resources>
                        <converters:BoolInverter
                            x:Key="BoolInverterConverter" />
                    </Grid.Resources>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="25"  />
                        <ColumnDefinition Width="60"  />
                        <ColumnDefinition Width="0.77*" />
                        <ColumnDefinition Width="25" />
                        <ColumnDefinition Width="0.77*" />
                        <!--<ColumnDefinition Width="Auto" />-->
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition
                                Height="*" />
                    </Grid.RowDefinitions>
                    <ffimageloading:CachedImage
                            Source="done2.png"
                            Margin="0,5,0,5"
                            HeightRequest="20"
                            WidthRequest="20"
                            Aspect="AspectFit"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            IsVisible="{Binding IsFinished}" />
                     <ffimageloading:CachedImage
                            Source="deleteset.png"
                            Margin="0,5,0,5"
                            HeightRequest="20"
                            WidthRequest="20"
                            Aspect="AspectFit"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                            </ffimageloading:CachedImage>
                    <t:DrMuscleButton
                            Margin="0"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            Clicked="DeleteSetTapGestureRecognizer_Tapped"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                            </t:DrMuscleButton>
                    <Label
                            Text="{Binding SetNo}"
                            Grid.Row="0"
                            Grid.Column="1"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF"
                            />
    <!--Reps for normal-->
        <Grid Grid.Column="2" Grid.Row="0"  Margin="10,3,10,4" >
            <Grid.Triggers>
                            <DataTrigger
                                    TargetType="Grid"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="False" />
                                </DataTrigger>

                        </Grid.Triggers>
                    <t:WorkoutEntry

                                Text="{Binding Reps}"
                                x:Name="RepsEntry"
                                HorizontalTextAlignment="Center"
                                VerticalOptions="Center"
                                Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                                FontSize="21"
                                MaxLength="4"
                                TextChanged="RepsEntry_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                TextColor="#AAFFFFFF" >
                        
                            <!--<t:WorkoutEntry.Triggers>
                                 <MultiTrigger
                                    TargetType="t:WorkoutEntry">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </MultiTrigger>
                            </t:WorkoutEntry.Triggers>-->
                        </t:WorkoutEntry>
                    
                    <Label
                        
                            
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand"
                            Text="Max"
                            HorizontalTextAlignment="Center"
                            FontSize="21"
                            IsVisible="false"
                            TextColor="#AAFFFFFF" 
                                        >
                        <!--<Label.Triggers>
                            
                            <MultiTrigger
                                    TargetType="Label">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </MultiTrigger>
                        </Label.Triggers>-->
                    </Label>
        </Grid>
    <!--Seconds for cardio-->
     <Grid Grid.Column="2" Grid.Row="0" IsVisible="false" Margin="10,3,10,4" >
         <Grid.Triggers>
                            <DataTrigger
                                    TargetType="Grid"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                                    <Setter
                                        Property="IsVisible"
                                        Value="True" />
                                </DataTrigger>

                        </Grid.Triggers>
                        <t:WorkoutEntry
                            Text="{Binding RepsCardio}"
                            x:Name="RepsCardioEntry"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            MaxLength="6"
                            TextChanged="RepsEntry_TextChanged"
                            BackgroundColor="{Binding BackColor}"
                            TextColor="#AAFFFFFF">
                            <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                        </t:WorkoutEntry>

                        <t:WorkoutEntry
                            Text="Max"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            x:Name="RepsMax"
                            Keyboard="Numeric"
                            IsVisible="false"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            TextChanged="RepsEntry_TextChanged"
                            BackgroundColor="{Binding BackColor}"
                            TextColor="#AAFFFFFF">
                            <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter Property="IsReadOnly" Value="True" />
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                        </t:WorkoutEntry>
                        <Label
                            HorizontalTextAlignment="Center"
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            BackgroundColor="Transparent"
                            >
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_2" />
                            </Label.GestureRecognizers>
                        </Label>
                            </Grid>
                    <Label
                            Text="*"
                            Grid.Row="0"
                            Margin="0,2,0,0"
                            Grid.Column="3"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF" />
                    <t:WorkoutEntry
                                x:Name="WeightEntry"
                                Grid.Row="0"
                            Grid.Column="4"
                            Margin="10,3,10,4"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                                Text="{Binding WeightSingal}"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="Center"
                                TextChanged="WeightEntry_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                FontSize="21"
                                TextColor="#AAFFFFFF">   
                        <t:WorkoutEntry.Triggers>
                                <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding IsBodyweight}"
                                                Value="true">
                                    <Setter Property="IsReadOnly" Value="True" />
                                            </DataTrigger>
                            <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding BodypartId}"
                                                Value="12">
                                    <Setter Property="IsVisible" Value="False" />
                                            </DataTrigger>
                            </t:WorkoutEntry.Triggers>
                    </t:WorkoutEntry>
    <t:WorkoutEntry
                                x:Name="SpeedEntryNormal"
                                Grid.Row="0"
                            Grid.Column="4"
                            Margin="10,3,10,4"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            IsVisible="false"
                                Text="{Binding Speed}"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="Center"
                                TextChanged="SpeedEntryNormal_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                FontSize="21"
                                TextColor="#AAFFFFFF">
                        <t:WorkoutEntry.Triggers>
                                <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding BodypartId}"
                                                Value="12">
                                    <Setter Property="IsVisible" Value="True" />
                                            </DataTrigger>
                            </t:WorkoutEntry.Triggers>
                    </t:WorkoutEntry>
                </Grid>
</Grid>
    </ViewCell.View>
</ViewCell>
