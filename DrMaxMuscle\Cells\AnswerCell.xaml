<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.AnswerCell">
    <controls:CustomFrame 
    Style="{StaticResource GradientFrameStyleBlue}"
    VerticalOptions="Start" x:Name="FrmContainer"  HorizontalOptions="End"  Margin="40,10,10,5" Padding="20,12, 20,12" CornerRadius="12" >
    <controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />      
</controls:CustomFrame.Shadow>
        <Label x:Name="LblAnswer" Text="{Binding Answer}" 
           FontSize="17"
           LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
           TextColor="#ffffff" HorizontalOptions="End" HorizontalTextAlignment="End" Margin="4,0" />
    </controls:CustomFrame>
</ContentView>
