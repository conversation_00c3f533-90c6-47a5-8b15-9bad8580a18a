using Acr.UserDialogs;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using System.Globalization;
using System.Text.RegularExpressions;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using Microsoft.Maui.Devices;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using Newtonsoft.Json;
using DrMaxMuscle.Constants;
using static System.Net.Mime.MediaTypeNames;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;
using Application = Microsoft.Maui.Controls.Application;
using CommunityToolkit.Maui.Core;


namespace DrMaxMuscle.Views;

public partial class BodyProgressPopup : Popup
{
    public decimal existingWeight { get; set; }
    public decimal existingFat { get; set; }
    public bool IsEdit { get; set; }
    public bool _isLastestWeight = false;
    UserWeight userWeight = new UserWeight();
    DateTime? selectedDate = null;
    public DateTime DefaultDate { get; set; }

    //public event EventHandler<DateTime> DateSelected;
    
    public event EventHandler<PopupAction> ActionSelected;
    public enum PopupAction
    {
        Cancel,
        OK
    }
    public BodyProgressPopup(UserWeight _userWeight, bool isLastestWeight = false, decimal _weight = 0, decimal? _fat = null)
    {
        InitializeComponent();
        var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
        MainFrame.WidthRequest = screenWidth * 0.9;
        try
        {

            GetDetails();
            if (_userWeight != null)
            {
                IsEdit = true;
                userWeight = _userWeight;
                BindData();
            }
            else
            {
                IsEdit = false;
                if (_weight > 0)
                {
                    decimal weight = Math.Round((decimal)_weight, 1, MidpointRounding.AwayFromZero);
                    // Check if weight has non-zero decimal places
                    if (weight % 1 != 0)
                    {
                        weightEntry.Text = weight.ToString();
                    }
                    else
                    {
                        weightEntry.Text = ((int)weight).ToString(); // Convert to int to remove decimal part
                    }
                }
                if (_fat > 0)
                {
                    decimal fat = Math.Round(_fat ?? 0, 1);
                    if (fat % 1 == 0)
                    {
                        fatEntry.Text = fat.ToString("#");
                    }
                    else
                    {
                        fatEntry.Text = fat.ToString("0.0");
                    }
                }
            }

            _isLastestWeight = isLastestWeight;
            this.Opened += Popup_Opened;

        }
        catch (Exception ex)
        {

        }
    }

    private void Popup_Opened(object sender, PopupOpenedEventArgs e)
    {
        try
        {
            weightEntry.Focus();
            if (!string.IsNullOrEmpty(weightEntry.Text))
                weightEntry.CursorPosition = weightEntry.Text.Length;
        }
        catch (Exception ex)
        {

        }
    }

    private void BindData()
    {
        try
        {

            datpickerStack.IsEnabled = true;
            //currentDateLbl.Text = userWeight.CreatedDate.ToString("MM/dd/yyyy");
            currentDateLbl.Text = userWeight.CreatedDate.ToString("MM/dd/yyyy");
            datePicker.Date = userWeight.CreatedDate;
            //datpickerStack.IsEnabled = false;
            if (userWeight.Weight > 0)
            {

                decimal weight = Math.Round((decimal)userWeight.Weight, 1, MidpointRounding.AwayFromZero);
                // Check if weight has non-zero decimal places
                if (weight % 1 != 0)
                {
                    weightEntry.Text = weight.ToString();
                }
                else
                {
                    weightEntry.Text = ((int)weight).ToString(); // Convert to int to remove decimal part
                }
            }
            if (userWeight?.Fat > 0)
            {
                decimal fat = Math.Round(userWeight.Fat ?? 0, 1);
                if (fat % 1 == 0)
                {
                    fatEntry.Text = fat.ToString("#");
                }
                else
                {
                    fatEntry.Text = fat.ToString("0.0");
                }
            }

        }
        catch (Exception ex)
        {

        }
    }
    private void GetDetails()
    {
        //var currentDate = DateTime.Now.Date;
        //currentDateLbl.Text = currentDate.ToString("MM/dd/yyyy");
        try
        {

            datpickerStack.IsEnabled = true;
            var currentDate = DateTime.Now.Date;
            datePicker.MaximumDate = currentDate;

            datePicker.Date = currentDate;
            currentDateLbl.Text = currentDate.ToString("MM/dd/yyyy");
            var massUnit = (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg") ? "kg" : "lbs";
            weightEntry.Placeholder = "Body weight in " + massUnit;

        }
        catch (Exception ex)
        {

        }
    }

    private async void Cancel_Btn_Clicked(object sender, EventArgs e)
    {
        await this.CloseAsync();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();
    }

    private async void OK_Btn_Clicked(object sender, EventArgs e)
    {
        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Try again"
                // });
                return;
            }

            if (string.IsNullOrWhiteSpace(weightEntry.Text) || Convert.ToDecimal(weightEntry.Text.ReplaceWithDot(), CultureInfo.InvariantCulture) < 1)
            {
                return;
            }

            if (IsEdit)
            {
                try
                {


                    var weightText = weightEntry.Text.ReplaceWithDot();
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    decimal bodyFat = 0;
                    var updatedWeight = new UserWeight() { };
                    updatedWeight.Weight = weight1;
                    if (!string.IsNullOrEmpty(fatEntry.Text))
                    {
                        bodyFat = Convert.ToDecimal(fatEntry.Text.ReplaceWithDot() , CultureInfo.InvariantCulture);
                        updatedWeight.Fat = bodyFat;
                    }
                    updatedWeight.Id = userWeight.Id;
                    updatedWeight.CreatedDate = (DateTime)((selectedDate != null && selectedDate != userWeight.CreatedDate) ? selectedDate : userWeight.CreatedDate);

                    if (LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb")
                    {
                        weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                        updatedWeight.Weight = new MultiUnityWeight(weight1, "lb").Kg;
                        userWeight.Label = string.Format("{0:0.##}", weight1);
                    }
                    else
                    {
                        updatedWeight.Label = string.Format("{0:0.##}", weight1);
                        updatedWeight.Weight = weight1;
                    }
                    if (_isLastestWeight)
                    {
                        LocalDBManager.Instance.SetDBSetting("BodyWeight", TruncateDecimal(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, 3).ToString().Replace(",", "."));
                    }
                    await DrMuscleRestClient.Instance.UpdateUserWeightHistoryWithOutLoader(updatedWeight);
                }
                catch (Exception ex)
                {

                }
                try
                {
                    await this.CloseAsync();
                    //await MauiProgram.SafeDismissTopPopup();
                    //if (PopupNavigation.Instance.PopupStack?.Count > 0)
                    //{
                    //    await PopupNavigation.Instance.PopAsync();
                    //}
                }
                catch (ObjectDisposedException ex)
                {
                }
            }
            else
            {
                try
                {
                    decimal? bodyFat = !string.IsNullOrEmpty(fatEntry.Text) ? Convert.ToDecimal(fatEntry.Text.ReplaceWithDot(), CultureInfo.InvariantCulture) : (decimal?)null;
                    var weightText = weightEntry.Text.ReplaceWithDot();
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    if (bodyFat > 0)
                    {
                        //bodyFat = Convert.ToDecimal(bodyFat.ToString().ReplaceWithDot(), CultureInfo.InvariantCulture);
                        LocalDBManager.Instance.SetDBSetting("BodyFat", bodyFat.ToString());
                    }

                    LocalDBManager.Instance.SetDBSetting("BodyWeight", TruncateDecimal(new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value).Kg, 3).ToString().Replace(",", "."));
                    var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.ReplaceWithDot(), System.Globalization.CultureInfo.InvariantCulture);
                    var weights = new MultiUnityWeight(value, "kg");
                    
                    try
                    {
                        await this.CloseAsync();
                        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
                        //    await PopupNavigation.Instance.PopAsync();
                    }
                    catch (ObjectDisposedException ex)
                    {
                    }
                    try
                    {
                        if (selectedDate == null || (selectedDate != null && ((DateTime)selectedDate).Date == DateTime.Now.Date))
                        {
                            // check last value of stored date
                            var data = LocalDBManager.Instance.GetDBSetting("OverallWeights")?.Value;
                            if (data != null)
                            {
                                var weightList = JsonConvert.DeserializeObject<List<UserWeight>>(data);
                                if (weightList != null && weightList.Count > 0)
                                {
                                    if (weightList.FirstOrDefault().CreatedDate.Date == DateTime.Now.Date)
                                        weightList.RemoveAt(0);
                                    if (weightList.Count > 0 && weightList.FirstOrDefault().CreatedDate.Date != DateTime.Now.Date)
                                    {
                                        var userWeight0 = new UserWeight() { CreatedDate = DateTime.Now, Fat = bodyFat, Weight = weights.Kg };

                                        var userWeight1 = weightList[0];
                                        decimal previousTrendWeight = (decimal)userWeight1.TrendWeight;

                                        userWeight0.TrendWeight = previousTrendWeight + (0.1m * (userWeight0.Weight - previousTrendWeight));

                                        var userWeight2 = weightList.Count > 2 ? weightList[1] : null;

                                        // Uncomment code please
                                        var popup = new EndCheckinPopup(userWeight0, userWeight1, userWeight2);
                                        await PopupNavigation.Instance.PushAsync(popup);
                                    }
                                }

                            }
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                    App.IsWeightChangeFromOtherScreen = false;
                    App.IsMealPlanChange = false;

                    LocalDBManager.Instance.SetDBSetting("Macros", "");
                    var email = LocalDBManager.Instance.GetDBSetting("email")?.Value;
                    Preferences.Set($"Macros{email}", "");
                    //LblBodyweight.Text = string.Format("{0:0.##}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? weights.Kg : weights.Lb);
                    await DrMuscleRestClient.Instance.SetUserBodyWeightWithoutLoader(new UserInfosModel()
                    {
                        BodyWeight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value),
                        Fat = bodyFat,
                        CreationDate = selectedDate ?? DateTime.Now
                    });
                    // WeightBox.IsVisible = false;
                    // Uncomment code please it cause
                    try
                    {
                        if (selectedDate == null || (selectedDate != null && ((DateTime)selectedDate).Date == DateTime.Now.Date))
                        {
                            if (Device.RuntimePlatform.Equals(Device.iOS))
                            {
                                IHealthData _healthService = DependencyService.Get<IHealthData>();
                                await _healthService.GetWeightPermissionAsync(async (r) =>
                                {
                                    var a = r;
                                    if (r)
                                    {
                                        _healthService.SetWeight(LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (double)Math.Round(weights.Kg, 2) : (double)Math.Round(weights.Lb, 2));
                                    }
                                });
                            }
                        }
                        }
                    catch (Exception ex)
                    {

                    }
                    
                }
                catch (Exception ex)
                {

                }
            }
            //await PopupNavigation.Instance.PopAsync();
            ActionSelected?.Invoke(this, PopupAction.OK);
        }
        catch (Exception ex)
        {

        }
    }
    public decimal TruncateDecimal(decimal value, int precision)
    {
        decimal step = (decimal)Math.Pow(10, precision);
        decimal tmp = Math.Truncate(step * value);
        return tmp / step;
    }

    private void fatEntry_TextChanged(object sender, TextChangedEventArgs e)
    {

        try
        {
            if (!string.IsNullOrEmpty(e.NewTextValue))
            {
                if (int.TryParse(e.NewTextValue, out int value))
                {
                    if (value > 100)
                        fatEntry.MaxLength = 2;
                    else
                        fatEntry.MaxLength = 4;
                }
                else
                {
                    fatEntry.MaxLength = 4;
                }
            }
            else
            {
                fatEntry.MaxLength = 4;
            }
        }
        catch (Exception ex)
        {
            fatEntry.MaxLength = 4;
        }

    }

    private void datePicker_DateSelected(object sender, DateChangedEventArgs e)
    {
        selectedDate = e.NewDate;
        if (selectedDate != null)
        {
            currentDateLbl.Text = selectedDate?.ToString("MM/dd/yyyy");
        }
    }

    private void TapGestureRecognizer_Tapped(object sender, EventArgs e)
    {

    }

    private void Picker_Tapped(object sender, EventArgs e)
    {
            datePicker.Focus();
    }
}
