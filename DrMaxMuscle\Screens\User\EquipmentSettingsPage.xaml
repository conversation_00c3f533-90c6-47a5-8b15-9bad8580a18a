﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.EquipmentSettingsPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:control="clr-namespace:DrMaxMuscle.Controls"
             Title="EquipmentSettingsPage">
    <ScrollView x:Name="SettingStack" HorizontalOptions="FillAndExpand" Padding="20,0,20,0">

        <StackLayout Margin="20,20,20,20"  HorizontalOptions="FillAndExpand" >
            <StackLayout Margin="0,0,0,0">
                <Label x:Name="LblUnits" Style="{StaticResource BoldLabelStyle}"  />

                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                    <Label x:Name="LblMinIncrements" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                    <control:DrEntry x:Name="MinEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                </StackLayout>
                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                    <Label x:Name="Increments" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                    <control:DrEntry x:Name="UnitEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                </StackLayout>
                <Label Text="The most general setting. Used when no other setting applies." Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />
                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                    <Label x:Name="LblMaxIncrements" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                    <control:DrEntry x:Name="MaxEntry"  HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                </StackLayout>

                <t:DrMuscleButton x:Name="SaveIncrementsButton" BackgroundColor="Transparent" BorderColor="#195377" BorderWidth="1" Margin="0,10,0,0" Style="{StaticResource buttonStyle}" />


            </StackLayout>
            <Label Text="GYMS" Style="{StaticResource BoldLabelStyle}" Margin="0,10,0,0"  />
            <Frame HasShadow="False" Margin="0,0,0,0" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
                    <StackLayout 
                      Margin="0"
      x:Name="GymGradient"
                          HorizontalOptions="FillAndExpand" VerticalOptions="End"  >

                        <Label FontSize="14" Text="Gym" x:Name="BtnGymEquipment" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="BtnGymEquipmentClicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                    <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                    <StackLayout
                      Margin="0" 
                          HorizontalOptions="FillAndExpand" x:Name="HomeGradient" VerticalOptions="End" >

                        <Label FontSize="14" Text="Home" x:Name="BtnHomeEquipment" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="BtnHomeEquipmentClicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                    <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                    <StackLayout 
                      Margin="0" 
                         HorizontalOptions="FillAndExpand" x:Name="OtherGradient" VerticalOptions="End" >

                        <Label FontSize="14"  Text="Other" x:Name="BtnOther" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"   TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="BtnOtherEquipmentClicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>
                </StackLayout>
            </Frame>

            <StackLayout x:Name="GymEquipmentStack" >

                <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                    <Label x:Name="LblOnlyEquipFor" Text="Activate profile" HorizontalOptions="FillAndExpand" Style="{StaticResource NormalLabelStyle}" />
                    <Switch x:Name="OnlyEquipForSwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                </StackLayout>
                <Label Text="Overrides general settings." Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />
                <StackLayout x:Name="OnlyEquipment" IsVisible="false">
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                        <Label Text="I have a chin-up bar" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="ChinUpSwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                        <Label Text="I have a pulley" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="PullySwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <CollectionView x:Name="PulleyListView" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill"  >
                        <!-- Uncomment  ios:ListView.SeparatorStyle="FullWidth" >-->
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <ContentView>
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                            </StackLayout>
                                            <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">

                                                <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                            </StackLayout>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="PulleyListView_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                        <StackLayout.Triggers>
                                            <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                <Setter Property="Margin" Value="0,8,0,0" />
                                            </DataTrigger>
                                        </StackLayout.Triggers>
                                    </StackLayout>
                                </ContentView>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>

                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                        <Label Text="I have plates" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="PlatesSwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <CollectionView x:Name="PlatesListView" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" >
                        <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <ContentView>
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                            </StackLayout>
                                            <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout.Triggers>
                                            <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                <Setter Property="Margin" Value="0,8,0,0" />
                                            </DataTrigger>
                                        </StackLayout.Triggers>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Tapped="PlatesListView_Tapped" CommandParameter="{Binding .}"/>
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </ContentView>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                        <Label Text="I have dumbbells" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="DumbbellsSwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <CollectionView x:Name="DumbbellsListView" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand">
                        <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <ContentView>
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                            </StackLayout>
                                            <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">

                                                <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout.Triggers>
                                            <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                <Setter Property="Margin" Value="0,8,0,0" />
                                            </DataTrigger>
                                        </StackLayout.Triggers>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Tapped="DumbbellsListView_Tapped" CommandParameter="{Binding .}"/>
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </ContentView>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>

                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0" >
                        <Label Text="I have bands" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="BandsSwitch" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <CollectionView x:Name="BandsListView" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill" >
                        <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <ContentView>
                                    <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                        <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                            </StackLayout>
                                            <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">

                                                <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                            </StackLayout>
                                        </StackLayout>
                                        <StackLayout.Triggers>
                                            <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                <Setter Property="Margin" Value="0,8,0,0" />
                                            </DataTrigger>
                                        </StackLayout.Triggers>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer Tapped="BandsListView_Tapped" CommandParameter="{Binding .}"/>
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                </ContentView>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>


                </StackLayout>
            </StackLayout>

            <StackLayout x:Name="HomeEquipmentStack" IsVisible="false">
                <StackLayout x:Name="EquipmentPlusStack" >
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                        <Label x:Name="LblOnlyEquipFor1" Text="Activate profile" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="OnlyEquipForSwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <Label Text="Overrides general settings." Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />

                    <StackLayout x:Name="OnlyEquipment1" IsVisible="false">
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have a chin-up bar" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="ChinUpSwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have a pulley" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="PullySwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="PulleyListView1" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" >
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditHome" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteHome" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="PulleyListView1_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have plates" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="PlatesSwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="PlatesListView1" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" >
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView >
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditHome" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteHome" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="PlatesListView1_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have dumbbells" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="DumbbellsSwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="DumbbellsListView1" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill">
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditHome" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteHome" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />

                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="DumbbellsListView1_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0" >
                            <Label Text="I have bands" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="BandsSwitch1" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="BandsListView1" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill" >
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditHome" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteHome" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />

                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BandsListView1_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </StackLayout>

            </StackLayout>

            <StackLayout x:Name="OtherEquipmentStack" IsVisible="false">

                <StackLayout x:Name="EquipmentOtherStack">

                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0">
                        <Label x:Name="LblOnlyEquipFor2" Text="Activate profile" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                        <Switch x:Name="OnlyEquipForSwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                    </StackLayout>
                    <Label Text="Overrides general settings." Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />

                    <StackLayout x:Name="OnlyEquipment2" IsVisible="false">
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have a chin-up bar" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="ChinUpSwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have a pulley" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="PullySwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="PulleyListView2" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" >
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditOther" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteOther" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="PulleyListView2_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have plates" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="PlatesSwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="PlatesListView2" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="FillAndExpand"  >
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditOther" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteOther" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="PlatesListView2_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0">
                            <Label Text="I have dumbbells" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="DumbbellsSwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="DumbbellsListView2" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill">
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditOther" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteOther" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="DumbbellsListView2_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>

                        <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,15,0,0" >
                            <Label Text="I have bands" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                            <Switch x:Name="BandsSwitch2" IsToggled="false" HorizontalOptions="End" VerticalOptions="Center" />
                        </StackLayout>
                        <CollectionView x:Name="BandsListView2" IsVisible="false" BackgroundColor="Transparent" VerticalOptions="Fill">
                            <!-- Uncomment ios:ListView.SeparatorStyle="FullWidth" >-->
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <ContentView>
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" HorizontalOptions="FillAndExpand">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                                                <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                    <Label Text="{Binding Label}" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                                                </StackLayout>
                                                <StackLayout Orientation="Horizontal" IsVisible="{Binding Id, Converter={StaticResource IntBoolConverter}}" HorizontalOptions="End">
                                                    <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                                                    <t:DrMuscleButton Clicked="OnEditOther" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                    <t:DrMuscleButton Clicked="OnDeleteOther" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                    <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                                </StackLayout>
                                            </StackLayout>
                                            <StackLayout.Triggers>
                                                <DataTrigger TargetType="StackLayout" Binding="{Binding Id}" Value="-1">
                                                    <Setter Property="Margin" Value="0,8,0,0" />
                                                </DataTrigger>
                                            </StackLayout.Triggers>
                                            <StackLayout.GestureRecognizers>
                                                <TapGestureRecognizer Tapped="BandsListView2_Tapped" CommandParameter="{Binding .}"/>
                                            </StackLayout.GestureRecognizers>
                                        </StackLayout>
                                    </ContentView>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </StackLayout>

            </StackLayout>

        </StackLayout>

    </ScrollView>

</ContentPage>