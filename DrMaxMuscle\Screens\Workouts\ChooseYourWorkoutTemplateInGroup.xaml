﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:DrMaxMuscle"
             xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Screens.Workouts.ChooseYourWorkoutTemplateInGroup"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             Title="ChooseYourWorkoutTemplateInGroup">
     <ContentPage.Resources>
    <ResourceDictionary>
      <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
    </ResourceDictionary>
  </ContentPage.Resources>
  <AbsoluteLayout>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
      <StackLayout VerticalOptions="FillAndExpand">
        <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
          <t:DrMuscleListView x:Name="WorkoutListView" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="#264457" SeparatorVisibility="Default" ios:ListView.SeparatorStyle="FullWidth">
            <ListView.ItemTemplate>
              <DataTemplate>
                <ViewCell Height="45">
                  <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                            <Label Text="{Binding Label}" VerticalTextAlignment="Center"  Style="{StaticResource LabelStyle}"></Label>
                        </StackLayout>  
                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                          <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                          <t:DrMuscleButton Clicked="OnRename" Text="{Binding [Rename].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextRenameButton}"  />
                          <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}"  />
                          <!--<t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />-->
                          <t:DrMuscleButton Clicked="OnReset" Text="{Binding [More].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextResetButton}" />             
                          <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise, Converter={StaticResource BooleanInverter}}"  Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                  </StackLayout>
                </ViewCell>
              </DataTemplate>
            </ListView.ItemTemplate>
          </t:DrMuscleListView>
        </StackLayout>
      </StackLayout>
    </StackLayout>
  </AbsoluteLayout>
</ContentPage>
