﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"
    x:Class="DrMuscle.Cells.RestRecoveredCell">
    <ViewCell.View>
        <Grid
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            Padding="10,10,10,0">
            <Grid.Margin>
                <OnPlatform
                    x:TypeArguments="Thickness"
                    iOS="0,0,0,0"
                    Android="0" />
            </Grid.Margin>
            <Grid.RowDefinitions>
                <RowDefinition
                    Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                
            </Grid.ColumnDefinitions>
            
            <StackLayout
                Grid.Row="0"
                Orientation="Vertical"
                HorizontalOptions="Center"
                Spacing="2"
                Grid.Column="0">
                <StackLayout Orientation="Horizontal" HorizontalOptions="CenterAndExpand">
                    <ffimageloading:CachedImage
                        HorizontalOptions="Center"
                        WidthRequest="20"
                        HeightRequest="20"
                        Aspect="AspectFit"
                        Source="orange2.png"
                         >
                        <ffimageloading:CachedImage.Triggers>
                            <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding TrainRest}" Value="Train">
                                <Setter Property="Source" Value="green.png" />
                            </DataTrigger>
                            <DataTrigger TargetType="ffimageloading:CachedImage" Binding="{Binding TrainRest}" Value="Rest">
                                <Setter Property="Source" Value="orange2.png" />
                            </DataTrigger>
                        </ffimageloading:CachedImage.Triggers>
                        </ffimageloading:CachedImage>
                <Label
                    Text="{Binding TrainRest}"
                    TextColor="{Binding StrengthTextColor}"
                    FontSize="Medium"
                    FontAttributes="Bold"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center" /></StackLayout>
                <Label
                    Text="{Binding TrainRestText}"
                    FontSize="13"
                            Style="{StaticResource LabelStyle}"
                    HorizontalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center" />
            </StackLayout>

        </Grid>
    </ViewCell.View>
</ViewCell>
