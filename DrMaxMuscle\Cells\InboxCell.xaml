<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             xmlns:app1 ="clr-namespace:DrMaxMuscle.Constants"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.InboxCell">
    <Grid
    FlowDirection="LeftToRight"
    ColumnSpacing="5"
    x:Name="ContentGrid"
    RowSpacing="0"
    Padding="8,15">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="40">
            </ColumnDefinition>
            <ColumnDefinition
            Width="*">
            </ColumnDefinition>
            <!--<ColumnDefinition
            Width="40">
        </ColumnDefinition>-->
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
            Height="auto">
            </RowDefinition>
            <RowDefinition
            Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <Frame
        x:Name="imgInProfilePicFrame"
         Grid.Row="0"
         Grid.Column="0"
         Grid.RowSpan="2"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        Padding="0"
        Margin="0"
        HeightRequest="35"
        WidthRequest=" 35"
        BackgroundColor="Transparent"
        CornerRadius="{OnPlatform Android='30' , iOS= '17'}">
            <ffimageloading:CachedImage
   
            x:Name="imgInProfilePic"
            
            WidthRequest="35"
            HeightRequest="35"
            DownsampleToViewSize="true"
            LoadingPlaceholder="backgroundblack.png"
            Source="{Binding ProfileUrl}"
                ErrorPlaceholder="backgroundblack.png">
            </ffimageloading:CachedImage>
        </Frame>

        <Border
        Padding="0"
        Grid.Row="0"
        Grid.Column="0"
        Grid.RowSpan="2"
        HorizontalOptions="Center"
        VerticalOptions="Start"
        WidthRequest="35"
        HeightRequest="35"
        Style="{StaticResource GradientBorderStyleBlue}"
        x:Name="FrmProfile">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="{OnPlatform Android='30' , iOS= '17'}"/>
            </Border.StrokeShape>
            <Label
            x:Name="LblProfileText"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            Margin="2,0,0,0"
            Text=""
            TextColor="White"
            FontSize="Large" />
        </Border>
        <!--Option 2-->
        <Frame
        Margin="10,10,40,5"
        CornerRadius="12"
        Padding="20,12,20,12"
        BorderColor="#ffffff"
        HasShadow="False"
        IsVisible="false"
        Grid.Row="1"
        Grid.Column="1"
        VerticalOptions="End"
        HorizontalOptions="Start"
        BackgroundColor="#ffffff">
            <controls:ExtendedLabel
            VerticalTextAlignment="End"
            x:Name="lblOutMessage"
            HorizontalOptions="End"
            HorizontalTextAlignment="Start"
            TextColor="#23253a"
            Text="{Binding Message}">
                <controls:ExtendedLabel.Triggers>
                    <DataTrigger
                    TargetType="controls:ExtendedLabel"
                    Binding="{Binding IsUnread}"
                    Value="True">
                        <Setter
                        Property="FontAttributes"
                        Value="Bold" />
                    </DataTrigger>
                </controls:ExtendedLabel.Triggers>
                
            </controls:ExtendedLabel>
        </Frame>


        <StackLayout
        Grid.Row="1"
        Grid.Column="1"
        VerticalOptions="Center"
        Orientation="Horizontal"
        HorizontalOptions="FillAndExpand"
        Spacing="0">
            <Label
            x:Name="LblName"
            FontSize="16"
            VerticalOptions="Center"
            FontAttributes="Bold"
            HorizontalOptions="StartAndExpand"
            Text="{Binding Nickname}"
            TextColor="{x:Static app1:AppThemeConstants.BlueColor}">
            </Label>
            <Label
             FontSize="13"
             x:Name="createdDate"
             VerticalOptions="Center"
             HorizontalOptions="End"
             Text="{Binding CreatedDate, StringFormat='{0:MMM d, yyyy h:mm tt}'}"
             TextColor="#26262B">
            </Label>


        </StackLayout>

    </Grid>
</ContentView>
