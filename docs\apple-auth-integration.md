# Apple Sign-In Integration Guide (<PERSON><PERSON> <PERSON><PERSON><PERSON>end)

This document reverse-engineers the current **legacy** authentication flow used by the mobile apps and describes how to implement the same flow in the watchOS app (and any future Web app).

## 1  High-level Flow

1. Get the user's Apple ID credentials from `ASAuthorizationAppleIDProvider` (watchOS/iOS) or Sign-in-With-Apple JS (web).
2. Extract the following values:
   • `userId` – stable Apple user identifier (string, **required**)
   • `email`  – user e-mail (may be *nil* on subsequent logins)
   • `name`   – full name (optional; first login only)
3. Call Dr. Muscle backend POST `/token` with **form-url-encoded** body containing the parameters listed below **in the exact order** observed in the shipping iOS app (order matters— the ASP-NET OAuth middleware parses by order):

| Key         | Purpose                                             | Apple value                     |
|-------------|-----------------------------------------------------|---------------------------------|
| `grant_type`| Legacy field, always the literal **`google`**       | `google`                        |
| `accesstoken`| Google OAuth token for Google login; for Apple leave empty | "" (empty string)            |
| `provider`  | Legacy field, always **`google`**                   | `google`                        |
| `email`     | Apple e-mail or empty string                        | `email ?? ""`                  |
| `name`      | User full name or empty string                      | `name ?? ""`                   |
| `bodyweight`| Optional; still **must be present** (empty OK)      | `bodyWeight ?? ""`             |
| `massunit`  | Optional mass unit (`kg` / `lb`); **must be present** | `massUnit ?? ""`            |
| `userid`    | **Apple userId** (critical for backend to detect Apple login) | `userId`               |

The backend's custom `SimpleAuthorizationServerProvider .cs` identifies Apple login when **`accesstoken` is empty** *and* `userid` is non-empty.

Successful response → JSON `LoginSuccessResult`:
```
{
  "access_token": "{JWT}",
  "token_type"  : "bearer",
  "expires_in"  : 1209599,
  "userName"    : "<EMAIL>",
  "issued"      : "2025-06-30T12:34:56Z",
  "expires"     : "2025-07-14T12:34:56Z"
}
```
Store `access_token` for subsequent API calls (Bearer token).

## 2  Pre-flight Checks

Before calling `/token` the mobile app performs two helper checks that map to dedicated API endpoints:

1. `POST api/Account/IsEmailAlreadyExistbyAppleId` with JSON `{ "appleId": "userId" }` → `{ "result": true|false }`
2. (optional) `POST api/Account/IsEmailAlreadyExist` with JSON `{ "email": "<EMAIL>" }` → `{ "result": true|false }`

Use these to decide whether to prompt for e-mail disclosure on first login.

## 3  watchOS Implementation Checklist

1. Conform to Swift 6 concurrency rules (`@MainActor`, `Sendable`, etc.).
2. Build request body with **exact key order** above (see `orderedKeys` array in `DrMuscleAPIClient.signInWithApple`).
3. Always include `bodyweight` and `massunit` even when values are unknown → send empty string.
4. Content-Type: `application/x-www-form-urlencoded`; no Authorization header.
5. On success store `access_token` to Keychain and broadcast authentication state.
6. On error decode OAuth error JSON `{ "error_description": "…" }` and surface friendly message.

## 4  Web App Integration Notes

The same parameter contract applies.  Use Apple JS to obtain the credential, then submit the form as above via HTTPS POST.

Important:
• The backend is **not** using the official Apple REST validation endpoint; it trusts the signed `userId`.  No extra server-side validation required.
• Future migration path: introduce proper `provider=apple` and deprecate current hack—documented in ADR-0008.

## 5  Sample cURL
```
curl -X POST https://drmuscle.azurewebsites.net/token \ 
     -H "Content-Type: application/x-www-form-urlencoded" \ 
     --data-urlencode "grant_type=google" \
     --data-urlencode "accesstoken=" \
     --data-urlencode "provider=google" \
     --data-urlencode "email=<EMAIL>" \
     --data-urlencode "name=John%20Doe" \
     --data-urlencode "bodyweight=" \
     --data-urlencode "massunit=" \
     --data-urlencode "userid=0012345A-BCDE-…"
```

---
Last updated : 2025-06-27 