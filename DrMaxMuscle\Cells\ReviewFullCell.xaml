<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Cells.ReviewFullCell">
    <StackLayout BackgroundColor="#F0F0F0" Margin="0,8,0,0" Padding="0,8,0,8" x:Name="StackContainer" >
        <Label
                        x:Name="LblTitle"
                        Text="{Binding Part1}"
                        Margin="10,0,0,0"
                        IsVisible="true"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
            FontAttributes="Bold"
            FontSize="18"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />
        <Frame Padding="20,5,20,15" Margin="10,2,40,2" BackgroundColor="White" CornerRadius="12" HasShadow="False" >
            <StackLayout>
                <Image Source="stars_5.png" x:Name="ImgReivew" WidthRequest="120" Aspect="AspectFit" HorizontalOptions="Start" />
                <Label x:Name="LblReview" Text="{Binding Part2}" Style="{StaticResource LabelStyle}" FontAttributes="Bold" />
                <Label x:Name="LblsubHeadingReviewer" Text="{Binding Part3}" Style="{StaticResource LabelStyle}"  />
                <Label x:Name="LblReviewerName" Text="{Binding Answer}" LineBreakMode="WordWrap" Style="{StaticResource LabelStyle}" FontAttributes="Bold"  />
                <Image
x:Name="ImgPhoto"
HorizontalOptions="FillAndExpand"
Aspect="AspectFit"
Source="{Binding Source}" />

            </StackLayout>
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="TapMoreReviews_Tapped" />
            </Frame.GestureRecognizers>
        </Frame>
        <!--<Label
                        Text="More reviews"
                        Margin="20,0,0,0"
                        IsVisible="true"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                        Font="Bold,16"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" >
                         <Label.GestureRecognizers>
                        <TapGestureRecognizer Tapped="TapMoreReviews_Tapped" CommandParameter="{Binding .}" />
                    </Label.GestureRecognizers>
                        </Label>-->
    </StackLayout>
</ContentView>
