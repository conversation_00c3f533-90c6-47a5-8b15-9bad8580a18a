﻿using Acr.UserDialogs;
using DrMaxMuscle.Screens.Exercises;
using DrMaxMuscle.Layout;
using DrMuscleWebApiSharedModel;

using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using DrMaxMuscle.Screens.History;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Constants;
using Microsoft.AppCenter.Crashes;
using System.Globalization;
using DrMaxMuscle.Views;
using DrMaxMuscle.Screens.Me;
using DrMaxMuscle.Effects;
using DrMaxMuscle.Screens.User;
using RGPopup.Maui.Services;
using System.Text.RegularExpressions;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;

namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseYourCustomWorkoutPage : ContentPage
{
    public List<WorkoutTemplateModel> workouts;
    public List<WorkoutTemplateGroupModel> workoutGroups;
    public ObservableCollection<WorkoutTemplateModel> workoutItems = new ObservableCollection<WorkoutTemplateModel>();
    public ObservableCollection<WorkoutTemplateGroupModel> workoutOrderItems = new ObservableCollection<WorkoutTemplateGroupModel>();
    ProgramListPopup popup;
    public ChooseYourCustomWorkoutPage()
    {
        InitializeComponent();

        WorkoutListView.ItemsSource = workoutItems;
        ProgramListView.ItemsSource = workoutOrderItems;

        WorkoutListView.ItemTapped += WorkoutListView_ItemTapped;
        ProgramListView.ItemTapped += WorkoutListView_ItemTapped;

        workoutButton.Clicked += WorkoutsButton_Clicked;
        programButton.Clicked += ProgramButton_Clicked;
        UploadButton.Clicked += UploadButton_Clicked;
        createWorkoutButton.Clicked += CreateWorkoutButton_Clicked;

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutTypeListDayPerWeek") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutTypeListDayPerWeek", "0");

        if (LocalDBManager.Instance.GetDBSetting("WorkoutOrderList") == null)
            LocalDBManager.Instance.SetDBSetting("WorkoutOrderList", "0");

        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkout;
        LblMyWorkouts.Text = AppResources.MyWorkouts;
        LblMyPrograms.Text = AppResources.MyPrograms;

    }

    private bool IsFromMePage()
    {
        var isMePage = false;
        foreach (var item in Navigation.NavigationStack)
        {

            if (item is SettingsPage)
            {
                if (Navigation.NavigationStack.Last() == item)
                    continue;
                isMePage = true && CurrentLog.Instance.IsChangeProgram;
                break;
            }
        }
        return isMePage;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        // Perform UI changes after initialization using Device.BeginInvokeOnMainThread
        Device.BeginInvokeOnMainThread(async () =>
        {
            if (IsFromMePage())
            {
                LblMyWorkouts.IsVisible = false;
                WorkoutListView.IsVisible = false;
                workoutGrid.IsVisible = false;
           //     createWorkoutButton.IsVisible = true;
                PlusIcon2.IsVisible = false;
                PlusIcon.IsVisible = false;
            }

            else
            {
                LblMyWorkouts.IsVisible = true;
                workoutGrid.IsVisible = true;
           //     createWorkoutButton.IsVisible = false;
                PlusIcon2.IsVisible = true;
                PlusIcon.IsVisible = true;
                if (workouts?.Count == 0 && EmptyWorkouts.IsVisible)
                {

                    EmptyWorkouts.IsVisible = true;
                }
                else
                    WorkoutListView.IsVisible = true;

                if (Config.ShowCustomPopup == false)
                {
                    if (App.IsCustomPopup)
                        return;
                    App.IsCustomPopup = true;
                    var ShowPopUp = await HelperClass.DisplayCustomPopup("Custom workouts",
                    "Tap the plus button at the bottom to create a workout (exercises in the order you like), a program (workouts in the order you like), or upload your program via the Web.",
                    AppResources.GotIt,AppResources.RemindMe);
                    ShowPopUp.ActionSelected += (sender,action) => {
                        if(action == PopupAction.OK){
                            Config.ShowCustomPopup = true;
                            App.IsCustomPopup = false;
                        }
                        else
                        {
                            Config.ShowCustomPopup = false;
                            App.IsCustomPopup = false;
                        }
                    };
                }
            }

            DependencyService.Get<IFirebase>().SetScreenName("choose_custom_workout");
            try
            {
                WorkoutListView.IsVisible = true;
                EmptyWorkouts.IsVisible = false;

                ProgramListView.IsVisible = true;
                EmptyProgram.IsVisible = false;
                if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                    LocalDBManager.Instance.SetDBSetting("Equipment", "false");

                if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                    LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

                if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

                if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                    LocalDBManager.Instance.SetDBSetting("Plate", "true");

                if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                    LocalDBManager.Instance.SetDBSetting("Pully", "true");

                GetUserWorkoutTemplateResponseModel itemsSource;

                if (workouts == null || workouts.Count == 0)
                {
                    itemsSource = await DrMuscleRestClient.Instance.GetCustomWorkoutsForUser();
                    workouts = itemsSource.Workouts;
                }


                if (workoutGroups == null || workoutGroups.Count == 0)
                {
                    GetUserWorkoutTemplateGroupResponseModel itemsGroupSource = await DrMuscleRestClient.Instance.GetUserWorkoutGroup();
                    workoutGroups = itemsGroupSource.WorkoutOrders;
                }
                if (workouts == null || workouts.Count == 0)
                {
                    EmptyWorkouts.IsVisible = true;
                    LbEmptyProgram.Text = "To create a custom program, you need to create custom workout(s) first";
                    if (Device.RuntimePlatform.Equals(Device.Android))
                        await Task.Delay(300);
                    TooltipEffect.SetHasShowTooltip(PlusIcon, true);
                }
                if (workoutGroups == null || workoutGroups.Count == 0)
                {
                    if (workouts == null || workouts.Count == 0)
                        LbEmptyProgram.Text = "To create a custom program, you need to create custom workout(s) first";
                    else
                        LbEmptyProgram.Text = "Tap \"+\" to create your custom program";
                    EmptyProgram.IsVisible = true;
                    if (Device.RuntimePlatform.Equals(Device.Android))
                        await Task.Delay(300);
                    TooltipEffect.SetHasShowTooltip(PlusIcon, true);
                }
                await UpdateWorkoutList();
            }
            catch (Exception e)
            {

            }
        });
    }


    private async Task UpdateWorkoutList()
    {
        try
        {
            if (workouts == null)
                return;
            workoutItems.Clear();
            workoutOrderItems.Clear();
            List<WorkoutTemplateModel> wkt;

            var request = workouts.Where(e => e.IsSystemExercise == false);
            var items = new ObservableCollection<WorkoutTemplateModel>();
            wkt = request.ToList();

            foreach (WorkoutTemplateModel em in wkt)
                items.Add(em);
            workoutItems = items;
            WorkoutListView.ItemsSource = workoutItems;

            //WorkoutTemplateModel addWorkoutItem = new WorkoutTemplateModel();
            //addWorkoutItem.Id = -1;
            //addWorkoutItem.IsSystemExercise = true;
            //addWorkoutItem.Label = AppResources.TapToCreateNewCustomWorkout___;;
            //workoutItems.Add(addWorkoutItem);

            foreach (WorkoutTemplateGroupModel em in workoutGroups)
                workoutOrderItems.Add(em);
            CurrentLog.Instance.workoutOrderItems = workoutOrderItems;
            //WorkoutTemplateGroupModel addWorkoutOrderItem = new WorkoutTemplateGroupModel();
            //addWorkoutOrderItem.Id = -1;
            //addWorkoutOrderItem.IsSystemExercise = true;
            //addWorkoutOrderItem.Label = AppResources.CreateWorkoutsToCreateACustomProgram;
            //workoutOrderItems.Add(addWorkoutOrderItem);
        }
        catch (Exception ex)
        {

        }
    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }
    void OnCancelClicked(object sender, System.EventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], false);
            SetVisibility(s.Children[3], false);
            SetVisibility(s.Children[4], true);
        });
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], true);
            SetVisibility(s.Children[3], true);
            SetVisibility(s.Children[4], false);
        });
    }

    public async void OnEdit(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.CurrentWorkoutTemplate = m;
                await Navigation.PushAsync(new AddExercisesToWorkoutPage());
            }
            else
            {
                WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;
                CurrentLog.Instance.CurrentWorkoutTemplateGroup = m;

                await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage());
            }
        }
        catch (Exception ex)
        {

        }
    }
    public async void OnRename(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;

            CustomPromptConfig p = new CustomPromptConfig(string.Format("{0} \"{1}\"", AppResources.Rename.FirstCharToUpper(), m.Label),AppResources.EnterNewName,
            AppResources.Rename.FirstCharToUpper(),AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    RenameWorkoutTemplateAction(m,p.text);
                }
            };

            await Application.Current.MainPage.ShowPopupAsync(p);
            // PromptConfig p = new PromptConfig()
            // {
            //     InputType = InputType.Default,
            //     IsCancellable = true,
            //     Title = string.Format("{0} \"{1}\"", AppResources.Rename.FirstCharToUpper(), m.Label),
            //     Placeholder = AppResources.EnterNewName,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.Rename.FirstCharToUpper(),
            //     OnAction = new Action<PromptResult>((PromptResult response) =>
            //     {
            //         if (response.Ok)
            //         {
            //             RenameWorkoutTemplateAction(m, response.Text);
            //         }
            //     })
            // };

            // UserDialogs.Instance.Prompt(p);
        }
        else
        {
            WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;

            CustomPromptConfig p = new CustomPromptConfig(string.Format("{0} \"{1}\"", AppResources.Rename.FirstCharToUpper(), m.Label),AppResources.EnterNewName,
            AppResources.Rename.FirstCharToUpper(),AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    RenameWorkoutTemplateGroupAction(m,p.text);
                }
            };

            await Application.Current.MainPage.ShowPopupAsync(p);
            // PromptConfig p = new PromptConfig()
            // {
            //     InputType = InputType.Default,
            //     IsCancellable = true,
            //     Title = string.Format("{0} \"{1}\"", AppResources.Rename.FirstCharToUpper(), m.Label),
            //     Placeholder = AppResources.EnterNewName,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = AppResources.Rename.FirstCharToUpper(),
            //     OnAction = new Action<PromptResult>((PromptResult response) =>
            //     {
            //         if (response.Ok)
            //         {
            //             RenameWorkoutTemplateGroupAction(m, response.Text);
            //         }
            //     })
            // };

            // UserDialogs.Instance.Prompt(p);
        }

    }

    private async void RenameWorkoutTemplateGroupAction(WorkoutTemplateGroupModel model, string newLabel)
    {
        int itemIndex = workoutOrderItems.IndexOf(model);
        model.Label = newLabel;
        BooleanModel result = await DrMuscleRestClient.Instance.RenameWorkoutTemplateGroup(model);
        if (result.Result)
        {
            workoutOrderItems.RemoveAt(itemIndex);
            workoutOrderItems.Insert(itemIndex, model);
            //exerciseItems[itemIndex].Label = newLabel;
            CurrentLog.Instance.workoutOrderItems = workoutOrderItems;
            ProgramListView.ItemsSource = workoutOrderItems;
        }
    }

    private async void RenameWorkoutTemplateAction(WorkoutTemplateModel model, string newLabel)
    {
        int itemIndex = workoutItems.IndexOf(model);
        model.Label = newLabel;
        BooleanModel result = await DrMuscleRestClient.Instance.RenameWorkoutTemplate(model);
        if (result.Result)
        {
            workoutItems.RemoveAt(itemIndex);
            workoutItems.Insert(itemIndex, model);
            WorkoutListView.ItemsSource = workoutItems;
            //exerciseItems[itemIndex].Label = newLabel;
        }
    }
    public async void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi.CommandParameter is WorkoutTemplateModel)
            {
                WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
                CurrentLog.Instance.WorkoutTemplateSettings = m;

            }
            await Navigation.PushAsync(new WorkoutSettingsPage());

            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }

    public async void OnDelete(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        if (mi.CommandParameter is WorkoutTemplateModel)
        {
            WorkoutTemplateModel m = (WorkoutTemplateModel)mi.CommandParameter;
            var p = await HelperClass.DisplayCustomPopup(AppResources.DeleteWorkout,string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            AppResources.Delete,AppResources.Cancel);
            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    OnCancelClicked(sender, e);
                    DeleteWorkoutTemplateAction(m);
                }
            };

            // ConfirmConfig p = new ConfirmConfig()
            // {
            //     Title = AppResources.DeleteWorkout,
            //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            //     OkText = AppResources.Delete,
            //     CancelText = AppResources.Cancel,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // };
            // p.OnAction = (obj) =>
            // {
            //     if (obj)
            //     {
            //         OnCancelClicked(sender, e);
            //         DeleteWorkoutTemplateAction(m);
            //     }
            // };
            // UserDialogs.Instance.Confirm(p);
        }
        else
        {
            WorkoutTemplateGroupModel m = (WorkoutTemplateGroupModel)mi.CommandParameter;
            var p = await HelperClass.DisplayCustomPopup("Delete program",string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            AppResources.Delete,AppResources.Cancel);
            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                     DeleteWorkoutTemplateGroupAction(m);
                }
            };

            // ConfirmConfig p = new ConfirmConfig()
            // {
            //     Title = "Delete program",
            //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
            //     OkText = AppResources.Delete,
            //     CancelText = AppResources.Cancel,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // };
            // p.OnAction = (obj) =>
            // {
            //     if (obj)
            //     {
            //         DeleteWorkoutTemplateGroupAction(m);
            //     }
            // };
            // UserDialogs.Instance.Confirm(p);
        }
    }

    private async void DeleteWorkoutTemplateGroupAction(WorkoutTemplateGroupModel model)
    {
        int itemIndex = workoutOrderItems.IndexOf(model);


        try
        {
            if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id == model.Id)
            {
                BooleanModel successWorkoutLog = await DrMuscleRestClient.Instance.SaveWorkout(new SaveWorkoutModel() { WorkoutId = 108 });
                CurrentLog.Instance.CurrentWorkoutTemplate = null;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
            }
        }
        catch (Exception ex)
        {

        }
        BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutTemplateGroupModel(model);
        if (result.Result)
        {

            workoutOrderItems.RemoveAt(itemIndex);

            workoutGroups.Remove(model);
            UpdateWorkoutList();
            try
            {

                if (CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id == model.Id)
                {
                    await Navigation.PopToRootAsync();
                }

            }
            catch (Exception ex)
            {

            }
        }
    }

    private async void DeleteWorkoutTemplateAction(WorkoutTemplateModel model)
    {
        try
        {

            int itemIndex = workoutItems.IndexOf(model);

            await WorkoutIsInProgram(model);
            BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutTemplateModel(model);
            if (result.Result)
            {
                var objWorkout = workouts.Where(x => x.Id == model.Id).FirstOrDefault();
                if (objWorkout != null)
                    workouts.Remove(objWorkout);
                workoutItems.RemoveAt(itemIndex);

            }
            UpdateWorkoutList();
        }
        catch (Exception ex)
        {
            var properties = new Dictionary<string, string>
                    {
                        { "Delete_Worokout", $"{ex.StackTrace}" }
                    };
            Crashes.TrackError(ex, properties);
        }
    }

    private async Task WorkoutIsInProgram(WorkoutTemplateModel model)
    {
        try
        {
            foreach (var item in workoutGroups)
            {
                var newItem = item.WorkoutTemplates.Where(x => x.Id == model.Id).FirstOrDefault();
                if (newItem != null)
                {
                    item.WorkoutTemplates.Remove(newItem);
                    var workTemplateGroup = new WorkoutTemplateGroupModel()
                    {
                        Id = item.Id,
                        WorkoutTemplates = item.WorkoutTemplates
                    };
                    BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplateOrder(workTemplateGroup);
                }
            }
        }
        catch (Exception ex)
        {

        }
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        WorkoutTemplateModel m = (WorkoutTemplateModel)((BindableObject)sender).BindingContext;
        if (m.IsSystemExercise)
            ((Cell)sender).ContextActions.Clear();

    }

    private async void WorkoutListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (!App.IsV1UserTrial && !App.IsFreePlan)
            {
               if(await CanGoFurther(true))
               {
                    if (e.Item is WorkoutTemplateModel workoutTemplate)
                    {
                        if (workoutTemplate.Id != -1)
                        {
                            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                            // {
                            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                            //     Message = $"No exercise exist in {workoutTemplate.Label} workout.",
                            //     Title = AppResources.Error
                            // });

                            if (workoutTemplate.Exercises.Count == 0)
                            {
                                await HelperClass.DisplayCustomPopupForResult("",
                                $"No exercise exist in {workoutTemplate.Label} workout.", AppResources.Error, "");

                                return;
                            }

                            CurrentLog.Instance.CurrentWorkoutTemplate = workoutTemplate;
                            CurrentLog.Instance.WorkoutTemplateCurrentExercise = workoutTemplate.Exercises.First();
                            CurrentLog.Instance.WorkoutStarted = true;
                            try
                            {
                                var workoutModel = LocalDBManager.Instance.GetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                                if (!string.IsNullOrEmpty(workoutModel))
                                {
                                    var model = Newtonsoft.Json.JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
                                    CurrentLog.Instance.CurrentWorkoutTemplate = model;
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                var kenkoPage = new KenkoChooseYourWorkoutExercisePage();
                                App.ShowTopButtonWorkout = true;
                                kenkoPage.OnBeforeShow();
                                await Navigation.PushAsync(kenkoPage);
                            });
                        }
                        else
                        {
                            //AddMyOwnWorkout();
                        }
                    }
                    if (e.Item is WorkoutTemplateGroupModel)
                    {
                        if (((WorkoutTemplateGroupModel)e.Item).Id != -1)
                        {
                            CurrentLog.Instance.CurrentWorkoutTemplateGroup = (WorkoutTemplateGroupModel)e.Item;

                            await Navigation.PushAsync(new ChooseYourWorkoutTemplateInGroup());
                            //await PagesFactory.PushAsync<ChooseYourWorkoutTemplateInGroup>();
                        }
                        else
                        {
                            AddMyOwnWorkoutTemplateOrder();
                        }
                    }
                }
                else
                {

                    await Navigation.PushAsync(new SubscriptionPage());
                }
            }
            else if (App.IsV1UserTrial || App.IsFreePlan)
            {
                if (e.Item is WorkoutTemplateModel workoutTemplate)
                {
                    if (workoutTemplate.Id != -1)
                    {
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = $"No exercise exist in {workoutTemplate.Label} workout.",
                        //     Title = AppResources.Error
                        // });

                        if (workoutTemplate.Exercises.Count == 0)
                        {
                        await HelperClass.DisplayCustomPopupForResult("",
                        $"No exercise exist in {workoutTemplate.Label} workout.",AppResources.Error,"");

                            return;
                        }

                        CurrentLog.Instance.CurrentWorkoutTemplate = workoutTemplate;
                        CurrentLog.Instance.WorkoutTemplateCurrentExercise = workoutTemplate.Exercises.First();
                        CurrentLog.Instance.WorkoutStarted = true;
                        try
                        {
                            var workoutModel = LocalDBManager.Instance.GetDBSetting($"Workout{DateTime.Now.Date}{CurrentLog.Instance.CurrentWorkoutTemplate.Id}")?.Value;
                            if (!string.IsNullOrEmpty(workoutModel))
                            {
                                var model = Newtonsoft.Json.JsonConvert.DeserializeObject<WorkoutTemplateModel>(workoutModel);
                                CurrentLog.Instance.CurrentWorkoutTemplate = model;
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            var kenkoPage = new KenkoChooseYourWorkoutExercisePage();
                            App.ShowTopButtonWorkout = true;
                            kenkoPage.OnBeforeShow();
                            await Navigation.PushAsync(kenkoPage);
                        });
                    }
                    else
                    {
                        //AddMyOwnWorkout();
                    }
                }
                if (e.Item is WorkoutTemplateGroupModel)
                {
                    if (((WorkoutTemplateGroupModel)e.Item).Id != -1)
                    {
                        CurrentLog.Instance.CurrentWorkoutTemplateGroup = (WorkoutTemplateGroupModel)e.Item;

                        await Navigation.PushAsync(new ChooseYourWorkoutTemplateInGroup());
                        //await PagesFactory.PushAsync<ChooseYourWorkoutTemplateInGroup>();
                    }
                    else
                    {
                        AddMyOwnWorkoutTemplateOrder();
                    }
                }
            }
            else
            {

                await Navigation.PushAsync(new SubscriptionPage());
            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void AddMyOwnWorkoutTemplateOrder()
    {
        CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewProgram,AppResources.EnterProgramName,
            AppResources.Create,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutOrderAction(action,p.text);
                }
            };

        await Application.Current.MainPage.ShowPopupAsync(p);

        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewProgram,
        //     Placeholder = AppResources.EnterProgramName,
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutOrderAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    private async void AddMyOwnWorkout()
    {
        try {
         CustomPromptConfig p = new CustomPromptConfig(AppResources.CreateNewWorkout,AppResources.EnterWorkoutName,
            AppResources.Create,AppResources.Cancel,
            "");

            p.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    AddWorkoutAction(action,p.text);
                }
            };

            await Application.Current.MainPage.ShowPopupAsync(p);
        } catch(Exception ex) {
            await Utility.HelperClass.DisplayCustomPopup("error",ex.Message,"ok","");
        }
        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Default,
        //     IsCancellable = true,
        //     Title = AppResources.CreateNewWorkout,
        //     Placeholder = AppResources.EnterWorkoutName,
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddWorkoutAction)
        // };

        // UserDialogs.Instance.Prompt(p);
    }

    public async void TakeOrderLevel(int programid, string programName)
    {
        WorkoutTemplateGroupModel newWorkoutOrder = new WorkoutTemplateGroupModel()
        {
            Label = programName,
            Id = -1
        };


        if (programid == -1)
        {
            try
            {
                CurrentLog.Instance.CurrentWorkoutTemplateGroup = newWorkoutOrder;

                await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage()); return;
            }
            catch (Exception e)
            {
            }
        }
        else
        {
            try
            {
            CustomPromptConfig minRepsPopup = new CustomPromptConfig("","Enter level here",
            AppResources.Ok,AppResources.Cancel,
            $"How many workouts before switching to that program automatically?",Keyboard.Numeric,"",3);

            minRepsPopup.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                        if (string.IsNullOrWhiteSpace(minRepsPopup.text) || Convert.ToDecimal(minRepsPopup.text, CultureInfo.InvariantCulture) < 1)
                        {
                            TakeOrderLevel(programid, programName);
                            return;
                        }
                        int levels = Convert.ToInt32(minRepsPopup.text, CultureInfo.InvariantCulture);
                        if (levels >= 0)
                        {
                            newWorkoutOrder.NextProgramId = programid;
                            newWorkoutOrder.RequiredWorkoutToLevelUp = levels;
                            CurrentLog.Instance.CurrentWorkoutTemplateGroup = newWorkoutOrder;
                            await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage());
                            //await PagesFactory.PushAsync<>();
                        }
                }
            };

                await Application.Current.MainPage.ShowPopupAsync(minRepsPopup);
            // PromptConfig minRepsPopup = new PromptConfig()
            // {
            //     InputType = InputType.Number,
            //     IsCancellable = true,
            //     Title = "",
            //     Message = $"How many workouts before switching to that program automatically?",
            //     Placeholder = "Enter level here",
            //     OkText = AppResources.Ok,
            //     MaxLength = 3,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OnAction = async (ageResponse) =>
            //     {
            //         if (ageResponse.Ok)
            //         {
            //             if (string.IsNullOrWhiteSpace(ageResponse.Value) || Convert.ToDecimal(ageResponse.Value, CultureInfo.InvariantCulture) < 1)
            //             {
            //                 TakeOrderLevel(programid, programName);
            //                 return;
            //             }
            //             int levels = Convert.ToInt32(ageResponse.Value, CultureInfo.InvariantCulture);
            //             if (levels >= 0)
            //             {
            //                 newWorkoutOrder.NextProgramId = programid;
            //                 newWorkoutOrder.RequiredWorkoutToLevelUp = levels;
            //                 CurrentLog.Instance.CurrentWorkoutTemplateGroup = newWorkoutOrder;
            //                 await Navigation.PushAsync(new AddWorkoutToWorkoutOrderPage());
            //                 //await PagesFactory.PushAsync<>();
            //             }
            //         }
            //     }
            // };
            // minRepsPopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            // UserDialogs.Instance.Prompt(minRepsPopup);
            }
            catch (Exception ex)
            {

            }
        }
    }

    protected void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:)?$";
        bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    private async void AddWorkoutOrderAction(PopupAction response,string Text)
    {

        if (response == PopupAction.OK)
        {
            if (workoutOrderItems.Count > 0)
            {
                popup = new ProgramListPopup();
                if (popup != null)
                {
                    popup.workoutOrderItems = new ObservableCollection<WorkoutTemplateGroupModel>(this.workoutOrderItems);
                    popup._chooseYourCustomWorkout = this;
                    popup.ProgramName = Text;
                    popup.setDataSource();
                    await PopupNavigation.Instance.PushAsync(popup);
                }
                return;
            }
            else
            {
                TakeOrderLevel(-1, Text);
            }


        }
    }


    async void GetMinReps(WorkoutTemplateModel newWorkout)
    {
        
        CustomPromptConfig minRepsPopup = new CustomPromptConfig("","Enter reps here",
            AppResources.Ok,"",
            $"Enter min reps",Keyboard.Numeric,"",3,false);

            minRepsPopup.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                        if (string.IsNullOrWhiteSpace(minRepsPopup.text) || Convert.ToDecimal(minRepsPopup.text, CultureInfo.InvariantCulture) < 1)
                        {
                           GetMinReps(newWorkout);
                            return;
                        }
                        int reps = Convert.ToInt32(minRepsPopup.text, CultureInfo.InvariantCulture);
                if (reps < 5)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig() { AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray), 
                    // Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5, Title = AppResources.LessThan5Reps });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                        AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"OK","");
                    GetMinReps(newWorkout);
                    return;
                }
                if (reps > 30)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    //     Title = AppResources.MoreThan30Reps
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
                        AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,"OK","");
                    GetMinReps(newWorkout);
                    return;
                }
                newWorkout.WorkoutSettingsModel.RepsMinValue = reps;
                GetMaxReps(newWorkout);
             }
            };

            await Application.Current.MainPage.ShowPopupAsync(minRepsPopup);

        // PromptConfig minRepsPopup = new PromptConfig()
        // {
        //     InputType = InputType.Number,
        //     IsCancellable = false,
        //     Title = "",
        //     Message = $"Enter min reps",
        //     Placeholder = "Enter reps here",
        //     OkText = AppResources.Ok,
        //     MaxLength = 3,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (ageResponse) =>
        //     {
        //         if (string.IsNullOrWhiteSpace(ageResponse.Value) || Convert.ToDecimal(ageResponse.Value, CultureInfo.InvariantCulture) < 1)
        //         {
        //             GetMinReps(newWorkout);
        //             return;
        //         }
        //         int reps = Convert.ToInt32(ageResponse.Value, CultureInfo.InvariantCulture);
        //         if (reps < 5)
        //         {
        //             // await UserDialogs.Instance.AlertAsync(new AlertConfig() { AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray), 
        //             // Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5, Title = AppResources.LessThan5Reps });

        //             await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
        //                 AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"OK","");
        //             GetMinReps(newWorkout);
        //             return;
        //         }
        //         if (reps > 30)
        //         {
        //             // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        //             // {
        //             //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //             //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
        //             //     Title = AppResources.MoreThan30Reps
        //             // });

        //             await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
        //                 AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,"OK","");
        //             GetMinReps(newWorkout);
        //             return;
        //         }
        //         newWorkout.WorkoutSettingsModel.RepsMinValue = reps;
        //         GetMaxReps(newWorkout);
        //     }
        // };
        // minRepsPopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(minRepsPopup);
    }

    async void GetMaxReps(WorkoutTemplateModel newWorkout)
    {
        
        CustomPromptConfig minRepsPopup = new CustomPromptConfig("","Enter reps here",
            AppResources.Ok,"",
            $"Enter max reps",Keyboard.Numeric,"",3,false);

            minRepsPopup.ActionSelected += async (sender,action) => {
                if(action == PopupAction.OK){
                        if (string.IsNullOrWhiteSpace(minRepsPopup.text) || Convert.ToDecimal(minRepsPopup.text, CultureInfo.InvariantCulture) < 1)
                        {
                           GetMaxReps(newWorkout);
                            return;
                        }
                        int reps = Convert.ToInt32(minRepsPopup.text, CultureInfo.InvariantCulture);
                if (reps < 5)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //     Title = AppResources.LessThan5Reps
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                        AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"OK","");
                    GetMaxReps(newWorkout);
                    return;
                }
                if (reps > 30)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    //     Title = AppResources.MoreThan30Reps
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
                        AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,"OK","");
                    GetMaxReps(newWorkout);
                    return;
                }
                
                newWorkout.WorkoutSettingsModel.RepsMaxValue = reps;
                newWorkout.WorkoutSettingsModel.IsCustomReps = true;
                CurrentLog.Instance.CurrentWorkoutTemplate = newWorkout;
                CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                CurrentLog.Instance.WorkoutStarted = false;
                await Navigation.PushAsync(new AddExercisesToWorkoutPage());
             }
            };

        await Application.Current.MainPage.ShowPopupAsync(minRepsPopup);

        // PromptConfig minRepsPopup = new PromptConfig()
        // {
        //     InputType = InputType.Number,
        //     IsCancellable = false,
        //     Title = "",
        //     Message = $"Enter max reps",
        //     Placeholder = "Enter reps here",
        //     OkText = AppResources.Ok,
        //     MaxLength = 3,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (ageResponse) =>
        //     {
        //         if (string.IsNullOrWhiteSpace(ageResponse.Value) || Convert.ToDecimal(ageResponse.Value, CultureInfo.InvariantCulture) < 1)
        //         {
        //             GetMaxReps(newWorkout);
        //             return;
        //         }
        //         int reps = Convert.ToInt32(ageResponse.Value, CultureInfo.InvariantCulture);
        //         if (reps < 5)
        //         {
        //             // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        //             // {
        //             //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //             //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
        //             //     Title = AppResources.LessThan5Reps
        //             // });

        //             await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
        //                 AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,"OK","");
        //             GetMaxReps(newWorkout);
        //             return;
        //         }
        //         if (reps > 30)
        //         {
        //             // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        //             // {
        //             //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //             //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
        //             //     Title = AppResources.MoreThan30Reps
        //             // });

        //             await HelperClass.DisplayCustomPopupForResult(AppResources.MoreThan30Reps,
        //                 AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,"OK","");
        //             GetMaxReps(newWorkout);
        //             return;
        //         }
        //         if (newWorkout.WorkoutSettingsModel.RepsMinValue >= reps)
        //         {

        //             // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        //             // {
        //             //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //             //     Message = "Max reps is not greater then min reps"

        //             // });

        //             await HelperClass.DisplayCustomPopupForResult("",
        //                 "Max reps is not greater then min reps","OK","");
        //             GetMaxReps(newWorkout);
        //             return;
        //         }
        //         newWorkout.WorkoutSettingsModel.RepsMaxValue = reps;
        //         newWorkout.WorkoutSettingsModel.IsCustomReps = true;
        //         CurrentLog.Instance.CurrentWorkoutTemplate = newWorkout;
        //         CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
        //         CurrentLog.Instance.WorkoutStarted = false;
        //         await Navigation.PushAsync(new AddExercisesToWorkoutPage());
        //     }
        // };
        // minRepsPopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(minRepsPopup);
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        ActionStack.IsVisible = false;
    }
    private async void AddWorkoutAction(PopupAction response,string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                WorkoutTemplateModel newWorkout = new WorkoutTemplateModel()
                {
                    Label = Text,
                    Id = -1
                };
                /*await DrMuscleRestClient.Instance.CreateNewWorkoutTemplate(new AddUserWorkoutTemplateModel()
            {
                WorkoutLabel = response.Text
            });
            */
                //Ask for custom reps

                var isConfirm = await HelperClass.DisplayCustomPopupForResult("","Use custom reps?","Custom reps", AppResources.Cancel);


                // ConfirmConfig CustomRepsPopUp = new ConfirmConfig()
                // {
                //     Message = "Use custom reps?",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Custom reps",
                //     CancelText = AppResources.Cancel,
                // };

                // var isConfirm = await UserDialogs.Instance.ConfirmAsync(CustomRepsPopUp);
                if (isConfirm == PopupAction.OK)
                {

                    newWorkout.WorkoutSettingsModel = new WorkoutTemplateSettingsModel();
                    GetMinReps(newWorkout);
                }
                else
                {
                    CurrentLog.Instance.CurrentWorkoutTemplate = newWorkout;
                    CurrentLog.Instance.WorkoutTemplateCurrentExercise = null;
                    CurrentLog.Instance.WorkoutStarted = false;
                    await Navigation.PushAsync(new AddExercisesToWorkoutPage());
                }

            }
            catch (Exception e)
            {

            }
        }
    }

    private async void WorkoutsButton_Clicked(object sender, EventArgs e)
    {
        PlusIcon2.IsVisible = true;
        PlusIcon.IsVisible = true;
        ActionStack.IsVisible = false;
        AddMyOwnWorkout();
    }

    private async void ProgramButton_Clicked(object sender, EventArgs e)
    {
        PlusIcon2.IsVisible = true;
        PlusIcon.IsVisible = true;
        ActionStack.IsVisible = false;
        AddMyOwnWorkoutTemplateOrder();
    }

    private async void UploadButton_Clicked(object sender, EventArgs e)
    {
        PlusIcon2.IsVisible = true;
        PlusIcon.IsVisible = true;
        ActionStack.IsVisible = false;
        Browser.OpenAsync(new Uri("https://my.dr-muscle.com"));
    }

    public async Task<bool> CanGoFurther(bool isLoader = false)
    {
        try
        {
            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("creation_date").Value))
            {
                DateTime setDate = DateTime.Now.ToUniversalTime();
                LocalDBManager.Instance.SetDBSetting("creation_date", setDate.Ticks.ToString());
                DrMuscleRestClient.Instance.SetUserCreationDate(setDate);
            }
            try
            {
                DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                {
                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                    App.IsV1UserTrial = true;
                    App.IsFreePlan = false;
                    // Uncomment code please
                    //SetTrialUserNotifications();
                    await DrMuscleRestClient.Instance.IsV1UserWithoutLoader();
                    return true;
                }
            }
            catch (Exception ex)
            {

            }
            BooleanModel isV1User = isLoader ? await DrMuscleRestClient.Instance.IsV1User() : await DrMuscleRestClient.Instance.IsV1UserWithoutLoader();

            if (isV1User != null)
            {
                App.IsMealPlan = isV1User.IsMealPlan;
                DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                {
                    App.IsV1UserTrial = true;
                    App.IsFreePlan = false;
                    App.IsMealPlan = isV1User.IsMealPlan;


                    LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", isV1User.IsMealPlan ? "true" : "false");

                    App.IsTraining = true;
                    return true;
                }
                else if (!isV1User.IsTraining)
                {
                    App.IsFreePlan = true;
                    App.IsV1UserTrial = false;
                    App.IsV1User = false;
                    App.IsTraining = false;
                    App.IsMealPlan = isV1User.IsMealPlan;
                    LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", isV1User.IsMealPlan ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "false");
                }
                if (isV1User.Result)
                {
                    App.IsV1UserTrial = isV1User.IsTraining;
                    //App.IsV1User = isV1User.IsTraining;
                    App.IsV1User = isV1User.IsTraining;
                    App.IsMealPlan = isV1User.IsMealPlan;
                    App.IsFreePlan = !isV1User.IsTraining;
                    LocalDBManager.Instance.SetDBSetting("IsMealPlanPurchased", isV1User.IsMealPlan ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");

                    if (App.IsV1User)
                        return true;
                }


            }
        }
        catch (Exception ex)
        {
            return false;
        }
        return false;
    }

    private async void CreateWorkoutButton_Clicked(object sender, EventArgs e)
    {
        ActionStack.IsVisible = false;
        PlusIcon.IsVisible = true;
    }

    void NewTapped(object sender, System.EventArgs e)
    {
        //AddMyOwnExercise();
        PlusIcon.IsVisible = !PlusIcon.IsVisible;
        ActionStack.IsVisible = !ActionStack.IsVisible;
    }

    void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
    {
        CurrentLog.Instance.IsChangeProgram = false;

        LblMyWorkouts.IsVisible = true;
        workoutGrid.IsVisible = true;
  //      createWorkoutButton.IsVisible = false;
  //      PlusIcon2.IsVisible = true;
  //      PlusIcon.IsVisible = true;
   //     createWorkoutButton.IsVisible = false;
    }
}

