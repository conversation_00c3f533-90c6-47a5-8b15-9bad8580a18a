# .github/workflows/maui-build.yml
name: MAUI Build and Sign

# Required Secrets:
# SENTRY_AUTH_TOKEN: For Sentry CLI authentication.
# ANDROID_KEYSTORE_BASE64: Base64 encoded Android keystore file.
# ANDROID_KEYSTORE_PASSWORD: Password for the Android keystore.
# ANDROID_KEY_ALIAS: Alias of the key within the Android keystore.
# ANDROID_KEY_PASSWORD: Password for the key within the Android keystore.
# IOS_P12_BASE64: Base64 encoded iOS .p12 certificate file.
# IOS_P12_PASSWORD: Password for the iOS .p12 file.
# IOS_PROVISIONING_PROFILE_BASE64: Base64 encoded iOS .mobileprovision file.
# GOOGLE_SERVICE_JSON_BASE64: Base64 encoded Google Play service account JSON key (Optional, for deployment).
#
# Optional Deployment Secrets:
# APPSTORE_API_PRIVATE_KEY: Base64 encoded App Store Connect API Key (.p8 file).
# APPSTORE_API_KEY_ID: Key ID for the App Store Connect API Key.
# APPSTORE_ISSUER_ID: Issuer ID for the App Store Connect API Key.

on:
  workflow_dispatch: # Allows manual triggering from the GitHub Actions UI
    inputs:
      version_name:
        description: 'Version name (e.g., 1.2.3)'
        required: true
        default: '1.0.0'
      version_code:
        description: 'Version code (integer as string, e.g., 10203)'
        required: true
        default: '10000'

concurrency: # Prevents multiple runs for the same ref from overlapping
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # ==================================
  # ==       ANDROID BUILD JOB      ==
  # ==================================
  build-android:
    name: Build Android App
    runs-on: ubuntu-latest # Use a Linux runner for Android builds
    timeout-minutes: 45 # Set a timeout for the job

    steps:
      # ----------------------------------
      # --        Initial Setup         --
      # ----------------------------------
      - name: Checkout repository
        uses: actions/checkout@v4 # Get the source code

      # ----------------------------------
      # --       Sentry Integration     --
      # ----------------------------------
      - name: Install Sentry CLI
        run: |
          # Download and install the Sentry command-line tool
          curl -sL https://sentry.io/get-cli/ | bash
          echo "Sentry CLI installed successfully"

      - name: Authenticate with Sentry
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }} # Use the secret token
        run: |
          # Verify the token is present and log in to Sentry
          if [ -z "$SENTRY_AUTH_TOKEN" ]; then
            echo "❌ SENTRY_AUTH_TOKEN is not set!"
            # Decide if this is fatal - exiting might be too strict if Sentry isn't critical
            # exit 1
            echo "⚠️ SENTRY_AUTH_TOKEN not set, skipping Sentry login."
          else
             sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"
             echo "✅ Sentry CLI authenticated."
          fi

      # ----------------------------------
      # --     Environment Setup        --
      # ----------------------------------
      - name: Setup Java JDK
        uses: actions/setup-java@v4 # Install Java, required for Android builds
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup .NET 8 SDK
        uses: actions/setup-dotnet@v4 # Install the specified .NET SDK version
        with:
          dotnet-version: 8.0.x

      # ----------------------------------
      # --   Credential Setup & Validation --
      # ----------------------------------
      - name: Setup Android Keystore File from Secret
        id: setup_keystore_file
        run: |
          # Decode the base64 encoded keystore from secrets and save it as a file
          if [ -z "${{ secrets.ANDROID_KEYSTORE_BASE64 }}" ]; then
            echo "❌ ANDROID_KEYSTORE_BASE64 secret is not set!"
            echo "keystore_present=false" >> $GITHUB_OUTPUT
            exit 1 # Keystore is essential for signing, fail early
          else
            echo "Setting up Android Keystore..."
            echo "${{ secrets.ANDROID_KEYSTORE_BASE64 }}" | base64 --decode > signing.keystore
            echo "Keystore file created: signing.keystore"
            ls -l signing.keystore # Verify the file was created
            echo "keystore_present=true" >> $GITHUB_OUTPUT
            echo "keystore_path=$(pwd)/signing.keystore" >> $GITHUB_OUTPUT
          fi

      - name: Validate Android Keystore Access
        id: validate_keystore
        if: steps.setup_keystore_file.outputs.keystore_present == 'true'
        run: |
          # Validate that the keystore can be accessed with the provided alias and passwords
          keystorePath="${{ steps.setup_keystore_file.outputs.keystore_path }}"
          keyAlias="${{ secrets.ANDROID_KEY_ALIAS }}"
          storePassword="${{ secrets.ANDROID_KEYSTORE_PASSWORD }}"
          keyPassword="${{ secrets.ANDROID_KEY_PASSWORD }}"
          keystore_valid=false

          if [ -z "$keyAlias" ] || [ -z "$storePassword" ] || [ -z "$keyPassword" ]; then
             echo "❌ One or more Android signing secrets (ALIAS, KEYSTORE_PASSWORD, KEY_PASSWORD) are missing!"
          elif keytool -list -v -keystore "$keystorePath" -alias "$keyAlias" -storepass "$storePassword" -keypass "$keyPassword" > /dev/null 2>&1; then
             echo "✅ Keystore validation successful with provided secrets."
             keystore_valid=true
          else
             echo "❌ Keystore validation FAILED. Could not access alias '$keyAlias' with provided passwords."
             # Optional: Add more detailed keytool error logging here if needed
          fi
          echo "keystore_valid=$keystore_valid" >> $GITHUB_OUTPUT

      - name: Validate Google Play Service Account JSON
        id: validate_gplay
        run: |
          # Check if the Google Play service account JSON secret is available and appears valid
          service_account_valid=false
          if [ -z "${{ secrets.GOOGLE_SERVICE_JSON_BASE64 }}" ]; then
            echo "ℹ️ GOOGLE_SERVICE_JSON_BASE64 secret not set. Skipping validation (Deployment will not be possible)."
          else
            echo "Validating Google Play Service Account JSON..."
            mkdir -p google-play
            echo "${{ secrets.GOOGLE_SERVICE_JSON_BASE64 }}" | base64 --decode > google-play/service-account.json

            # Basic validation: Check if file exists and is not empty
            if [ -s google-play/service-account.json ]; then
               # Deeper validation: Check if it's valid JSON and contains required fields
               if jq -e 'has("type") and has("project_id") and has("private_key_id") and has("private_key") and has("client_email") and has("client_id")' google-play/service-account.json > /dev/null 2>&1; then
                  echo "✅ Google Play Service Account JSON appears valid."
                  service_account_valid=true
               else
                  echo "❌ Google Play Service Account JSON is invalid or missing required fields."
                  # Optional: Display first few lines for debugging (mask sensitive data if needed)
                  # head -n 3 google-play/service-account.json
               fi
            else
               echo "❌ Failed to decode or save Google Play Service Account JSON."
            fi
          fi
          echo "service_account_valid=$service_account_valid" >> $GITHUB_OUTPUT

      - name: Validation Summary Output
        run: |
          # Output a summary of the credential validation results to the GitHub Actions summary page
          echo "### 🔑 Android Credential Validation Results" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.validate_keystore.outputs.keystore_valid }}" = "true" ]; then
            echo "- **Keystore:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Keystore:** ❌ Invalid or Access Denied" >> $GITHUB_STEP_SUMMARY
          fi
          if [ -n "${{ secrets.GOOGLE_SERVICE_JSON_BASE64 }}" ]; then
             if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
               echo "- **Google Play Service Account:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
             else
               echo "- **Google Play Service Account:** ❌ Invalid" >> $GITHUB_STEP_SUMMARY
             fi
          else
             echo "- **Google Play Service Account:** ℹ️ Not Provided" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Check if Build Can Proceed
        id: check_credentials
        run: |
          # Determine if the essential credentials (keystore) are valid to allow the build
          build_allowed="false"
          deploy_allowed="false"
          if [ "${{ steps.validate_keystore.outputs.keystore_valid }}" = "true" ]; then
            echo "✅ Keystore is valid. Build can proceed."
            build_allowed="true"
            # Check if deployment is possible (requires valid keystore AND valid service account)
            if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
               echo "✅ Google Play Service Account is valid. Deployment can proceed."
               deploy_allowed="true"
            else
               echo "⚠️ Google Play Service Account is invalid or missing. Build will proceed, but deployment will be skipped."
            fi
          else
            echo "❌ Keystore is invalid. Build cannot proceed."
            # Fail the workflow explicitly if keystore is invalid
            exit 1
          fi
          echo "build_allowed=$build_allowed" >> $GITHUB_OUTPUT
          echo "deploy_allowed=$deploy_allowed" >> $GITHUB_OUTPUT

      # ----------------------------------
      # --       Caching Setup          --
      # ----------------------------------
      - name: Cache .NET MAUI Workloads
        id: cache-dotnet-workloads
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        uses: actions/cache@v4 # Cache installed .NET workloads to speed up subsequent runs
        with:
          path: ~/.dotnet/workload
          key: ${{ runner.os }}-dotnet-workloads-${{ hashFiles('**/DrMaxMuscle.csproj') }} # Cache key based on OS and project file hash
          restore-keys: |
            ${{ runner.os }}-dotnet-workloads-

      - name: Cache NuGet Packages
        id: cache-nuget
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        uses: actions/cache@v4 # Cache downloaded NuGet packages
        with:
          path: ~/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/packages.lock.json') }} # Cache key based on OS and lock file hash
          restore-keys: |
            ${{ runner.os }}-nuget-

      # ----------------------------------
      # --   MAUI & Dependencies        --
      # ----------------------------------
      - name: Install .NET MAUI Android Workload
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        run: dotnet workload install maui-android --skip-manifest-update # Install the specific MAUI workload for Android

      - name: Restore NuGet Dependencies
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        run: dotnet restore DrMaxMuscle/DrMaxMuscle.csproj # Restore packages defined in the project file (adjust path if needed)

      # ----------------------------------
      # -- Versioning & Manifest Update --
      # ----------------------------------
      - name: Determine Version Info
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        id: version
        run: |
          # Use the version name and code provided via workflow_dispatch inputs
          echo "Using version name from input: ${{ github.event.inputs.version_name }}"
          echo "Using version code from input: ${{ github.event.inputs.version_code }}"
          echo "version_name=${{ github.event.inputs.version_name }}" >> $GITHUB_OUTPUT
          echo "version_code=${{ github.event.inputs.version_code }}" >> $GITHUB_OUTPUT
          # Basic validation for version code (should be integer)
          if ! [[ "${{ github.event.inputs.version_code }}" =~ ^[0-9]+$ ]]; then
             echo "❌ Invalid version_code input: Must be an integer."
             exit 1
          fi

      - name: Update AndroidManifest.xml and csproj
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        run: |
          # Update version code/name in AndroidManifest.xml and the main csproj file
          VERSION_CODE="${{ steps.version.outputs.version_code }}"
          VERSION_NAME="${{ steps.version.outputs.version_name }}"
          MANIFEST_PATH="DrMaxMuscle/Platforms/Android/AndroidManifest.xml" # Adjust path if needed
          CSPROJ_PATH="DrMaxMuscle/DrMaxMuscle.csproj" # Adjust path if needed

          echo "Updating $MANIFEST_PATH with VersionCode=$VERSION_CODE, VersionName=$VERSION_NAME"
          if [ -f "$MANIFEST_PATH" ]; then
             # Use simpler sed syntax compatible with Linux/macOS sed versions
             sed -i.bak "s/android:versionCode=\"[^\"]*\"/android:versionCode=\"$VERSION_CODE\"/" "$MANIFEST_PATH"
             sed -i.bak "s/android:versionName=\"[^\"]*\"/android:versionName=\"$VERSION_NAME\"/" "$MANIFEST_PATH"
             rm "${MANIFEST_PATH}.bak" # Remove backup file
             echo "✅ Updated AndroidManifest.xml"
          else
             echo "⚠️ AndroidManifest.xml not found at $MANIFEST_PATH"
          fi

          echo "Updating $CSPROJ_PATH with ApplicationVersion=$VERSION_CODE, ApplicationDisplayVersion=$VERSION_NAME"
           if [ -f "$CSPROJ_PATH" ]; then
             # Use simpler sed syntax
             sed -i.bak "s|<ApplicationVersion>[^<]*</ApplicationVersion>|<ApplicationVersion>$VERSION_CODE</ApplicationVersion>|" "$CSPROJ_PATH"
             sed -i.bak "s|<ApplicationDisplayVersion>[^<]*</ApplicationDisplayVersion>|<ApplicationDisplayVersion>$VERSION_NAME</ApplicationDisplayVersion>|" "$CSPROJ_PATH"
             rm "${CSPROJ_PATH}.bak" # Remove backup file
             echo "✅ Updated $CSPROJ_PATH"
          else
             echo "⚠️ Project file not found at $CSPROJ_PATH"
          fi

      # ----------------------------------
      # --     Build Application        --
      # ----------------------------------
      - name: Build and Sign Android App (AAB)
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if credentials are ok
        run: |
          # Publish the Android project in Release configuration, creating a signed AAB
          # Version properties are now read from the updated csproj file during the build
          dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-android -c Release \
            /p:AndroidPackageFormat=aab \
            /p:AndroidKeyStore=True \
            /p:AndroidSigningKeyStore=${{ steps.setup_keystore_file.outputs.keystore_path }} \
            /p:AndroidSigningKeyAlias=${{ secrets.ANDROID_KEY_ALIAS }} \
            /p:AndroidSigningStorePass=${{ secrets.ANDROID_KEYSTORE_PASSWORD }} \
            /p:AndroidSigningKeyPass=${{ secrets.ANDROID_KEY_PASSWORD }}
        env:
          # Pass passwords as env vars if preferred over command line args
          ANDROID_SIGNING_STORE_PASS: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          ANDROID_SIGNING_KEY_PASS: ${{ secrets.ANDROID_KEY_PASSWORD }}

      # ----------------------------------
      # --  Post-Build Tests & Verify   --
      # ----------------------------------
      - name: Find AAB file Path
        if: steps.check_credentials.outputs.build_allowed == 'true' # Only run if build was attempted
        id: find_aab
        run: |
          # Find the generated AAB file in the expected output directory
          AAB_PATH=$(find DrMaxMuscle/bin/Release/net8.0-android/publish/ -name '*.aab' -type f | head -n 1)
          if [ -z "$AAB_PATH" ]; then
            echo "❌ AAB file not found after build!"
            echo "aab_found=false" >> $GITHUB_OUTPUT
            # Decide if this should fail the job immediately
            # exit 1
          else
            echo "✅ AAB file found: $AAB_PATH"
            echo "aab_path=$AAB_PATH" >> $GITHUB_OUTPUT
            echo "aab_found=true" >> $GITHUB_OUTPUT
          fi

      - name: Run Basic Automated Sanity Checks
        if: steps.find_aab.outputs.aab_found == 'true' # Only run if AAB was found
        id: run_tests
        run: |
          # Perform basic checks on the generated AAB
          tests_passed="true"
          echo "Running basic automated tests..."

          # 1. Check AAB file size (simple check for non-empty file)
          AAB_PATH="${{ steps.find_aab.outputs.aab_path }}"
          FILE_SIZE=$(stat -c%s "$AAB_PATH")
          if [ "$FILE_SIZE" -lt 100000 ]; then # Check if size is suspiciously small (e.g., < 100KB)
             echo "⚠️ WARNING: AAB file size ($FILE_SIZE bytes) seems small."
             # tests_passed="false" # Decide if this is a failure condition
          else
             echo "✅ AAB file size ($FILE_SIZE bytes) looks reasonable."
          fi

          # Add more checks here if needed (e.g., check manifest content within AAB using bundletool if installed)

          echo "tests_passed=$tests_passed" >> $GITHUB_OUTPUT
          if [ "$tests_passed" = "true" ]; then
             echo "✅ Basic sanity checks passed."
          else
             echo "❌ Basic sanity checks failed."
             # exit 1 # Decide if failed tests should stop the workflow
          fi

      - name: Verify AAB Version Code (using bundletool)
        if: steps.run_tests.outputs.tests_passed == 'true' # Only run if basic tests passed
        id: verify_aab_version
        run: |
          # Install bundletool and verify the version code inside the AAB matches the input
          echo "Verifying AAB version code..."
          AAB_PATH="${{ steps.find_aab.outputs.aab_path }}"
          EXPECTED_VERSION_CODE="${{ steps.version.outputs.version_code }}"
          version_verified="false"

          # Install bundletool (consider caching this if used frequently)
          curl -sL -o bundletool.jar https://github.com/google/bundletool/releases/download/1.15.6/bundletool-all-1.15.6.jar

          # Extract manifest info using bundletool
          # Redirect stderr to stdout to capture potential errors
          MANIFEST_DUMP=$(java -jar bundletool.jar dump manifest --bundle="$AAB_PATH" 2>&1)

          if [ $? -eq 0 ]; then
             # Extract version code using grep/sed
             ACTUAL_VERSION_CODE=$(echo "$MANIFEST_DUMP" | grep -o 'android:versionCode="[0-9]*"' | sed 's/android:versionCode="\([0-9]*\)"/\1/')

             if [ -n "$ACTUAL_VERSION_CODE" ]; then
                echo "Expected Version Code: $EXPECTED_VERSION_CODE"
                echo "Actual Version Code in AAB: $ACTUAL_VERSION_CODE"
                if [ "$ACTUAL_VERSION_CODE" = "$EXPECTED_VERSION_CODE" ]; then
                   echo "✅ AAB version code matches expected value."
                   version_verified="true"
                else
                   echo "❌ ERROR: AAB version code ($ACTUAL_VERSION_CODE) does NOT match expected value ($EXPECTED_VERSION_CODE)!"
                   # exit 1 # Fail the job due to version mismatch
                fi
             else
                echo "❌ ERROR: Could not extract versionCode from bundletool manifest dump."
                echo "$MANIFEST_DUMP" # Print dump for debugging
             fi
          else
             echo "❌ ERROR: bundletool command failed."
             echo "$MANIFEST_DUMP" # Print error output
          fi
          echo "version_verified=$version_verified" >> $GITHUB_OUTPUT


      # ----------------------------------
      # --     Upload Artifact          --
      # ----------------------------------
      - name: Upload Android AAB Artifact
        # Only upload if build succeeded, AAB was found, and tests passed
        if: steps.check_credentials.outputs.build_allowed == 'true' && steps.find_aab.outputs.aab_found == 'true' && steps.run_tests.outputs.tests_passed == 'true' && steps.verify_aab_version.outputs.version_verified == 'true'
        uses: actions/upload-artifact@v4 # Upload the AAB as a workflow artifact
        with:
          name: android-aab-v${{ steps.version.outputs.version_name }}-${{ steps.version.outputs.version_code }} # Name the artifact with version info
          path: ${{ steps.find_aab.outputs.aab_path }} # Use the path found previously
          if-no-files-found: error # Ensure the upload step fails if the file isn't there

      # ----------------------------------
      # -- Optional: Google Play Upload --
      # ----------------------------------
      - name: Upload to Play Store Internal Testing
        # Only run if build succeeded, tests passed, AND deployment is allowed (valid keystore + valid service account)
        if: steps.check_credentials.outputs.deploy_allowed == 'true' && steps.find_aab.outputs.aab_found == 'true' && steps.run_tests.outputs.tests_passed == 'true' && steps.verify_aab_version.outputs.version_verified == 'true'
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJson: google-play/service-account.json # Path to the decoded JSON key
          packageName: com.drmaxmuscle.dr_max_muscle # IMPORTANT: Change to your actual package name
          releaseFiles: ${{ steps.find_aab.outputs.aab_path }}
          track: internal # Or alpha, beta, production
          status: completed # Or draft

      # ----------------------------------
      # --    Final Summary Output      --
      # ----------------------------------
      - name: Output Final Build Summary
        if: always() # Always run this step to provide summary regardless of success/failure
        run: |
          # Append final status to the GitHub Actions summary page
          echo "### 📱 Android Build Final Status" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.check_credentials.outputs.build_allowed }}" != "true" ]; then
             echo "- **Status:** ❌ Build Skipped (Invalid Keystore)" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.find_aab.outputs.aab_found }}" != "true" ]; then
             echo "- **Status:** ❌ Build Failed (AAB not found)" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.run_tests.outputs.tests_passed }}" != "true" ]; then
             echo "- **Status:** ❌ Build Failed (Sanity Checks Failed)" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.verify_aab_version.outputs.version_verified }}" != "true" ]; then
             echo "- **Status:** ❌ Build Failed (Version Code Mismatch)" >> $GITHUB_STEP_SUMMARY
          else
             echo "- **Status:** ✅ Build Successful" >> $GITHUB_STEP_SUMMARY
             echo "- **Version:** ${{ steps.version.outputs.version_name }} (Code: ${{ steps.version.outputs.version_code }})" >> $GITHUB_STEP_SUMMARY
             echo "- **Artifact:** \`android-aab-v${{ steps.version.outputs.version_name }}-${{ steps.version.outputs.version_code }}\` uploaded" >> $GITHUB_STEP_SUMMARY
             if [ "${{ steps.check_credentials.outputs.deploy_allowed }}" == "true" ]; then
                # Check job status of the upload step if it exists and ran
                # This requires giving the upload step an ID, e.g., id: upload_gplay
                # echo "- **Deployment:** ✅ Uploaded to Google Play (Internal)" >> $GITHUB_STEP_SUMMARY
                echo "- **Deployment:** Attempted upload to Google Play (Internal)" >> $GITHUB_STEP_SUMMARY # Safer message without checking job status
             else
                echo "- **Deployment:** ⚠️ Skipped (Invalid Google Play credentials or Keystore)" >> $GITHUB_STEP_SUMMARY
             fi
          fi


  # ==================================
  # ==         iOS BUILD JOB        ==
  # ==================================
  build-ios:
    name: Build iOS App
    runs-on: macos-14 # Use a macOS runner for iOS builds (adjust version if needed)
    timeout-minutes: 45 # Set a timeout for the job

    steps:
      # ----------------------------------
      # --        Initial Setup         --
      # ----------------------------------
      - name: Checkout repository
        uses: actions/checkout@v4 # Get the source code

      # ----------------------------------
      # --       Sentry Integration     --
      # ----------------------------------
      - name: Install Sentry CLI
        run: |
          # Install Sentry CLI using Homebrew on macOS
          brew install getsentry/tools/sentry-cli
          echo "Sentry CLI installed successfully"

      - name: Authenticate with Sentry
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }} # Use the secret token
        run: |
          # Verify the token is present and log in to Sentry
          if [ -z "$SENTRY_AUTH_TOKEN" ]; then
            echo "⚠️ SENTRY_AUTH_TOKEN not set, skipping Sentry login."
          else
            sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"
            echo "✅ Sentry CLI authenticated."
          fi

      # ----------------------------------
      # --     Environment Setup        --
      # ----------------------------------
      - name: Setup .NET 8 SDK
        uses: actions/setup-dotnet@v4 # Install the specified .NET SDK version
        with:
          dotnet-version: 8.0.x

      # ----------------------------------
      # --       Caching Setup          --
      # ----------------------------------
      - name: Cache .NET MAUI Workloads
        id: cache-dotnet-workloads
        uses: actions/cache@v4 # Cache installed .NET workloads
        with:
          path: ~/.dotnet/workload
          key: ${{ runner.os }}-dotnet-workloads-${{ hashFiles('**/DrMaxMuscle.csproj') }} # Cache key based on OS and project file hash
          restore-keys: |
            ${{ runner.os }}-dotnet-workloads-

      - name: Cache NuGet Packages
        id: cache-nuget
        uses: actions/cache@v4 # Cache downloaded NuGet packages
        with:
          path: ~/.nuget/packages
          key: ${{ runner.os }}-nuget-${{ hashFiles('**/packages.lock.json') }} # Cache key based on OS and lock file hash
          restore-keys: |
            ${{ runner.os }}-nuget-

      # ----------------------------------
      # --   MAUI & Dependencies        --
      # ----------------------------------
      - name: Install .NET MAUI Workload (includes iOS)
        run: dotnet workload install maui --skip-manifest-update # Install the full MAUI workload

      - name: Restore NuGet Dependencies
        run: dotnet restore DrMaxMuscle/DrMaxMuscle.csproj # Restore packages (adjust path if needed)

      # ----------------------------------
      # --      iOS Signing Setup       --
      # ----------------------------------
      - name: Setup iOS Certificate and Provisioning Profile
        id: setup_ios_signing # Give step an ID for potential dependency checks
        env:
          IOS_P12_BASE64: ${{ secrets.IOS_P12_BASE64 }}
          IOS_P12_PASSWORD: ${{ secrets.IOS_P12_PASSWORD }}
          IOS_PROVISIONING_PROFILE_BASE64: ${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}
        run: |
          # Basic check for secrets
          if [ -z "$IOS_P12_BASE64" ] || [ -z "$IOS_P12_PASSWORD" ] || [ -z "$IOS_PROVISIONING_PROFILE_BASE64" ]; then
             echo "❌ One or more iOS signing secrets are missing!"
             exit 1 # Fail early if essential secrets are missing
          fi

          # Decode P12 certificate from base64 secret
          echo "$IOS_P12_BASE64" | base64 --decode > certificate.p12

          # Create a temporary keychain specific to this build
          KEYCHAIN_PATH=build.keychain
          security create-keychain -p "" "$KEYCHAIN_PATH"
          security list-keychains -d user -s "$KEYCHAIN_PATH" # Add to search list, making it default for user domain
          security default-keychain -s "$KEYCHAIN_PATH" # Set as default overall
          security unlock-keychain -p "" "$KEYCHAIN_PATH" # Unlock it

          # Import the P12 certificate into the temporary keychain
          # Use -T /usr/bin/codesign to allow codesign access without prompts
          security import certificate.p12 -k "$KEYCHAIN_PATH" -P "$IOS_P12_PASSWORD" -T /usr/bin/codesign
          # Allow codesign utility to access the imported key without prompts (alternative method)
          security set-key-partition-list -S apple-tool:,apple: -s -k "" "$KEYCHAIN_PATH"
          rm certificate.p12 # Clean up the temporary P12 file
          echo "✅ Certificate imported into temporary keychain."

          # Decode the provisioning profile from base64 secret and install it
          PROFILE_DIR="$HOME/Library/MobileDevice/Provisioning Profiles"
          mkdir -p "$PROFILE_DIR" # Ensure the directory exists
          PROFILE_UUID=$(/usr/libexec/PlistBuddy -c 'Print :UUID' /dev/stdin <<< $(security cms -D -i certificate.p12 -k "$KEYCHAIN_PATH" -P "$IOS_P12_PASSWORD" 2>/dev/null) 2>/dev/null) # Attempt to get UUID if needed, might fail
          PROFILE_FILENAME="${PROFILE_UUID:-build_profile}.mobileprovision" # Use UUID in filename if possible
          echo "$IOS_PROVISIONING_PROFILE_BASE64" | base64 --decode > "$PROFILE_DIR/$PROFILE_FILENAME"
          echo "✅ Provisioning profile installed to $PROFILE_DIR/$PROFILE_FILENAME"

          # Optional: List identities to verify import
          echo "Listing codesigning identities:"
          security find-identity -v -p codesigning "$KEYCHAIN_PATH"

      # ----------------------------------
      # -- Versioning & Info.plist Update --
      # ----------------------------------
      - name: Determine Version Info (iOS)
        id: version_ios # Separate ID from Android version step
        run: |
          # Use the version name and code provided via workflow_dispatch inputs
          echo "Using version name from input: ${{ github.event.inputs.version_name }}"
          echo "Using version code from input: ${{ github.event.inputs.version_code }}"
          echo "version_name=${{ github.event.inputs.version_name }}" >> $GITHUB_OUTPUT
          echo "version_code=${{ github.event.inputs.version_code }}" >> $GITHUB_OUTPUT
          # Basic validation for version code (should be integer)
          if ! [[ "${{ github.event.inputs.version_code }}" =~ ^[0-9]+$ ]]; then
             echo "❌ Invalid version_code input: Must be an integer."
             exit 1
          fi

      - name: Update Info.plist and csproj (iOS)
        run: |
          # Update version strings in Info.plist and the main csproj file
          VERSION_CODE="${{ steps.version_ios.outputs.version_code }}" # Use iOS step output
          VERSION_NAME="${{ steps.version_ios.outputs.version_name }}" # Use iOS step output
          PLIST_PATH="DrMaxMuscle/Platforms/iOS/Info.plist" # Adjust path if needed
          CSPROJ_PATH="DrMaxMuscle/DrMaxMuscle.csproj" # Adjust path if needed

          echo "Updating $PLIST_PATH with CFBundleVersion=$VERSION_CODE, CFBundleShortVersionString=$VERSION_NAME"
          if [ -f "$PLIST_PATH" ]; then
             # Use PlistBuddy to set the values correctly
             /usr/libexec/PlistBuddy -c "Set :CFBundleVersion $VERSION_CODE" "$PLIST_PATH"
             /usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $VERSION_NAME" "$PLIST_PATH"
             echo "✅ Updated Info.plist"
          else
             echo "⚠️ Info.plist not found at $PLIST_PATH"
          fi

          echo "Updating $CSPROJ_PATH with ApplicationVersion=$VERSION_CODE, ApplicationDisplayVersion=$VERSION_NAME"
           if [ -f "$CSPROJ_PATH" ]; then
             # Use simpler sed syntax (macOS sed requires '' for -i with no backup)
             sed -i '' "s|<ApplicationVersion>[^<]*</ApplicationVersion>|<ApplicationVersion>$VERSION_CODE</ApplicationVersion>|" "$CSPROJ_PATH"
             sed -i '' "s|<ApplicationDisplayVersion>[^<]*</ApplicationDisplayVersion>|<ApplicationDisplayVersion>$VERSION_NAME</ApplicationDisplayVersion>|" "$CSPROJ_PATH"
             echo "✅ Updated $CSPROJ_PATH"
          else
             echo "⚠️ Project file not found at $CSPROJ_PATH"
          fi

      # ----------------------------------
      # --     Build Application        --
      # ----------------------------------
      - name: Build and Sign iOS App (IPA)
        # Depends on signing setup being successful
        if: steps.setup_ios_signing.outcome == 'success'
        run: |
          # Publish the iOS project in Release configuration for the device architecture, creating a signed IPA
          # Version properties are read from the updated csproj/Info.plist during the build
          dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-ios -c Release \
            /p:RuntimeIdentifier=ios-arm64 \
            /p:BuildIpa=True
            # NOTE: Signing properties (CodesignKey, CodesignProvision) are often picked up automatically
            # from the keychain and installed profile. If the build fails with signing errors,
            # you might need to explicitly specify them here using /p:CodesignKey="..." /p:CodesignProvision="..."
            # Find the exact names in Keychain Access and the profile file.

      # ----------------------------------
      # --     Verify & Upload Output   --
      # ----------------------------------
      - name: Verify IPA Output
        id: verify_ipa
        # Only run if build was attempted (signing setup succeeded)
        if: steps.setup_ios_signing.outcome == 'success'
        run: |
          # Find the generated IPA file in the expected output directory
          IPA_PATH=$(find DrMaxMuscle/bin/Release/net8.0-ios/ios-arm64/publish/ -name '*.ipa' -type f | head -n 1)
          if [ -z "$IPA_PATH" ]; then
            echo "❌ IPA file not found after build!"
            echo "ipa_found=false" >> $GITHUB_OUTPUT
            exit 1 # Fail the job if IPA is missing
          else
            echo "✅ IPA file found: $IPA_PATH"
            echo "ipa_path=$IPA_PATH" >> $GITHUB_OUTPUT
            echo "ipa_found=true" >> $GITHUB_OUTPUT
          fi

      - name: Upload iOS IPA Artifact
        # Only upload if IPA was found
        if: steps.verify_ipa.outputs.ipa_found == 'true'
        uses: actions/upload-artifact@v4 # Upload the IPA as a workflow artifact
        with:
          name: ios-ipa-v${{ steps.version_ios.outputs.version_name }}-${{ steps.version_ios.outputs.version_code }} # Name the artifact with version info
          path: ${{ steps.verify_ipa.outputs.ipa_path }} # Use the path found in the previous step
          if-no-files-found: error # Ensure the upload step fails if the file isn't there

      # ----------------------------------
      # --  Optional: TestFlight Upload --
      # ----------------------------------
      - name: Setup App Store Connect API Key
        # Only run if IPA was found and secret exists
        if: steps.verify_ipa.outputs.ipa_found == 'true' && secrets.APPSTORE_API_PRIVATE_KEY != ''
        run: |
          # Decode the base64 encoded P8 key and save it
          mkdir -p ~/private_keys
          echo "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" | base64 --decode > ~/private_keys/AuthKey_${{ secrets.APPSTORE_API_KEY_ID }}.p8
          echo "App Store Connect API Key installed."

      - name: Upload app to TestFlight
        # Only run if API key setup succeeded
        if: steps.verify_ipa.outputs.ipa_found == 'true' && secrets.APPSTORE_API_PRIVATE_KEY != ''
        run: |
          # Use xcrun altool (part of Xcode) to upload the IPA to TestFlight
          xcrun altool --upload-app \
            -f "${{ steps.verify_ipa.outputs.ipa_path }}" \
            -t ios \
            --apiKey "${{ secrets.APPSTORE_API_KEY_ID }}" \
            --apiIssuer "${{ secrets.APPSTORE_ISSUER_ID }}"

      # ----------------------------------
      # --    Final Summary Output      --
      # ----------------------------------
      - name: Output Final Build Summary (iOS)
        if: always() # Always run this step
        run: |
          # Append final status to the GitHub Actions summary page
          echo "### 🍏 iOS Build Final Status" >> $GITHUB_STEP_SUMMARY
          # Check outcome of critical steps
          if [ "${{ steps.setup_ios_signing.outcome }}" != "success" ]; then
             echo "- **Status:** ❌ Build Skipped (Signing Setup Failed)" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.verify_ipa.outputs.ipa_found }}" != "true" ]; then
             echo "- **Status:** ❌ Build Failed (IPA not found)" >> $GITHUB_STEP_SUMMARY
          else
             echo "- **Status:** ✅ Build Successful" >> $GITHUB_STEP_SUMMARY
             echo "- **Version:** ${{ steps.version_ios.outputs.version_name }} (Code: ${{ steps.version_ios.outputs.version_code }})" >> $GITHUB_STEP_SUMMARY
             echo "- **Artifact:** \`ios-ipa-v${{ steps.version_ios.outputs.version_name }}-${{ steps.version_ios.outputs.version_code }}\` uploaded" >> $GITHUB_STEP_SUMMARY
             if [ -n "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" ]; then
                # Check job status of the upload step if it exists and ran
                # This requires giving the upload step an ID, e.g., id: upload_testflight
                # echo "- **Deployment:** ✅ Uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
                echo "- **Deployment:** Attempted upload to TestFlight" >> $GITHUB_STEP_SUMMARY # Safer message
             else
                echo "- **Deployment:** ⚠️ Skipped (App Store Connect API Key not provided)" >> $GITHUB_STEP_SUMMARY
             fi
          fi