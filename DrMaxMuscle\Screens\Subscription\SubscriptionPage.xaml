﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:local="clr-namespace:DrMaxMuscle.Cells"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Screens.Subscription.SubscriptionPage"
             Title="SubscriptionPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:BotDataTemplateSelector
        x:Key="BotTemplateSelector">
            </local:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <!--<StackLayout
    VerticalOptions="CenterAndExpand">-->
            <ScrollView
        Orientation="Vertical">
                <StackLayout
            VerticalOptions="FillAndExpand"
            Margin="0,0,0,0"
            Padding="0,0,0,20"
            BackgroundColor="White">
                    <StackLayout
                x:Name="buttonStacklayout"
                Orientation="Vertical"
                        Spacing="5"
                VerticalOptions="CenterAndExpand"
                HorizontalOptions="FillAndExpand">
                        <Label
                    x:Name="SignUpLabelHeading"
                    Text="Get in Shape Faster on Autopilot"
                    HorizontalOptions="CenterAndExpand"
                            FontSize="20"
                            FontAttributes="Bold"
                    Style="{StaticResource LabelStyle}"
                    TextColor="Black"
                    Margin="0,20,0,0"></Label>

                        <!--<Label x:Name="SignUpLabelLine1" Text="24/7 smart coach for less than" HorizontalOptions="CenterAndExpand" Style="{StaticResource LabelStyle}"></Label>
    <Label x:Name="SignUpLabelLine2" Text="1 session with a personal trainer" HorizontalOptions="CenterAndExpand" Style="{StaticResource LabelStyle}"></Label>-->
                        <Grid
                    x:Name="GridTips"
                    HorizontalOptions="CenterAndExpand"
                            VerticalOptions="FillAndExpand"
                    Padding="35,0,35,15"
                    RowSpacing="5">
                            <Grid.RowDefinitions>
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" /><RowDefinition
                            Height="Auto" /><RowDefinition
                            Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition
                            Width="Auto" />
                                <ColumnDefinition
                            Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="0"
                        Grid.Column="1"
                        Text="23+ smart features"
                        HorizontalOptions="Start"
                        TextDecorations="Underline"
                        Style="{StaticResource LabelStyle}"
                        TextColor="{x:Static constnats:AppThemeConstants.BlueColor}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                Tapped="HelpGestureRecognizer_Tapped" />
                                </Label.GestureRecognizers>
                            </Label>

                            <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="Automate everything"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="Progress on autopilot"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="3"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="3"
                        Grid.Column="1"
                        Text="Workouts unique to you"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="4"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="4"
                        Grid.Column="1"
                        Text="A top trainer in your phone"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                        </Grid>
                        <Label
                    x:Name="SignUpLabelLine2"
                    Text="Try it for a month—it's worth it 💪"
                    HorizontalOptions="CenterAndExpand"
                    Style="{StaticResource LabelStyle}"
                    FontSize="17"
FontAttributes="Bold"></Label>
                        <!--<Label x:Name="SignUpMonthly" Text="Try it for $XXX for 1 month, then pay monthly:" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Margin="20,0" Style="{StaticResource LabelStyle}" ></Label>-->
                        <t:DrMuscleButton
                            BorderColor="#195377"
                            BackgroundColor="Transparent"
                    x:Name="BuyMonthlyAccessButton"
                    Text="Sign up monthly"
                    Style="{StaticResource buttonStyle}"
                    HorizontalOptions="FillAndExpand"
                    Margin="20,0"></t:DrMuscleButton>
                        <Label
                    x:Name="SignUpYearly"
                    Text="Sign up annual now &amp; save (~4 months free):"
                    HorizontalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center"
                    Style="{StaticResource LabelStyle}"
                    Margin="20,0"></Label>
                        <t:DrMuscleButton
                            BorderColor="#195377"
                    x:Name="BuyYearlyAccessButton"
                    Text="Sign up annual"
                    Style="{StaticResource buttonStyle}"
                    HorizontalOptions="FillAndExpand"
                    Margin="20,0"></t:DrMuscleButton>
                        <t:DrMuscleButton
                    x:Name="RestorePurchaseButton"
                    IsVisible="false"
                    Text="Restore purchase"
                    Style="{StaticResource buttonStyle}"
                    HorizontalOptions="FillAndExpand"
                    Margin="20,0"></t:DrMuscleButton>

                        <Border
                    Padding="0"
                    Margin="20,5,20,0"
                    x:Name="SupportEmail2"
                    Style="{StaticResource GradientBorderStyleBlue}"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="60">
                            <t:DrMuscleButton
                        x:Name="EmailSupportButton2"
                        HeightRequest="60"
                        Text="Email us, we reply in 1 day"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"
                        Style="{StaticResource buttonStyle}"
                        HorizontalOptions="FillAndExpand">
                            </t:DrMuscleButton>
                        </Border>
                        <!--Meal Plan-->
                        <Label
                    x:Name="SignUpLabelLineMealPlan"
                    Margin="0,15,0,0"
                    Text="Get unlimited meal plans (add-on)"
                    HorizontalOptions="CenterAndExpand"
                    Style="{StaticResource LabelStyle}"
                    FontSize="17"
FontAttributes="Bold"></Label>

                        <!--<Label x:Name="SignUpMonthly" Text="Try it for $XXX for 1 month, then pay monthly:" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Margin="20,0" Style="{StaticResource LabelStyle}" ></Label>-->
                        <t:DrMuscleButton
                                BorderColor="#195377"
                    x:Name="BuyMealplanAddon"
                    Text="Sign up monthly"
                    Style="{StaticResource buttonStyle}"
                    HorizontalOptions="FillAndExpand"
                    Margin="20,0"></t:DrMuscleButton>
                        <t:DrMuscleButton
                                BorderColor="#195377"
                    x:Name="RestorePurchaseMealplan"
                    IsVisible="false"
                    Text="Restore purchase"
                    Style="{StaticResource buttonStyle}"
                    HorizontalOptions="FillAndExpand"
                    Margin="20,0"></t:DrMuscleButton>
                        <Frame
                    Padding="0"
                    Margin="20,5,20,0"
                    IsClippedToBounds="true"
                    x:Name="SupportEmail3"
                    CornerRadius="0"
                    HorizontalOptions="FillAndExpand"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HeightRequest="60">
                            <t:DrMuscleButton
                        x:Name="EmailSupportButton3"
                        HeightRequest="60"
                        Text="Email us, we reply in 1 day"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"
                        Style="{StaticResource buttonStyle}"
                        HorizontalOptions="FillAndExpand"></t:DrMuscleButton>
                        </Frame>
                        <Label
                    x:Name="LblTooExpensive"
                    Text="Too expensive?"
                    HorizontalOptions="Center"
                    FontSize="17"
FontAttributes="Bold"
                    Style="{StaticResource LabelStyle}"
                    Margin="0,20,0,0" />
                        <Grid
                    x:Name="GridTips1"
                    HorizontalOptions="CenterAndExpand"
                    Padding="35,0,35,0"
                    RowSpacing="5">
                            <Grid.RowDefinitions>
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" />


                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition
                            Width="Auto" />
                                <ColumnDefinition
                            Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="0"
                        Grid.Column="1"
                        Text="More expensive, but more advanced"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="You get in shape faster, on autopilot"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="&quot;For a product that works, it’s worth it&quot;"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />

                        </Grid>
                        <Label
                    x:Name="LblRead5Star"
                    Text="(read this 5-star review and hundreds more)"
                    HorizontalOptions="CenterAndExpand"
                    Margin="0,0,0,20"
                    Style="{StaticResource LabelStyle}"
                    TextColor="{x:Static constnats:AppThemeConstants.BlueColor}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="MoreUserReviewGestureRecognizer_Tapped" />
                            </Label.GestureRecognizers>
                        </Label>


                        <!--<Label x:Name="LblUserReview" Text="User review" HorizontalOptions="Start"  Margin="20,20,0,0" Font="Bold,17"  Style="{StaticResource LabelStyle}" />-->

                        <StackLayout
                    BackgroundColor="#f4f4f4"
                    Margin="0,0,0,0"
                    Padding="0,8,0,8"
                    x:Name="FrmExepertReviewJonny">
                            <Border
                        Padding="20,5,20,15"
                        Margin="12,2,12,2"
                        BackgroundColor="White"
                                Stroke="Transparent" StrokeShape="RoundRectangle 12,12,12,12" 
                                >
                                <StackLayout Spacing="5">
                                    <Image
                                Source="stars_5.png"
                                WidthRequest="120"
                                Aspect="AspectFit"
                                HorizontalOptions="Start" />
                                    <Label
                                x:Name="LblReview2"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold, Italic"
                                FontSize="15" />
                                    <Label
                                x:Name="LblsubHeadingReviewer2"
                                Style="{StaticResource LabelStyle}"
                                FontSize="15"
                                FontAttributes="Italic" />
                                    <Label
                                x:Name="LblReviewerName2"
                                LineBreakMode="WordWrap"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
                                FontSize="15" />
                                    <StackLayout
                                Margin="0,0,0,0"
                                Padding="0"
                                x:Name="ImgArtin">
                                        <ffimageloading:CachedImage
                                            ErrorPlaceholder="backgroundblack.png"
                                    Margin="0"
                                    Source="jonus.png"
                                    Aspect="AspectFit" />
                                    </StackLayout>
                                </StackLayout>
                            </Border>
                        </StackLayout>

                        <StackLayout
                    BackgroundColor="#f4f4f4"
                    Margin="0,0,0,0"
                    Padding="0,8,0,8"
                    x:Name="FrmExepertReview">
                            <Frame
                        Padding="20,5,20,15"
                        Margin="12,2,12,2"
                        BackgroundColor="White"
                                BorderColor="Transparent"
                        CornerRadius="12"
                        HasShadow="False">
                                <StackLayout Spacing="5">
                                    <Image
                                Source="stars_5.png"
                                WidthRequest="120"
                                Aspect="AspectFit"
                                HorizontalOptions="Start" />
                                    <Label
                                x:Name="LblReview1"
                                Text="{Binding Part2}"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold, Italic"
                                FontSize="15" />
                                    <Label
                                x:Name="LblsubHeadingReviewer1"
                                Text="{Binding Part3}"
                                Style="{StaticResource LabelStyle}"
                                FontSize="15"
                                FontAttributes="Italic" />
                                    <Label
                                x:Name="LblReviewerName1"
                                Text="{Binding Answer}"
                                LineBreakMode="WordWrap"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
                                FontSize="15" />

                                    <Image
                                Margin="0"
                                Source="artin.png"
                                Aspect="AspectFit"
                                x:Name="ImgJonus" />

                                </StackLayout>
                                <!--<Frame.GestureRecognizers>
                            <TapGestureRecognizer Tapped="TapMoreExperReviews_Tapped" />
                        </Frame.GestureRecognizers>-->
                            </Frame>
                        </StackLayout>

                        <StackLayout
                    x:Name="FrmPoteroUserReview"
                    BackgroundColor="#f4f4f4"
                    Margin="0,0,0,0"
                    Padding="0,8,0,8">
                            <Frame
                        Padding="20,5,20,15"
                        Margin="12,2,12,2"
                        BackgroundColor="White"
                                BorderColor="Transparent"
                        CornerRadius="12"
                        HasShadow="False">
                                <StackLayout Spacing="5">
                                    <Image
                                Source="stars_5.png"
                                WidthRequest="120"
                                Aspect="AspectFit"
                                HorizontalOptions="Start" />
                                    <Label
                                x:Name="LblPoteroReview"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold, Italic"
                                FontSize="15" />
                                    <Label
                                x:Name="LblPoterosubHeadingReviewer"
                                Style="{StaticResource LabelStyle}"
                                FontSize="15"
                                FontAttributes="Italic" />
                                    <Label
                                x:Name="LblPoteroReviewerName"
                                LineBreakMode="WordWrap"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
                                FontSize="15" />
                                </StackLayout>
                                <!--<Frame.GestureRecognizers>
                            <TapGestureRecognizer Tapped="MoreUserReviewGestureRecognizer_Tapped" />
                        </Frame.GestureRecognizers>-->
                            </Frame>
                        </StackLayout>
                        <StackLayout
                    x:Name="FrmMKJUserReview"
                    BackgroundColor="#f4f4f4"
                    Margin="0,0,0,0"
                    Padding="0,8,0,8">
                            <Frame
                        Padding="20,5,20,15"
                        Margin="12,2,12,2"
                        BackgroundColor="White"
                                BorderColor="Transparent"
                        CornerRadius="12"
                        HasShadow="False">
                                <StackLayout Spacing="5">
                                    <Image
                                Source="stars_5.png"
                                WidthRequest="120"
                                Aspect="AspectFit"
                                HorizontalOptions="Start" />
                                    <Label
                                x:Name="LblMKJReview"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold, Italic"
                                FontSize="15" />
                                    <Label
                                x:Name="LblMKJsubHeadingReviewer"
                                Style="{StaticResource LabelStyle}"
                                FontSize="15"
                                FontAttributes="Italic" />
                                    <Label
                                x:Name="LblMKJReviewerName"
                                LineBreakMode="WordWrap"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
                                FontSize="15" />
                                </StackLayout>
                            </Frame>
                        </StackLayout>
                        <StackLayout
                    x:Name="FrmUserReview"
                    BackgroundColor="#f4f4f4"
                    Margin="0,0,0,0"
                    Padding="0,8,0,8">
                            <Frame
                        Padding="20,5,20,15"
                        Margin="12,2,12,2"
                        BackgroundColor="White"
                                BorderColor="Transparent"
                        CornerRadius="12"
                        HasShadow="False">
                                <StackLayout Spacing="5">
                                    <Image
                                Source="stars_5.png"
                                WidthRequest="120"
                                Aspect="AspectFit"
                                HorizontalOptions="Start" />
                                    <Label
                                x:Name="LblReview"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold, Italic"
                                FontSize="15" />
                                    <Label
                                x:Name="LblsubHeadingReviewer"
                                Style="{StaticResource LabelStyle}"
                                FontSize="15"
                                FontAttributes="Italic" />
                                    <Label
                                x:Name="LblReviewerName"
                                LineBreakMode="WordWrap"
                                Style="{StaticResource LabelStyle}"
                                FontAttributes="Bold"
                                FontSize="15" />
                                    <Label
                                Text="Read more reviews"
                                x:Name="LblMoreUserReview"
                                HorizontalOptions="Start"
                                TextDecorations="Underline"
                                Style="{StaticResource LabelStyle}"
                                TextColor="{x:Static constnats:AppThemeConstants.BlueColor}"
                                Margin="0">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer
                                        Tapped="MoreUserReviewGestureRecognizer_Tapped" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </StackLayout>
                            </Frame>
                        </StackLayout>
                        <Label
                    x:Name="LblTheTestla"
                    Text="The Tesla of workout apps"
                    HorizontalOptions="Center"
                    FontSize="17"
FontAttributes="Bold"
                    Style="{StaticResource LabelStyle}"
                    Margin="0,20,0,0" />
                        <Grid
                    x:Name="GridTips2"
                    HorizontalOptions="CenterAndExpand"
                    Padding="15,0"
                    RowSpacing="5">
                            <Grid.RowDefinitions>
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" />
                                <RowDefinition
                            Height="Auto" />

                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition
                            Width="Auto" />
                                <ColumnDefinition
                            Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="0"
                        Grid.Column="1"
                        Text="Invested over $1.25 M over 5 years"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="1"
                        Grid.Column="1"
                        Text="Updated weekly (46 updates in 2021)"
                        HorizontalOptions="Start"
                        TextDecorations="Underline"
                        Style="{StaticResource LabelStyle}"
                        TextColor="{x:Static constnats:AppThemeConstants.BlueColor}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                Tapped="NewUpdatesGestureRecognizer_Tapped" />
                                </Label.GestureRecognizers>
                            </Label>
                            <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Text="•"
                        Style="{StaticResource LabelStyle}" />
                            <Label
                        Grid.Row="2"
                        Grid.Column="1"
                        Text="With exercise scientist Dr. Carl Juneau, PhD"
                        HorizontalOptions="Start"
                        Style="{StaticResource LabelStyle}">

                            </Label>
                        </Grid>
                        <Image
                    Margin="20,10,20,0"
                    x:Name="ImgBrandLogo"
                    Source="brandlogo.png"
                    Aspect="AspectFit" />
                        <Label
                    x:Name="LblTooExpensive2"
                    IsVisible="false"
                    Text="Still too expensive?"
                    HorizontalOptions="Center"
                    FontSize="17"
FontAttributes="Bold"
                    Style="{StaticResource LabelStyle}"
                    Margin="0,20,0,5" />

                        <Frame
                    Padding="0"
                    Margin="20,5,20,0"
                    IsClippedToBounds="true"
                    x:Name="SupportEmail"
                    CornerRadius="0"
                    HorizontalOptions="FillAndExpand"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HeightRequest="60">
                            <t:DrMuscleButton
                        x:Name="EmailSupportButton"
                        HeightRequest="60"
                        Text="Email us, we reply in 1 day"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"
                        Style="{StaticResource buttonStyle}"
                        HorizontalOptions="FillAndExpand"></t:DrMuscleButton>
                        </Frame>
                        <!--<Label x:Name="LblExpertReview" Text="Expert review" HorizontalOptions="Start" Font="Bold,17"  Style="{StaticResource LabelStyle}" Margin="0,20,0,0"  />-->

                        <StackLayout
                    HorizontalOptions="CenterAndExpand"
                    VerticalOptions="End"
                    Orientation="Vertical"
                    Margin="20,20,20,0">
                            <Label
                        IsVisible="false"
                        x:Name="SignUpLabelLine3"
                        HorizontalOptions="CenterAndExpand"
                        Text="Once you confirm your subscription purchase, your payment will be charged to your iTunes account, and any unused portion of your free trial will be forfeited."
                        Style="{StaticResource LabelStyle}"
                        FontSize="12"></Label>
                            <Label
                        IsVisible="false"
                        x:Name="SignUpLabelLine4"
                        HorizontalOptions="CenterAndExpand"
                        Text="Once your subscription is active, your iTunes account will be charged again automatically when your subscription renews at the end of your subscription period, unless you turn off auto-renew at least 24 hours before. You can turn off auto-renew anytime in your iTunes account settings."
                        Style="{StaticResource LabelStyle}"
                        FontSize="12"></Label>
                            <StackLayout
                        x:Name="TermsPrivacyStack"
                        HorizontalOptions="CenterAndExpand"
                        VerticalOptions="End"
                        Orientation="Horizontal"
                        Margin="20,10,20,0">
                                <Label
                            x:Name="TermsOfUse"
                            Text="Terms of Use"
                            Style="{StaticResource LearnMoreText}" />
                                <Label
                            x:Name="Pipe"
                            Text=" ∣ "
                            FontSize="12"
                            Style="{StaticResource LabelStyle}" />
                                <Label
                            x:Name="PrivacyPolicy"
                            Text="Privacy Policy"
                            Style="{StaticResource LearnMoreText}"
                            Margin="0,0,0,10" />
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>
                </StackLayout>
            </ScrollView>

            <!--<Frame HasShadow="true" CornerRadius="8" Padding="1,4,1,0" BackgroundColor="#faf9f8"
                 x:Name="bottomSheet" 
                RelativeLayout.YConstraint="{ConstraintExpression Type=RelativeToParent,
                    Property=Height,Factor=.90,Constant=0}"
                RelativeLayout.WidthConstraint="{ConstraintExpression
                    Type=RelativeToParent,Property=Width,Factor=1,Constant=0}"
                RelativeLayout.HeightConstraint="{ConstraintExpression
                    Type=RelativeToParent,Property=Height,Factor=1,Constant=0}" >
        <Frame.GestureRecognizers>
                <PanGestureRecognizer PanUpdated="OnPanUpdated" />
            </Frame.GestureRecognizers>
          <StackLayout Spacing="0">
              <StackLayout.GestureRecognizers>
                  <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
              </StackLayout.GestureRecognizers>
           
           <StackLayout
                Orientation="Vertical" HorizontalOptions="CenterAndExpand" Padding="0,5" Spacing="0">
               
               <Image x:Name="ImgArrow" Source="up.png" WidthRequest="22" Aspect="AspectFit"/>
               <Label HorizontalOptions="Center" Text="Reviews" TextColor="#195377" VerticalOptions="Center" />
            </StackLayout>
          <StackLayout
    Spacing="10" Padding="0,0,0,15"
             BackgroundColor="#f4f4f4">
    <t:DrMuscleListView
        BackgroundColor="#f4f4f4"
        ItemTemplate="{StaticResource BotTemplateSelector}"
        HasUnevenRows="True"
        ItemTapped="lstChats_ItemTapped"
        x:Name="lstChats"
        VerticalOptions="FillAndExpand"
        FlowDirection="LeftToRight"
        SeparatorColor="Transparent" />
        </StackLayout>
              </StackLayout>
      </Frame>-->
        <!--</StackLayout>-->
    </ContentPage.Content>
</ContentPage>