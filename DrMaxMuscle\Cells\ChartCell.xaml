<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
              xmlns:oxy="clr-namespace:OxyPlot.Maui.Skia;assembly=OxyPlot.Maui.Skia"
             x:Class="DrMaxMuscle.Cells.ChartCell">
    <Grid
     HeightRequest="150"
     HorizontalOptions="FillAndExpand">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
             Width="*" />
            <ColumnDefinition
             Width="*" />
        </Grid.ColumnDefinitions>
        <oxy:PlotView
          IsVisible="true"
         Grid.Row="0"
         Grid.Column="0"
         x:Name="plotView"
         HorizontalOptions="FillAndExpand"
         VerticalOptions="FillAndExpand"
         HeightRequest="150">
        </oxy:PlotView>
        <BoxView IsVisible="true"  Grid.Row="0" Grid.Column="0" BackgroundColor="Transparent">
            <BoxView.GestureRecognizers>
                <TapGestureRecognizer Tapped="StrenthChart_Tapped" />
            </BoxView.GestureRecognizers>
        </BoxView>
        <oxy:PlotView
         Grid.Row="0"
         Grid.Column="1"
         x:Name="plotView2"
         IsVisible="true"
         HorizontalOptions="FillAndExpand"
         VerticalOptions="FillAndExpand"
         HeightRequest="150">
        </oxy:PlotView>
        <BoxView  IsVisible="true" Grid.Row="0" Grid.Column="1" BackgroundColor="Transparent">
            <BoxView.GestureRecognizers>
                <TapGestureRecognizer Tapped="SetsChart_Tapped" />
            </BoxView.GestureRecognizers>
        </BoxView>

    </Grid>
</ContentView>
