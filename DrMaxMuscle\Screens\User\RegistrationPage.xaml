﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.RegistrationPage"
             xmlns:local="clr-namespace:DrMaxMuscle.Behaviors"
            xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
            NavigationPage.HasNavigationBar="False"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             Title="RegistrationPage">
    <StackLayout>
        <Grid
         Margin="0"
         HorizontalOptions="FillAndExpand"
         RowSpacing="0"
         VerticalOptions="FillAndExpand">
            <Image
     Aspect="AspectFill"
     HorizontalOptions="FillAndExpand"
     Source="page3"
     VerticalOptions="FillAndExpand" />
            <ScrollView
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
                <StackLayout
                 Margin="20">

                    <StackLayout
                    HorizontalOptions="FillAndExpand"
                    Orientation="Vertical"
                    Spacing="0">
                        <Image
                        x:Name="AppLogoImage"
                        Aspect="AspectFit"
                         HeightRequest="155"
                         WidthRequest="160"
                        HorizontalOptions="Center"
                        Source="logo1">
                            <Image.Margin>
                                <OnPlatform x:TypeArguments="Thickness">
                                    <On Platform="iOS" Value="0,20,0,0"/>
                                    <On Platform="Android" Value="0,10,0,0"/>
                                </OnPlatform>
                            </Image.Margin>
                        </Image>
                        <Label
                        x:Name="LblHeader1"
                        Style="{StaticResource OnBoardingLabelStyle}"
                        FontSize="21" 
                        FontAttributes="Bold"
                        HorizontalTextAlignment="Center"
                        Text="Create free account"
                        TextColor="White" />
                        <Label
                        x:Name="LblHeader2"
                        Style="{StaticResource OnBoardingLabelStyle}" 
                        FontSize="17"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center"
                        Text="Save your workouts,"
                        TextColor="LightGray" />
                        <Label
                        x:Name="LblHeader3"
                        Style="{StaticResource OnBoardingLabelStyle}" 
                        FontSize="17"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center"
                        Text="progress, and stats"
                        TextColor="LightGray" />
                    </StackLayout>
                    <StackLayout
                     x:Name="DataFetchLayout"
                     Margin="{OnPlatform Android='0,10,0,0',iOS='0,0,0,0'}"
                     HorizontalOptions="FillAndExpand"
                     Orientation="Vertical"
                     VerticalOptions="CenterAndExpand">
                        <StackLayout />
                        <StackLayout
                        Margin="0,20"
                        HorizontalOptions="FillAndExpand"
                        Spacing="10"
                        VerticalOptions="End">
                            <StackLayout
                            Padding="0"
                            Orientation="Vertical"
                            Spacing="10">

                                <Frame
                                x:Name="EmailFrame"
                                Margin="0,30,0,0"
                                CornerRadius="{OnPlatform Android='10', iOS='10'}"
                                Padding="{OnPlatform Android='15,5,17,5',iOS= '15'}"
                                HasShadow="False"
                                BackgroundColor="#4C000000"
                                BorderColor="LightGray">
                                    <t:DrMuscleEntry 
                                    MaxLength="40"
                                    TextChanged="EmailTextChanged"
                                    FontSize="16"
                                        Margin="0"
                                    x:Name="EmailEntry"
                                    BackgroundColor="Transparent"
                                    PlaceholderColor="LightGray"
                                    Placeholder="Enter email" 
                                    Keyboard="Email" 
                                    TextColor="White"
                                 >
                                    </t:DrMuscleEntry>

                                </Frame>
                                <Label Text="" Margin="6,-4,0,0" TextColor="White" FontSize="12" x:Name="EmailValidator" IsVisible="false"/>
                                <Frame
                                x:Name="PasswordFrame"
                                Margin="0,2,0,0"
                                CornerRadius="{OnPlatform Android='10', iOS='10'}"
                                Padding="{OnPlatform Android='15,5,5,5',iOS= '13,4'}"
                                HasShadow="False"
                                BackgroundColor="#4C000000"
                                BorderColor="LightGray">
                                    <Grid
                                        Margin="0">
                                        <t:DrMuscleEntry 
                                        MaxLength="40"
                                        TextChanged="PasswordTextChanged"
                                        FontSize="16"
                                            Margin="0"
                                        x:Name="PasswordEntry" 
                                        Placeholder="Create password" 
                                        IsPassword="{Binding Source={x:Reference ShowPasswordActualTrigger}, Path=HidePassword}" 
                                        BackgroundColor="Transparent"
                                        TextColor="White"
                                            HorizontalOptions="FillAndExpand"
                                        PlaceholderColor="LightGray"></t:DrMuscleEntry>
                                        <ImageButton VerticalOptions="Center"
                                         HeightRequest="20" WidthRequest="30"
                                         Margin="0,0,6,0"
                                         BackgroundColor="Transparent"
                                         HorizontalOptions="End"
                                         Source="close_eye">
                                            <ImageButton.Triggers>
                                                <EventTrigger Event="Clicked">
                                                    <local:ShowPasswordTriggerAction ShowIcon="open_eye"
                                                         HideIcon="close_eye"
                                                         x:Name="ShowPasswordActualTrigger"/>
                                                </EventTrigger>
                                            </ImageButton.Triggers>
                                        </ImageButton>

                                    </Grid>

                                </Frame>
                                <Label Text="" Margin="6,-4,0,0" TextColor="White" FontSize="12" x:Name="PasswordValidator" IsVisible="false"/>
                                <Frame
                                 x:Name="EmailBtnFrame"
                                 Padding="15"
                                 BackgroundColor="White"
                                 CornerRadius="{OnPlatform Android='10', iOS='10'}"
                                 HorizontalOptions="FillAndExpand">
                                    <StackLayout
                                    Padding="0"
                                    Orientation="Horizontal"
                                    Spacing="10">
                                        <Image HeightRequest="20" Source="mail_icon" />
                                        <Label
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        Text="Create free account"
                                        TextColor="Black"
                                        VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="CreateAccountByEmail"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                            </StackLayout>
                            <StackLayout
                            Margin="20,0"
                            HorizontalOptions="FillAndExpand"
                            Orientation="Horizontal">
                                <BoxView
                                HeightRequest="0.6"
                                Margin="0,3,0,0"
                                HorizontalOptions="FillAndExpand"
                                VerticalOptions="CenterAndExpand"
                                Color="White" />
                                <Label
                                 x:Name="LblOr"
                                 FontSize="15"
                                Text="OR"
                                TextColor="White"
                                VerticalTextAlignment="Center" />
                                <BoxView
                                HeightRequest="0.6"
                                Margin="0,3,0,0"
                                HorizontalOptions="FillAndExpand"
                                VerticalOptions="CenterAndExpand"
                                Color="White" />
                            </StackLayout>
                            <StackLayout
                            HorizontalOptions="FillAndExpand"
                            Orientation="Vertical"
                            Spacing="10"
                            VerticalOptions="EndAndExpand">
                                <Frame
                                 x:Name="GoogleBtnFrame"
                                 Padding="15"
                                    BorderColor="#db3737"
                                 BackgroundColor="#db3737"
                                 CornerRadius="{OnPlatform Android='10', iOS='10'}"
                                 HorizontalOptions="FillAndExpand">
                                    <StackLayout
                                     Padding="0"
                                     Orientation="Horizontal"
                                     Spacing="10">
                                        <Image HeightRequest="20" Source="google_icon" />
                                        <Label
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        Text="Join free with Google"
                                        TextColor="White"
                                        VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="CreateAccountByGmail"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <Frame
                                 x:Name="FacebookBtnFrame"
                                 IsVisible="{OnPlatform Android='false' , iOS='false'}"
                                 Padding="15"
                                    BorderColor="#4768AD"
                                 BackgroundColor="#4768AD"
                                 CornerRadius="{OnPlatform Android='10', iOS='10'}"
                                 HorizontalOptions="FillAndExpand">
                                    <StackLayout
                                    Padding="0"
                                    Orientation="Horizontal"
                                    Spacing="10">
                                        <Image HeightRequest="20" Source="facebook_icon" />
                                        <Label
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        Text="Join free with Facebook"
                                        TextColor="White"
                                        VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="CreateAccountByFacebook"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <Frame
                                x:Name="AppleBtnFrame"
                                IsVisible="{OnPlatform Android='false' , iOS='true'}"
                                Padding="15"
                                    BorderColor="Black"
                                BackgroundColor="Black"
                                HorizontalOptions="FillAndExpand">
                                    <StackLayout
                                    Padding="0"
                                    Orientation="Horizontal"
                                    Spacing="10">
                                        <Image HeightRequest="20" Source="apple" />
                                        <Label
                                        FontSize="16"
                                        FontAttributes="Bold"
                                        HorizontalOptions="FillAndExpand"
                                        HorizontalTextAlignment="Center"
                                        Text="Join free with Apple"
                                        TextColor="White"
                                        VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.CornerRadius>
                                        <OnPlatform x:TypeArguments="x:Single">
                                            <On Platform="iOS" Value="10" />
                                            <On Platform="Android" Value="10" />
                                        </OnPlatform>
                                    </Frame.CornerRadius>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="CreateAccountByApple"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                            </StackLayout>
                            <StackLayout 
                            Margin="0,10,0,10"
                             HeightRequest="48"
                            Orientation="Horizontal"
                            HorizontalOptions="Center"
                            Spacing="3">
                                <Label 
                                 VerticalTextAlignment="Start"
                                 HorizontalOptions="CenterAndExpand"
                                 FontSize="Small"
                                 Padding="0"
                                 Margin="0"
                                 TextColor="LightGray" 
                                 Text="Already have an account?"/>
                                <Label 
                                VerticalTextAlignment="Start"
                                TextDecorations="Underline"
                                Margin="0"
                                Padding="0"
                                Text="Log in"
                                FontSize="15"
                                FontAttributes="Bold"
                                TextColor="LightGray">
                                    
                                </Label>
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="Login_btn_clicked"/>
                                </StackLayout.GestureRecognizers>
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>
                    <StackLayout  Spacing="2" HorizontalOptions="CenterAndExpand" VerticalOptions="EndAndExpand" Orientation="Vertical" >
                        <Label x:Name="ByContinueAgree" TextColor="White" HorizontalOptions="CenterAndExpand" Text="By continuing, you agree to our " FontSize="13" Style="{StaticResource LabelStyle}"/>
                        <StackLayout x:Name="TermsPrivacyPolicy" HorizontalOptions="CenterAndExpand" VerticalOptions="End" Orientation="Horizontal">
                            <Label x:Name="TermsOfUse" TextColor="{x:Static constnats:AppThemeConstants.SharpBlueColor}" Text="terms of use" FontSize="13">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="TermsClicked"/>
                                </Label.GestureRecognizers>
                            </Label>
                            <Label x:Name="LblAnd" TextColor="White" Text=" and " FontSize="13" Style="{StaticResource LabelStyle}"/>
                            <Label x:Name="PrivacyPolicy" FontSize="13" Text="privacy policy." TextColor="{x:Static constnats:AppThemeConstants.SharpBlueColor}" Margin="0,0,0,0">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="PrivacyClicked"/>
                                </Label.GestureRecognizers>
                            </Label>
                        </StackLayout>
                    </StackLayout>
                </StackLayout>
            </ScrollView>
        </Grid>

    </StackLayout>
</ContentPage>