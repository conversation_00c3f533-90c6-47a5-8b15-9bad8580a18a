﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
               xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
               xmlns:t="clr-namespace:DrMaxMuscle.Layout"
                    BackgroundColor="White"
                   IsAnimationEnabled="False"
                                 xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
   
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.PlateCalculatorPopup">
    <AbsoluteLayout IgnoreSafeArea="True">
            <StackLayout IgnoreSafeArea="True" Padding="0,0,0,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
            <ffimageloading:CachedImage Source="nav.png" Aspect="Fill" ErrorPlaceholder="backgroundblack.png" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand"/>
            </StackLayout>
        <StackLayout
            x:Name="PlateView"
            Padding="20,50,10,0"
            AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
            BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">
            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
            </StackLayout.GestureRecognizers>
            <VerticalStackLayout
                VerticalOptions="StartAndExpand">
                
                    <StackLayout
                        x:Name="PlateStack"
                        Margin="0,30,0,0"
                    VerticalOptions="Start"
                    HeightRequest="160"
                        Orientation="Horizontal"
                        HorizontalOptions="CenterAndExpand"
                        >
                    </StackLayout>
                    <!--<t:DrMuscleButton
                        Text="Edit  "
                        ContentLayout="Top,8"
                        FontSize="15"
                        x:Name="EditButton"
                        ImageSource="edit_plate.png"
                        BackgroundColor="Transparent"
                        HorizontalOptions="Center"
                        TextColor="White"
                        Padding="0,0"
                        Clicked="Edit_Clicked"
                        VerticalOptions="Center"
                        Margin="10,0,0,0" />-->
                
                <StackLayout
                    Margin="0,20,0,0"
                    Padding="15,0,15,0"
                    Orientation="Vertical"
                    HorizontalOptions="FillAndExpand">
                    <Label
                        x:Name="LblSlideToAdjust"
                        Text="Slide to adjust bar weight"
                        FontSize="15"
                        TextColor="White"
                        Style="{StaticResource LabelStyle}"
                        HorizontalOptions="Center" />
                    <StackLayout
                        Margin="0,0,0,20"
                        Orientation="Vertical"
                        Spacing="30"
                        HorizontalOptions="FillAndExpand">
                        <StackLayout
                            x:Name="SliderBar"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="Center"
                            Orientation="Horizontal"
                            >
                            <ImageButton Source="ic_minus.png" HorizontalOptions="Start" WidthRequest="60" HeightRequest="60" Aspect="AspectFit" BackgroundColor="Transparent" Clicked="DecreaseBarWeight_Clicked" />

                            <Label
                                Text="0"
                                FontSize="15"
                                Style="{StaticResource LabelStyle}"
                                TextColor="White"
                                HorizontalOptions="Start"
                                WidthRequest="25"
                                x:Name="LblMinimum"
                                VerticalOptions="Center" />

                                <Slider
                                Minimum="0"
                                x:Name="slider"
                                MinimumTrackColor="{OnPlatform Android='#E1E1E1',iOS='#CCFFFFFF'}"
                                MaximumTrackColor="{OnPlatform Android='#CCFFFFFF',iOS='#66FFFFFF'}"
                                VerticalOptions="Center"
                                HorizontalOptions="FillAndExpand"
                                ThumbColor="{StaticResource Gray100}"
                                ValueChanged="OnSliderValueChanged"
                                DragCompleted="slider_DragCompleted"
                                />
                            <Label
                                x:Name="LblSlider"
                                Text="0"
                                TextColor="White"
                                FontSize="15"
                                HorizontalOptions="End"
                                WidthRequest="38"
                                Style="{StaticResource LabelStyle}"
                                VerticalOptions="Center" />
                        <ImageButton Source="ic_plus.png" HorizontalOptions="End" WidthRequest="60" HeightRequest="60" BackgroundColor="Transparent" Aspect="AspectFit"  Clicked="IncreaseBarWeight_Clicked" />

                        </StackLayout>
                        <Label
                            x:Name="LblBarWeight"
                            HorizontalTextAlignment="Center"
                            Style="{StaticResource LabelStyle}"
                            TextColor="White"
                            FontSize="38"
                            MaxLines="2"
                            WidthRequest="100">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span
                                        Text=""
                                        x:Name="BarWeight"
                                        FontSize="33"
                                        Style="{StaticResource LabelStyle}" TextColor="White"/>
                                    <Span
                                        Text=""
                                        x:Name="BarWeightText"
                                        FontSize="15"
                                        TextColor="White"
                                        Style="{StaticResource LabelStyle}" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Label
                            x:Name="LblPlatesWeight"
                            HorizontalTextAlignment="Center"
                            Style="{StaticResource LabelStyle}"
                            FontSize="38"
                            TextColor="White"
                            MaxLines="2"
                            WidthRequest="140">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span
                                        Text=""
                                        x:Name="PlateWeight"
                                        FontSize="33"
                                        TextColor="White"
                                        Style="{StaticResource LabelStyle}" />
                                    <Span
                                        Text="Plates"
                                        x:Name="PlateWeightText"
                                        FontSize="15"
                                        TextColor="White"
                                        Style="{StaticResource LabelStyle}" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                    </StackLayout>
                </StackLayout>
            </VerticalStackLayout>
            <StackLayout
                VerticalOptions="End"
                HorizontalOptions="CenterAndExpand"
                Margin="0,0,0,0"
                Spacing="0"
                Padding="25.0">
                <StackLayout.GestureRecognizers>
                    <TapGestureRecognizer Tapped="HideTapGestureRecognizer_Tapped" />

                </StackLayout.GestureRecognizers>
                <!--<ImageButton
                    x:Name="HidePlateButton"
                    Source="hide.png"
                    BackgroundColor="Transparent"
                    HorizontalOptions="CenterAndExpand"
                    Clicked="ButtonPlateHide_Clicked"
                    Padding="0,5" />-->
                <ffimageloading:CachedImage
                    Source="hide"
                    WidthRequest="35"
                    HeightRequest="35"/>
                <Button TextColor="White"
                        Text="Hide"
                        FontSize="20"
                        Clicked="ButtonPlateHide_Clicked"
                        BackgroundColor="Transparent" />
            </StackLayout>
        </StackLayout>
             </AbsoluteLayout>
</toolkit:PopupPage>

