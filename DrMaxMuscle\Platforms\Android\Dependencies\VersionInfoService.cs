﻿using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Util;
using DrMaxMuscle.Dependencies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Android.Provider.Settings;

namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class VersionInfoService : IVersionInfoService
    {
        string id = string.Empty;

        public VersionInfoService()
        {
        }

        public string GetDeviceUniqueId()
        {
            if (!string.IsNullOrWhiteSpace(id))
                return id;

            id = Build.Serial;
            if (string.IsNullOrWhiteSpace(id) || id == Build.Unknown || id == "0")
            {
                try
                {
                    var context = global::Android.App.Application.Context;
                    id = Secure.GetString(context.ContentResolver, Secure.AndroidId);
                }
                catch (Exception ex)
                {
                    Log.Warn("DeviceInfo", "Unable to get id: " + ex.ToString());
                }
            }
            return id;
        }

        public int GetVersionInfo()
        {
            Context context = global::Android.App.Application.Context; ;
            PackageManager manager = context.PackageManager;
            PackageInfo i = manager.GetPackageInfo(context.PackageName, 0);
            return i.VersionCode;
        }
    }
}
