<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             x:Class="DrMaxMuscle.Cells.WelcomeCell">
    <StackLayout Padding="15"
             Rotation="180">
        <Label HorizontalOptions="CenterAndExpand"
           x:Name="LblWelcome"
           HorizontalTextAlignment="{OnPlatform Android='Start',iOS='End'}"
           Text=""
           TextColor="{x:Static app:AppThemeConstants.BlueColor}">
        </Label>
        <Label HorizontalOptions="End"
           x:Name="LblGroupChat"
           HorizontalTextAlignment="{OnPlatform Android='Start',iOS='Start'}"
           Text="Preview group chat"
           TextDecorations="Underline"
           TextColor="{x:Static app:AppThemeConstants.BlueColor}">
            
            <Label.GestureRecognizers>
                <TapGestureRecognizer Tapped="GroupChatTapped" />
            </Label.GestureRecognizers>
        </Label>
        <!--<BoxView BackgroundColor="Silver" HeightRequest="0.5" HorizontalOptions="FillAndExpand" />-->
    </StackLayout>
</ContentView>
