﻿using Acr.UserDialogs;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMuscleWebApiSharedModel;
using System.Collections.ObjectModel;

namespace DrMaxMuscle.Screens.Workouts;

public partial class AddWorkoutToWorkoutOrderPage : ContentPage
{
    public ObservableCollection<SelectableWorkoutTemplateModel> workoutItems = new ObservableCollection<SelectableWorkoutTemplateModel>();

    public AddWorkoutToWorkoutOrderPage()
    {
        InitializeComponent();

        WorkoutListView.ItemsSource = workoutItems;

        NextButton.Clicked += NextButton_Clicked;
        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseWorkout;
        NextButton.Text = AppResources.Next;
    }

    public  async void OnBeforeShow()
    {
        DependencyService.Get<IFirebase>().SetScreenName("add_wotkout_to_workout_order");

        await UpdateExerciseList();
    }

    void Handle_Toggled(object sender, ToggledEventArgs e)
    {
        //throw new NotImplementedException();
    }

    private async void NextButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates = new List<WorkoutTemplateModel>();

            foreach (SelectableWorkoutTemplateModel ex in workoutItems.Where(i => i.IsSelected))
            {
                CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Add(ex);
            }
            if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Count == 0)
            {
                await HelperClass.DisplayCustomPopupForResult("Choose workouts",
                        $"Choose 1 or more workouts to continue.","OK","");
            return;
            }
            await Navigation.PushAsync(new ChooseWorkoutOrder());
        }
        catch(Exception ex)
        {

        }
    }

    private async Task UpdateExerciseList()
    {
        workoutItems.Clear();
        try
        {
            if (LocalDBManager.Instance.GetDBSetting("Equipment") == null)
                LocalDBManager.Instance.SetDBSetting("Equipment", "false");

            if (LocalDBManager.Instance.GetDBSetting("ChinUp") == null)
                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");

            if (LocalDBManager.Instance.GetDBSetting("Dumbbell") == null)
                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");

            if (LocalDBManager.Instance.GetDBSetting("Plate") == null)
                LocalDBManager.Instance.SetDBSetting("Plate", "true");

            if (LocalDBManager.Instance.GetDBSetting("Pully") == null)
                LocalDBManager.Instance.SetDBSetting("Pully", "true");

            GetUserWorkoutTemplateResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomizedUserWorkout(new EquipmentModel()
            {
                IsEquipmentEnabled = LocalDBManager.Instance.GetDBSetting("Equipment").Value == "true",
                IsChinUpBarEnabled = LocalDBManager.Instance.GetDBSetting("ChinUp").Value == "true",
                IsPullyEnabled = LocalDBManager.Instance.GetDBSetting("Pully").Value == "true",
                IsPlateEnabled = LocalDBManager.Instance.GetDBSetting("Plate").Value == "true"
            });
            //GetUserWorkoutTemplateResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserWorkout();
            foreach (WorkoutTemplateModel e in itemsSource.Workouts.Where(w => w.IsSystemExercise == false))
            {
                SelectableWorkoutTemplateModel em = new SelectableWorkoutTemplateModel();
                em.Id = e.Id;
                em.IsSystemExercise = e.IsSystemExercise;
                em.Label = e.Label;
                em.IsSelected = CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id == -1 ? false : CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Select(ex => ex.Id).Contains(e.Id);
                workoutItems.Add(em);
            }
            if (workoutItems.Count == 0)
            {
                EmptyWorkouts.IsVisible = true;
                stkWorkout.IsVisible = false;
            }
            else
            {
                EmptyWorkouts.IsVisible = false;
                stkWorkout.IsVisible = true;
            }
        }
        catch (Exception e)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
            // await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
        }
    }
}

public class SelectableWorkoutTemplateModel : WorkoutTemplateModel
{
    public bool IsSelected { get; set; }
}
