﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             VerticalOptions="FillAndExpand"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             xmlns:fftransformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Maui"
             xmlns:layout="clr-namespace:DrMaxMuscle.Layout"
             xmlns:control="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Layout.RightSideMasterPage">

    <StackLayout BackgroundColor="#AA333333"  HorizontalOptions="FillAndExpand">
        <StackLayout.GestureRecognizers>
            <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_1" />

        </StackLayout.GestureRecognizers>
        <Grid x:Name="SlideMenuGrid" BackgroundColor="White" Padding="0" VerticalOptions="FillAndExpand" WidthRequest="250"
          HorizontalOptions="End" IsVisible="False">
        <StackLayout>
            <StackLayout
        x:Name="GeneralStack"
        Orientation="Horizontal"
        VerticalOptions="FillAndExpand">
                <StackLayout
            Orientation="Vertical"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
                    <StackLayout
                Orientation="Vertical"
                VerticalOptions="Start"
                Spacing="1">
                        <!--<layout:DrMuscleButton x:Name="HomeButton" Text="Home screen" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <Frame
                    VerticalOptions="Start"
                    x:Name="PancakeContainer"
                    IsClippedToBounds="true"
                    Style="{StaticResource GradientFrameStyleBlue}"
                    HorizontalOptions="End"
                    Margin="0"
                    Padding="0,10,0,0"
                    CornerRadius="0">

                            <Grid
                        Padding="15,0,0,15"
                        RowSpacing="12">
                                <Grid.RowDefinitions>
                                    <RowDefinition
                                Height="35" />
                                    <RowDefinition
                                Height="35" />

                                    <RowDefinition
                                Height="Auto" />
                                    <RowDefinition
                                Height="Auto" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                                Width="60" />
                                    <ColumnDefinition
                                Width="*" />
                                </Grid.ColumnDefinitions>
                                <ffimageloading:CachedImage
                            x:Name="ImgProfile"
                            Grid.Row="0"
                            Grid.Column="0"
                            Grid.RowSpan="2"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            WidthRequest="60"
                            HeightRequest="60"
                            DownsampleToViewSize="true"
                                    ErrorPlaceholder="backgroundblack.png"
                            Source="me_tab.png">
                                    <ffimageloading:CachedImage.Transformations>
                                        <fftransformations:CircleTransformation />
                                    </ffimageloading:CachedImage.Transformations>
                                </ffimageloading:CachedImage>
                                <StackLayout
                            Grid.Row="0"
                            Grid.Column="1"
                            Grid.RowSpan="2"
                            Spacing="2"
                            HorizontalOptions="Start"
                            VerticalOptions="Center">
                                    <Label
                                TextColor="#CCFFFFFF"
                                x:Name="LblNmae"
                                Text="Etienee Juneau"
                                Margin="0,0,0,0"
                                VerticalOptions="End"
                                VerticalTextAlignment="End"
                                FontSize="21" />
                                    <Label
                                TextColor="#77FFFFFF"
                                x:Name="LblDoneWorkout"
                                Text=""
                                FontSize="15"
                                VerticalOptions="Start" />
                                </StackLayout>
                                <BoxView
                            Grid.Row="0"
                            Grid.Column="0"
                            Grid.RowSpan="2"
                                    Color="Transparent"
                            Grid.ColumnSpan="2"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="FillAndExpand"
                            BackgroundColor="Transparent">
                                    <BoxView.GestureRecognizers>
                                        <TapGestureRecognizer
                                    x:Name="MeGesture" />
                                    </BoxView.GestureRecognizers>
                                </BoxView>
                                <Label
                            Grid.Row="2"
                            Grid.Column="0"
                            Margin="5,0,0,0"
                            Grid.ColumnSpan="2"
                            x:Name="SettingsButton"
                            Text="Settings"
                            BackgroundColor="Transparent"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Start"
                            FontSize="18">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer
                                    x:Name="SettingGesture" />
                                    </Label.GestureRecognizers>
                                </Label>
                                <Label
                            Grid.Row="3"
                            Grid.Column="0"
                            Margin="5,0,0,0"
                            Grid.ColumnSpan="2"
                            x:Name="SubscriptionInfosButton"
                            Text="Subscription info"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Start"
                            FontSize="18">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer
                                    x:Name="SubscriptionGesture" />
                                    </Label.GestureRecognizers>
                                </Label>
                            </Grid>
                        </Frame>
                        <Grid
                    RowSpacing="15"
                    Padding="15,15,14,5">
                            <Label
                        Grid.Row="0"
                        Grid.Column="0"
                        Margin="5,0,0,0"
                        x:Name="ChartsButton"
                        Text="Charts"
                        BackgroundColor="Transparent"
                        TextColor="#26262B"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        FontSize="21">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                x:Name="ChartsGesture" />
                                </Label.GestureRecognizers>
                            </Label>
                            <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="5,0,0,0"
                        x:Name="HistoryButton"
                        Text="History"
                        BackgroundColor="Transparent"
                        TextColor="#26262B"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        FontSize="21">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                x:Name="HistoryGesture" />
                                </Label.GestureRecognizers>
                            </Label>
                            <!--<Label Grid.Row="2" Grid.Column="0" Margin="5,0,0,0" x:Name="WebButton" Text="Web app" BackgroundColor="Transparent" TextColor="#26262B" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Start" FontSize="21">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer x:Name="WebGesture" />
                            </Label.GestureRecognizers>
                        </Label>-->
                            <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Margin="5,0,0,0"
                        x:Name="TellAFriend"
                        Text="Share free trial"
                        BackgroundColor="Transparent"
                        TextColor="#26262B"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        FontSize="21">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                x:Name="TellAFriendGesture" />
                                </Label.GestureRecognizers>
                            </Label>

                            <Label
                        Grid.Row="3"
                        Grid.Column="0"
                        Margin="5,0,0,0"
                        x:Name="FAQButton"
                        Text="FAQ"
                        BackgroundColor="Transparent"
                        TextColor="#26262B"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        FontSize="21">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                x:Name="FAQGesture" />
                                </Label.GestureRecognizers>
                            </Label>
                            <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Margin="5,0,0,0"
                        x:Name="WebReviews"
                        Text="Reviews"
                        BackgroundColor="Transparent"
                        TextColor="#26262B"
                        HorizontalOptions="FillAndExpand"
                        HorizontalTextAlignment="Start"
                        FontSize="21">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer
                                x:Name="WebGestures" />
                                </Label.GestureRecognizers>
                            </Label>
                        </Grid>
                        <!--<layout:DrMuscleButton x:Name="MeButton" Text="Me" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!--<layout:DrMuscleButton x:Name="HistoryButton" Text="History" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />-->
                        <!--<BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!--<layout:DrMuscleButton x:Name="SettingsButton" Text="Settings" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!--<layout:DrMuscleButton x:Name="WebButton" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />-->
                        <!--<BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!--<layout:DrMuscleButton x:Name="SubscriptionInfosButton" Text="Subscription info" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!--<layout:DrMuscleButton x:Name="OldHomeButton" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->

                        <!--<layout:DrMuscleButton x:Name="FAQButton" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />-->
                        <!--<BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->
                        <!-- <layout:DrMuscleButton x:Name="ChatButton" Text="Chat" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->

                        <!--<layout:DrMuscleButton x:Name="EmailUsButton" Text="Email support" Margin="20,18,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                </BoxView>-->

                    </StackLayout>
                    <StackLayout
                BackgroundColor="#F0F0F0"
                VerticalOptions="FillAndExpand">
                        <!--<Label
                        Text="User reviews"
                        Margin="20,10,0,0"
                        IsVisible="true"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                        Font="Bold,18"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" />-->
                        <Frame
                    Padding="25,5,25,10"
                    Margin="20,5,20,0"
                    BackgroundColor="White"
                    CornerRadius="8"
                    HasShadow="False">
                            <StackLayout>
                                <Image
                            Source="stars_5.png"
                            WidthRequest="120"
                            Aspect="AspectFit"
                            HorizontalOptions="Start" />
                                <Label
                            x:Name="LblReview"
                            Text="For basic strength training this app out performs the many methods/apps I have tried in my 30+ years of body/strength training. What I like the most is that it take the brain work out of weights, reps, and sets (if you follow a structured workout). What I like even more is the exceptional customer engagement."
                            LineBreakMode="TailTruncation"
                            Style="{StaticResource LabelStyle}" />
                                <Label
                            x:Name="LblReviewerName"
                            Text="A McM"
                            MaxLines="1"
                            LineBreakMode="TailTruncation"
                            Style="{StaticResource LabelStyle}"
                            FontAttributes="Bold"
                            FontSize="15" />
                            </StackLayout>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="TapMoreReviews_Tapped" />
                            </Frame.GestureRecognizers>
                        </Frame>
                        <!--<Label
                        Text="Read more reviews &#x226B;"
                        Margin="20,2,0,10"
                        IsVisible="true"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                        Font="Bold,16"
                        Style="{StaticResource LabelStyle}"
                        TextColor="Black" >
                         <Label.GestureRecognizers>
                        <TapGestureRecognizer Tapped="TapMoreReviews_Tapped" />
                    </Label.GestureRecognizers>
                        </Label>-->
                    </StackLayout>
                    <StackLayout
                Orientation="Vertical"
                VerticalOptions="End"
                Spacing="14"
                Padding="0">
                        <!--<BoxView HeightRequest="0.5" BackgroundColor="White" HorizontalOptions="FillAndExpand" />-->
                        <Label
                    Grid.Row="1"
                    Grid.Column="0"
                    Margin="20,8,0,0"
                    x:Name="LogOutButton"
                    Text="Log out"
                    BackgroundColor="Transparent"
                    TextColor="#26262B"
                    HorizontalOptions="FillAndExpand"
                    HorizontalTextAlignment="Start"
                    FontSize="18">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            x:Name="LogoutGesture" />
                            </Label.GestureRecognizers>
                        </Label>
                        <!--<layout:DrMuscleButton x:Name="LogOutButton" Text="Log out" Margin="20,18,20,18" Style="{StaticResource slideMenuButtonStyle}" />-->
                        <Label
                    x:Name="VersionInfoLabel"
                    FontSize="14"
                    VerticalOptions="End"
                    HorizontalOptions="Start"
                    Margin="20,0,0,20"
                    TextColor="#26262B">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="Handle_BuildVersionTapped" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                </StackLayout>
            </StackLayout>

            <StackLayout
        x:Name="BotStack"
        Orientation="Horizontal"
        VerticalOptions="FillAndExpand">
                <StackLayout
            Orientation="Vertical"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
                    <StackLayout
                Orientation="Vertical"
                VerticalOptions="StartAndExpand"
                Spacing="1">
                        <layout:DrMuscleButton
                    x:Name="SignInButton"
                    Text="Sign in"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <layout:DrMuscleButton
                    x:Name="SkipDemoButton"
                    Text="Skip demo"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <layout:DrMuscleButton
                    x:Name="CancelButton"
                    Text="Cancel"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>
                        <layout:DrMuscleButton
                    x:Name="RestartDemoButton"
                    Text="Restart demo"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    x:Name="BoxDemoBorder"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>

                        <layout:DrMuscleButton
                    x:Name="LanguageButton"
                    Text="Language"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    x:Name="BoxLanguageBorder"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>
                        <layout:DrMuscleButton
                    x:Name="RestartSetupButton"
                    Text="Restart setup"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    x:Name="BoxSetupBorder"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand"></BoxView>

                        <!--<layout:DrMuscleButton x:Name="NewNUXButton" Text="New NUX" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                    <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                    </BoxView>-->

                    </StackLayout>
                    <StackLayout
                Orientation="Vertical"
                VerticalOptions="EndAndExpand">
                        <Label
                    x:Name="ModeLbl"
                    Text="Normal experience - load beta"
                    Margin="20,20,20,0"
                    FontSize="10"
                    HorizontalOptions="CenterAndExpand">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="Handle_ModeChange" />
                            </Label.GestureRecognizers>
                        </Label>

                        <Label
                    x:Name="VersionInfoLabel1"
                    FontSize="10"
                    VerticalOptions="End"
                    HorizontalOptions="CenterAndExpand"
                    Margin="0,0,0,20">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="Handle_BuildVersionTapped" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                </StackLayout>
            </StackLayout>

            <StackLayout
        x:Name="HomeStack"
        Orientation="Horizontal"
        VerticalOptions="FillAndExpand">
                <StackLayout
            Orientation="Vertical"
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand">
                    <StackLayout
                x:Name="HomeMainStack"
                Orientation="Vertical"
                VerticalOptions="StartAndExpand"
                Spacing="1">
                        <layout:DrMuscleButton
                    x:Name="SharefreeMonthButton"
                    Text="Share 1 month free"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>
                        <!--<layout:DrMuscleButton x:Name="WorkoutsButton" Text="Workouts" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                    <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                    </BoxView>-->
                        <layout:DrMuscleButton
                    x:Name="TiredTodayButton"
                    Text="Tired today"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>
                        <layout:DrMuscleButton
                    x:Name="ShortOnTimeButton"
                    Text="Short on time"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>
                        <!--<layout:DrMuscleButton x:Name="StartWorkoutButton" Text="Start workouts" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                    <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                    </BoxView>-->

                    </StackLayout>
                    <StackLayout
                x:Name="SummaryMainStack"
                Orientation="Vertical"
                VerticalOptions="StartAndExpand"
                Spacing="1">
                        <layout:DrMuscleButton
                    x:Name="MoreStatsButton"
                    Text="View more stats"
                    Margin="20,20,20,0"
                    Style="{StaticResource slideMenuButtonStyle}" />
                        <BoxView
                    Color="Gray"
                    HeightRequest="1"
                    Opacity="0.5"
                    HorizontalOptions="FillAndExpand">
                        </BoxView>

                        <!--<layout:DrMuscleButton x:Name="StartWorkoutButton" Text="Start workouts" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}" />
                    <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand">
                    </BoxView>-->

                    </StackLayout>
                    <StackLayout
                Orientation="Vertical"
                VerticalOptions="EndAndExpand">
                        <Label
                    x:Name="VersionInfoLabel2"
                    FontSize="10"
                    VerticalOptions="End"
                    HorizontalOptions="CenterAndExpand"
                    Margin="0,0,0,20">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer
                            Tapped="Handle_BuildVersionTapped" />
                            </Label.GestureRecognizers>
                        </Label>
                    </StackLayout>
                </StackLayout>
            </StackLayout>

            <StackLayout x:Name="TimerStack"
                         Orientation="Horizontal"
                         VerticalOptions="FillAndExpand">
                <StackLayout.GestureRecognizers>
                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
                </StackLayout.GestureRecognizers>
                <StackLayout Orientation="Vertical"
                             VerticalOptions="FillAndExpand"
                             HorizontalOptions="FillAndExpand" Spacing="{OnPlatform Android=0, iOS=3}">
                    
                    <StackLayout Orientation="Horizontal"
                                 Margin="20,0,20,0" Spacing="8">
                        <layout:DrMuscleButton x:Name="TimerLess"
                                                Text="-"
                                                WidthRequest="50"
                                                HeightRequest="45"
                                                Style="{StaticResource timerButtonStyle}"
                                                Margin="0,0,0,0" />
                        <control:DrEntry x:Name="TimerEntry"
                                        MaxLength="4"
                                        TextColor="Black"
                                        Text="60"
                                        HeightRequest="45"
                                        HorizontalOptions="FillAndExpand"
                                        Keyboard="Numeric"
                                        BackgroundColor="White"
                                        HorizontalTextAlignment="Center"
                                        Focused="TimerEntry_Focused">
                        </control:DrEntry>
                        <layout:DrMuscleButton x:Name="TimerMore"
                                                Text="+"
                                                WidthRequest="50"
                                                HeightRequest="45"
                                                Style="{StaticResource timerButtonStyle}"
                                                Margin="0,0,0,0" />
                    </StackLayout>
                    
                    <Label Text="To apply your custom rest time, turn off the automatch reps below"
                            Margin="20,0"
                            Style="{StaticResource infoText}" />

                        <StackLayout Margin="{OnPlatform Android='20,0,20,0', iOS='20,10,20,0'}" 
                                     Spacing="{OnPlatform Android=0, iOS=6}">
                        <StackLayout Orientation="Horizontal"
                                     Spacing="0"
                                     Margin="0,0,0,0">
                            <Label x:Name="LblVibrate"
                                   FontSize="{OnPlatform Android='15', iOS='17'}"
                                   Text="VIBRATE"
                                   VerticalOptions="Center"
                                   HorizontalOptions="StartAndExpand" />
                                <Switch x:Name="VibrateSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference VibrateSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference VibrateSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                            </StackLayout>

                        <StackLayout Orientation="Horizontal"
                                     Spacing="0"
                                     Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblTimer321Sound"
                                   FontSize="{OnPlatform Android='15', iOS='17'}"
                                   Text="3-2-1"
                                   VerticalOptions="Center"
                                   HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="Timer123Switch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference Timer123Switch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference Timer123Switch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblRepsSound"
                                    FontSize="{OnPlatform Android='15', iOS='17'}"
                                    Text="REPS TO DO"
                                    VerticalOptions="Center"
                                    HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="RepsSoundSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference RepsSoundSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference RepsSoundSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblSound"
                                    FontSize="{OnPlatform Android='15', iOS='17'}"
                                    Text="DING"
                                    VerticalOptions="Center"
                                    HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="SoundSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference SoundSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference SoundSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>

                        <StackLayout Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblFullScreen"
                                    FontSize="{OnPlatform Android='15', iOS='17'}"
                                    Text="FULLSCREEN"
                                    VerticalOptions="Center"
                                    HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="FullscreenSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference FullscreenSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference FullscreenSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>
                        
                        <StackLayout Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblAutoStart"
                                    FontSize="{OnPlatform Android='15', iOS='17'}"
                                    Text="AUTOSTART"
                                    VerticalOptions="Center"
                                    HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="AutostartSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference AutostartSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference AutostartSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>

                            <StackLayout Orientation="Horizontal"
                                         Spacing="0"
                                         Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                                <Label x:Name="LblAutoTimer"
                                       FontSize="{OnPlatform Android='15', iOS='17'}"
                                       Text="BETWEEN EXERCISES"
                                       VerticalOptions="Center"
                                       HorizontalOptions="StartAndExpand" 
                                       MaxLines="1"/>
                                <Switch x:Name="BetweenExercisesSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                    <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference BetweenExercisesSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference BetweenExercisesSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                            </StackLayout>

                        <StackLayout x:Name="StackAutoMatch"
                                    Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="{OnPlatform Android='0,0,0,0', iOS='0,10,0,0'}">
                            <Label x:Name="LblAutomatchReps"
                                   FontSize="{OnPlatform Android='15', iOS='17'}"
                                   Text="AUTOMATCH REPS"
                                   VerticalOptions="Center"
                                   HorizontalOptions="StartAndExpand" />
                            <Switch x:Name="AutosetSwitch"
                                        IsToggled="true"
                                        HorizontalOptions="End"
                                        VerticalOptions="Center">
                                   <Switch.Triggers>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference AutosetSwitch}, Path=IsToggled}"
                                                     Value="True">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#61e845', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                        </DataTrigger>
                                        <DataTrigger TargetType="Switch"
                                                     Binding="{Binding Source={x:Reference AutosetSwitch}, Path=IsToggled}"
                                                     Value="False">
                                            <Setter Property="ThumbColor"
                                                    Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                            <Setter Property="OnColor"
                                                    Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                        </DataTrigger>
                                    </Switch.Triggers>
                                </Switch>
                        </StackLayout>
                        
                        <StackLayout Orientation="Horizontal"
                                    Spacing="0"
                                    Margin="0,0,0,0">
                            <Label x:Name="LblAutomaticallyChangeTimer"
                                   HorizontalOptions="FillAndExpand"
                                   Text="(Automatically change timer duration to match recommended reps and optimize muscle hypertrophy.)"
                                   Style="{StaticResource infoText}" />
                        </StackLayout>
                        
                        <StackLayout Orientation="Horizontal"
                                     Spacing="0"
                                     Margin="0,0,0,0">
                            <Label x:Name="LearnMoreLink"
                                   Text="Learn more"
                                   Style="{StaticResource infoText}"
                                   TextColor="Blue" />
                        </StackLayout>
                    </StackLayout>
                    
                    <StackLayout Margin="20,15,20,0">
                        <layout:DrMuscleButton x:Name="TimerStartButton"
                                               Text="START"
                                               HeightRequest="45"
                                               Style="{StaticResource timerButtonStyle}" />
                    </StackLayout>
                </StackLayout>
            </StackLayout>

            <StackLayout x:Name="FeaturedStack"
                        Orientation="Horizontal"
                        VerticalOptions="FillAndExpand"
                        IsVisible="False">
                <StackLayout
            Orientation="Vertical"
            VerticalOptions="StartAndExpand"
            Spacing="1">
                    <layout:DrMuscleButton
                x:Name="FeatureWorkout"
                Text="Featured"
                Margin="20,20,20,0"
                Style="{StaticResource slideMenuButtonStyle}"
                Clicked="BtnFeatureWorkout_Clicked"/>
                </StackLayout>
            </StackLayout>
            
            <StackLayout x:Name="MealPlanStack"
                        Orientation="Horizontal"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        IsVisible="False">
                <StackLayout
            Orientation="Vertical"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            Spacing="1">
                    <layout:DrMuscleButton
                x:Name="resetPlan"
                Text="Reset plan"
                Margin="20,20,20,0"
                Style="{StaticResource slideMenuButtonStyle}"
                Clicked="BtnResetPlan_Clicked"/>
                    <BoxView
                Color="Gray"
                HeightRequest="1"
                Opacity="0.5"
                HorizontalOptions="FillAndExpand">
                    </BoxView>
                    <!--<layout:DrMuscleButton
                IsVisible="false"
                x:Name="changePlan"
                Text="Change plan"
                Margin="20,20,20,0"
                Style="{StaticResource slideMenuButtonStyle}"
                Clicked="BtnChangePlan_Clicked"/>
            <BoxView
                IsVisible="false"
                x:Name="changePlanBox"
                Color="Gray"
                HeightRequest="1"
                Opacity="0.5"
                HorizontalOptions="FillAndExpand">
            </BoxView>-->
                    <layout:DrMuscleButton
                x:Name="reviewTips"
                Text="Replay tips"
                Margin="20,20,20,0"
                Style="{StaticResource slideMenuButtonStyle}"
                Clicked="BtnFReviewTips_Clicked"/>
                    <BoxView
                Color="Gray"
                HeightRequest="1"
                Opacity="0.5"
                HorizontalOptions="FillAndExpand">
                    </BoxView>
                </StackLayout>
            </StackLayout>
        </StackLayout>
    </Grid>
</StackLayout>
</ContentView>  
