﻿using Microsoft.Maui.Networking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Views;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Layout
{
    public class DrMusclePage : ContentPage
    {
        public ToolbarItem timerToolbarItem;
        private ToolbarItem generalToolbarItem;
        protected bool HasSlideMenu = true;
        private bool isPresented = false;
        public DrMusclePage()
        {
            Timer.Instance.OnTimerChange += OnTimerChange;
            Timer.Instance.OnTimerDone += OnTimerDone;
            Timer.Instance.OnTimerStop += OnTimerStop;
        }
        public void OnTimerChange(int remaining)
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                // uncomment code please
                //timerToolbarItem.Text = remaining.ToString();
            });
        }
        protected void OnTimerDone()
        {
            try
            {
                try
                {
                    var navigation = (((MainTabbedPage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).CurrentPage.Navigation);
                    if (navigation.NavigationStack.Last() is ChatPage || navigation.NavigationStack.Last() is GroupChatPage || navigation.NavigationStack.Last() is SupportPage)
                        return;

                }
                catch (Exception ex)
                {

                }
                if (ToolbarItems.Count > 0)
                {
                    var index = 0;
                    if (this.ToolbarItems.Count == 2)
                    {
                        index = 1;
                    }
                    this.ToolbarItems.RemoveAt(index);
                    // Uncomment code please
                    //timerToolbarItem = new ToolbarItem("", "stopwatch.png", SlideTimerAction, ToolbarItemOrder.Primary, 0);
                    //this.ToolbarItems.Insert(index, timerToolbarItem);
                }
            }
            catch (Exception ex)
            {

            }

        }

        protected void OnTimerStop()
        {
            try
            {
                // uncomment code please
                //if (ToolbarItems.Count > 0)
                //{
                //    var index = 0;
                //    if (this.ToolbarItems.Count == 2)
                //    {
                //        index = 1;
                //    }
                //    this.ToolbarItems.RemoveAt(index);
                //    timerToolbarItem = new ToolbarItem("", "stopwatch.png", SlideTimerAction, ToolbarItemOrder.Primary, 0);
                //    this.ToolbarItems.Insert(index, timerToolbarItem);
                //}
            }
            catch (Exception ex)
            {

            }
        }
        protected async Task ConnectionErrorPopup()
        {
            if (isPresented)
                return;
            isPresented = true;
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     OkText = "Try again"
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
            isPresented = false;
        }

        protected async Task<bool> ConnectionErrorConfirmPopup()
        {
            if (isPresented)
                return true;
            isPresented = true;
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     OkText = "Try again"
            // });

           var results = await HelperClass.DisplayCustomPopupForResult("Loading error",  "Slow or no connection. Please check and try again.", "Retry loading","Cancel");


            // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
            // {
            //     Title = "Loading error",
            //     Message = "Slow or no connection. Please check and try again.",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Retry loading",
            //     CancelText = "Cancel",

            // };
            // var results = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
            isPresented = false;
            if (results == PopupAction.OK)
            {
                return true;
            }

            return false;
        }

        public async Task<bool> IsGoogleReachable()
        {


            try
            {
                return Connectivity.NetworkAccess == NetworkAccess.Internet;
                //if (Device.RuntimePlatform.Equals(Device.iOS))
                //    return await CrossConnectivity.Current.IsReachable("https://www.google.com/");

                //return await DependencyService.Get<INetworkCheck>().IsNetworkAvailable();
            }
            catch (Exception)
            {
                // An exception occurred (e.g., no internet connection)
                return false;
            }
        }

        public async Task<bool> IsActualGoogleReachable()
        {

            // uncomment code please Remove
            return true;
            try
            {
                if (Device.RuntimePlatform.Equals(Device.iOS))
                    return await IsUrlReachableAsync("https://www.google.com/");
                // uncomment code please
                //return await DependencyService.Get<INetworkCheck>().IsNetworkAvailable();
            }
            catch (Exception)
            {
                return false;
            }
        }
        private async Task<bool> IsUrlReachableAsync(string url)
        {
            try
            {
                using (var httpClient = new HttpClient())
                {
                    var response = await httpClient.GetAsync(url);
                    return response.IsSuccessStatusCode;
                }
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsActiveInternet()
        {
            try
            {

                return Connectivity.NetworkAccess == NetworkAccess.Internet;
                if (Device.RuntimePlatform.Equals(Device.iOS))
                    return await IsUrlReachableAsync("https://www.google.com/");
                // uncomment code please
                //return await DependencyService.Get<INetworkCheck>().IsNetworkAvailable();
            }
            catch (Exception)
            {
                // An exception occurred (e.g., no internet connection)
                return false;
            }
        }
    }
}
