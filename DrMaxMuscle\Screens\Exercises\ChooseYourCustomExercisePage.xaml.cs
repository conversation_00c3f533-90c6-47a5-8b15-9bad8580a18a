﻿using System.Collections.ObjectModel;
using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Screens.Exercises;

public partial class ChooseYourCustomExercisePage : ContentPage
{
    public ObservableRangeCollection<BodyPartSection> ExeList { get; set; }
            = new ObservableRangeCollection<BodyPartSection>();
    private List<ExerciceModel> exercises;
    public ObservableCollection<ExerciceModel> exerciseItems = new ObservableCollection<ExerciceModel>();
    public ObservableCollection<ExerciceModel> exerciseItemsResult = new ObservableCollection<ExerciceModel>();

    public ObservableCollection<ExerciceModel> favouriteItems = new ObservableCollection<ExerciceModel>();
    public ObservableCollection<ExerciceModel> customItems = new ObservableCollection<ExerciceModel>();

    AddUserExerciseModel newAddUserExercise;
    public List<long> exercisesIds = new List<long>();
    public ObservableCollection<SelectableExerciceModel> selectedItems = new ObservableCollection<SelectableExerciceModel>();
    private long? _exerciseId;
    private bool partIsenabled = false;

    public ChooseYourCustomExercisePage(List<long> exercisesIdList = null)
    {
        InitializeComponent();
        BindingContext = this;

        if (exercisesIdList != null)
            exercisesIds = exercisesIdList;
        ExpandableList.ItemTapped += ExerciseListView_ItemTapped;
        RefreshLocalized();
        if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
        //Adding Messanger to subscribe localize updation
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void StateImage_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName.Equals("Source"))
        {
            var image = sender as Image;
            image.Opacity = 0;
            image.FadeTo(1, 1000);
        }
    }

    private void RefreshLocalized()
    {
        Title = AppResources.ChooseYourExercise;
        BtnCancel.Text = AppResources.Cancel;
        SearchEntry.Placeholder = AppResources.SearchExercises;
    }
    bool IsEntry = false;
    public async void OnBeforeShow()
    {
        if (IsEntry)
            return;
        IsEntry = true;
        if (LocalDBManager.Instance.GetDBSetting("ExerciseTypeList") == null)
            LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
        DependencyService.Get<IFirebase>().SetScreenName("choose_your_custom_exercise_page");
        ExeList.Clear();
        exerciseItems.Clear();
        exerciseItemsResult.Clear();
        customItems.Clear();
        CurrentLog.Instance.IsAddedNewExercise = false;
        CurrentLog.Instance.IsFavouriteUpdated = false;

        //try
        //{
        //GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
        //exercises = itemsSource.Exercises;
        //await UpdateExerciseList();
        try
        {

            CurrentLog.Instance.IsBodyPartUpdated = false;

            string jsonFileName = "Exercises.json";
            ExerciceModel exerciseList = new ExerciceModel();
            var assembly = typeof(KenkoChooseYourWorkoutExercisePage).GetType().Assembly;
            var stream = await FileSystem.OpenAppPackageFileAsync(jsonFileName);
            using (var reader = new System.IO.StreamReader(stream))
            {
                var jsonString = reader.ReadToEnd();

                //Converting JSON Array Objects into generic list    
                var list = JsonConvert.DeserializeObject<List<DBExerciseModel>>(jsonString);
                exercises = new List<ExerciceModel>();
                foreach (var item in list)
                {
                    if (exercisesIds.Contains(item.Id))
                        continue;
                    exercises.Add(new ExerciceModel()
                    {
                        Id = item.Id,
                        Label = item.Label,
                        BodyPartId = item.BodyPartId,
                        EquipmentId = item.EquipmentId,
                        IsBodyweight = item.IsBodyweight,
                        IsEasy = item.IsEasy,
                        IsMedium = item.IsMedium,
                        IsPlate = item.EquipmentId == 3,
                        IsSystemExercise = true,
                        VideoUrl = item.VideoUrl,
                        IsTimeBased = item.IsTimeBased,
                        IsUnilateral = item.IsUnilateral,
                        IsFlexibility = item.IsFlexibility,
                        IsAssisted = item.IsAssisted
                    });
                }
                
                exercises = exercises.OrderBy(x => x.Label).ToList();
            }

            //Remove exercises which is already inside workout

            await UpdateExerciseList();
            //exercises = itemsSource.Exercises;

            try
            {
                GetUserExerciseResponseModel itemsSource = await DrMuscleRestClient.Instance.GetCustomExerciseForUser(LocalDBManager.Instance.GetDBSetting("email").Value);
                

                if (itemsSource != null && itemsSource.Exercises != null)
                {
                    customItems.Clear();

                    foreach (var item in itemsSource.Exercises)
                    {
                        customItems.Add(item);
                        var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
                            bodyPartShoulders.Exercices.Add(item);
                            BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                            ExeList.Insert(0, section);
                            ExpandableList.ItemsSource = ExeList;
                        }
                    }
                }

                GetUserExerciseResponseModel itemsSources = await DrMuscleRestClient.Instance.GetFavoriteExercises();
                if (itemsSources != null && itemsSources.Exercises != null)
                {
                    favouriteItems.Clear();

                    foreach (var item in itemsSources.Exercises)
                    {
                        favouriteItems.Add(item);
                        var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                            bodyPartShoulders.Exercices.Add(item);
                            BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                            ExeList.Insert(0, section);
                            ExpandableList.ItemsSource = ExeList;
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }


        }
        catch (Exception e)
        {
            ConnectionErrorPopup();

        }
        finally
        {
            IsEntry = false;
        }
        //}
        //catch (Exception e)
        //{

    }

    protected async override void OnAppearing()
    {
        base.OnAppearing();
        try
        {

            if (CurrentLog.Instance.IsBodyPartUpdated)
            {
                CurrentLog.Instance.IsBodyPartUpdated = false;
                await UpdateExerciseList();
            }
            if (CurrentLog.Instance.IsFavouriteUpdated)
            {
                OnBeforeShow();
            }
            if (CurrentLog.Instance.IsAddedNewExercise)
            {
                CurrentLog.Instance.IsAddedNewExercise = false;
                OnBeforeShow();
            }
            if (CurrentLog.Instance.IsRecoveredWorkout)
            {
                CurrentLog.Instance.IsRecoveredWorkout = false;
                // AlertConfig ShowExplainDeloadPopUp = new AlertConfig()
                // {
                //     Title = "Suggested exercises",
                //     Message = "The app recommends exercises for the same muscle group. Tap any to swap or scroll down for more exercises.",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Got it",

                // };
                // await UserDialogs.Instance.AlertAsync(ShowExplainDeloadPopUp);

                await HelperClass.DisplayCustomPopupForResult("Suggested exercises",
                            "The app recommends exercises for the same muscle group. Tap any to swap or scroll down for more exercises.","Got it","");
                //CurrentLog.Instance.IsWalkthrough = true;
                if (Navigation?.NavigationStack?.Count > 1)
                {
                    try
                    {
                        await Navigation.PopAsync();
                    }
                    catch (NotSupportedException ex)
                    {
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }

        }
        catch (Exception ex)
        {

        }
    }

    void OnCancelClicked(object sender, System.EventArgs e)
    {
        //StackLayout s = ((StackLayout)((Button)sender).Parent);
        //s.Children[0].IsVisible = false;
        //s.Children[1].IsVisible = false;
        //s.Children[2].IsVisible = false;
        //s.Children[3].IsVisible = false;
        //s.Children[4].IsVisible = false;
        //s.Children[5].IsVisible = true;
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        try
        {
            StackLayout s = ((StackLayout)((Button)sender).Parent);
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            if (!m.IsSystemExercise)
            {
                //s.Children[0].IsVisible = false;
                //s.Children[1].IsVisible = true;
                //s.Children[2].IsVisible = false;
                //s.Children[3].IsVisible = true;
                //s.Children[4].IsVisible = false;
                //s.Children[5].IsVisible = false;
            }
            else
            {
                //s.Children[0].IsVisible = true;
                //s.Children[1].IsVisible = false;
                //if (!string.IsNullOrEmpty(m.VideoUrl))
                //s.Children[2].IsVisible = true;
                //s.Children[3].IsVisible = true;
                //s.Children[4].IsVisible = false;
                //s.Children[5].IsVisible = false;
            }
        }
        catch (Exception ex)
        {

        }
    }

    public async void OnVideo(object sender, EventArgs e)
    {
        try
        {
            CurrentLog.Instance.VideoExercise = ((ExerciceModel)((Button)sender).CommandParameter);
            if (Device.RuntimePlatform.Equals(Device.iOS))
                DependencyService.Get<IOrientationService>().Landscape();
            await Navigation.PushAsync(new ExerciseVideoPage());
            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }

    private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs ex)
    {
        try
        {


            if (((ExerciceModel)ex.Item).Id != -1)
            {
                if (CurrentLog.Instance.IsAddingExerciseFromWorkout)
                {
                    ExerciceModel model = ((ExerciceModel)ex.Item);
                    try
                    {
                        selectedItems.Clear();

                        var exercise = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.GroupBy(x => x.Id).Select(x => x.First()).ToList(); ;
                        foreach (var e in exercise)
                        {
                            var exe = e;
                            bool isSwapped = ((App)Application.Current).SwapExericesContexts.Swaps.Any(c => c.SourceExerciseId == exe.Id);
                            if (isSwapped)
                            {
                                long targetExerciseId = (long)((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.SourceExerciseId == exe.Id).TargetExerciseId;
                                var swappedExe = GetSwappedExercise(targetExerciseId);
                                if (swappedExe != null)
                                {
                                    exe = swappedExe;
                                }
                                else
                                    isSwapped = false;
                            }

                            if (exercises.Any(x => x.Id == exe.Id))
                                exercises.Remove(exercises.Where(x => x.Id == exe.Id).First());
                            SelectableExerciceModel em = new SelectableExerciceModel();
                            em.Id = exe.Id;
                            em.IsSystemExercise = exe.IsSystemExercise;
                            em.Label = exe.Label;
                            em.BodyPartId = exe.BodyPartId;
                            em.VideoUrl = exe.VideoUrl;
                            em.IsUnilateral = exe.IsUnilateral;
                            em.IsTimeBased = exe.IsTimeBased;
                            em.IsBodyweight = exe.IsBodyweight;
                            em.IsPlate = exe.IsPlate;
                            if (CurrentLog.Instance.ShowEditWorkouts)
                            {
                                _exerciseId = exe.Id;
                                CurrentLog.Instance.ShowEditWorkouts = false;
                            }
                            em.IsSelected = CurrentLog.Instance.CurrentWorkoutTemplate.Id == -1 ? false : isSwapped ? true : CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Select(exer => exer.Id).Contains(exe.Id);

                            if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise)
                            {
                                if (em.Id == 16508)
                                {
                                    var data = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutCardioDetails")?.Value;
                                    if (data != null)
                                    {
                                        var CurrentWorkoutId = LocalDBManager.Instance.GetDBSetting("CurrentWorkoutId")?.Value;
                                        var currentWorkoutCardioDetails = JsonConvert.DeserializeObject(data);
                                        var jObject = currentWorkoutCardioDetails as JObject;
                                        if (jObject["Id"].ToString() == CurrentWorkoutId && jObject["IsCardio"].ToString() == "false")
                                        {
                                            em.IsSelected = false;
                                        }
                                        else
                                        {
                                            var isGlobalCardio = LocalDBManager.Instance.GetDBSetting("Cardio")?.Value;
                                            if (isGlobalCardio == "true")
                                                em.IsSelected = true;
                                            else
                                                em.IsSelected = false;
                                        }
                                    }
                                    else
                                    {
                                        em.IsSelected = ((LocalDBManager.Instance.GetDBSetting("Cardio")?.Value) == "true") ? true : false;
                                    }
                                }
                                if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value))
                                {
                                    if (exe.IsBodypartPriority)
                                        switch (em.Id)
                                        {
                                            case 15778:
                                            case 18846:
                                                //ZottmanCurlExId
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Biceps")
                                                {

                                                    partIsenabled = true;

                                                    continue;
                                                }
                                                break;
                                            case 12982:
                                            case 18847:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Chest")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 20826:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Chest")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 17229:
                                            case 18845:
                                            case 18849:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Abs")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 12989:
                                            case 18848:
                                            case 18851:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Legs")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Glutes")
                                                {

                                                    partIsenabled = true;
                                                    continue; ;
                                                }
                                                break;
                                            case 17482:
                                            case 18850:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Biceps")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                                ;
                                            case 15934:
                                            case 15935:



                                                if (LocalDBManager.Instance.GetDBSetting("Equipment")?.Value == "true" && LocalDBManager.Instance.GetDBSetting("Plate")?.Value == "false")
                                                    switch (CurrentLog.Instance.CurrentWorkoutTemplate.Id)
                                                    {
                                                        case 110:
                                                        case 426:
                                                        case 874:
                                                        case 877:
                                                        case 883:
                                                        case 2317:
                                                        case 2322:
                                                        case 2326:
                                                        case 2330:
                                                        case 2334:
                                                        case 2338:
                                                        case 2354:
                                                        case 2358:
                                                        case 2362:
                                                        case 14157:
                                                        case 14340:
                                                        case 14358:
                                                            break;
                                                        default:
                                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Legs")
                                                            {

                                                                partIsenabled = true;
                                                                continue;
                                                            }
                                                            if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Glutes")
                                                            {

                                                                partIsenabled = true;
                                                                continue;
                                                            }

                                                            break;
                                                    }
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Legs")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Glutes")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }

                                                break;

                                            case 9661:
                                            case 25554:
                                            case 27419:
                                            case 17225:
                                            case 27420:
                                            case 27421:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Triceps")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 15913:
                                            case 27422:
                                            case 27423:
                                            case 15911:
                                            case 27424:
                                            case 27425:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Shoulders")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 15769:
                                            case 27428:
                                            case 27429:
                                            case 15768:
                                            case 27426:
                                            case 27427:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Traps")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 17224:
                                            case 27430:
                                            case 27431:
                                            case 17360:
                                            case 27432:
                                            case 27433:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Upper back")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            case 15938:
                                            case 15939:
                                            case 15940:
                                            case 14235:
                                            case 27435:
                                            case 27434:
                                                if (LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value == "Calves")
                                                {

                                                    partIsenabled = true;
                                                    continue;
                                                }
                                                break;
                                            default:
                                                break;
                                        }
                                }
                            }
                            if (selectedItems.Where(x => x.Id == em.Id).FirstOrDefault() == null)
                                selectedItems.Add(em);
                        }
                    }
                    catch (Exception exx)
                    {

                    }
                    if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Where(x => x.Id == model.Id).FirstOrDefault() == null)
                    {
                        var json = JsonConvert.SerializeObject(CurrentLog.Instance.CurrentWorkoutTemplate);
                        var wkModel = JsonConvert.DeserializeObject<WorkoutTemplateModel>(json);

                        if (_exerciseId != null && partIsenabled)
                        {
                            var bodyPartExer = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.FirstOrDefault(x => x.Id == _exerciseId);
                            if (bodyPartExer != null)
                            {

                            }

                        }
                        selectedItems.Add(new SelectableExerciceModel() { Id = model.Id, Label = model.Label, IsSelected = true });
                        if (CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.LastOrDefault()?.Id == 16508)
                        {

                            if (selectedItems.FirstOrDefault(x => x.Id == 16508) != null)
                                selectedItems.Remove(selectedItems.FirstOrDefault(x => x.Id == 16508));

                            var lastCardio = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Last();
                            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Remove(lastCardio);
                            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(model);
                            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(lastCardio);
                            wkModel.Exercises.Remove(wkModel.Exercises.Last());
                            wkModel.Exercises.Add(model);

                        }
                        else
                        {
                            CurrentLog.Instance.CurrentWorkoutTemplate.Exercises.Add(model);
                            wkModel.Exercises = CurrentLog.Instance.CurrentWorkoutTemplate.Exercises;
                        }
                        var list = new List<ExerciceModel>();
                        foreach (var selectedex in selectedItems.Where(x => x.IsSelected).ToList())
                        {
                            list.Add(selectedex);
                        }
                        wkModel.Exercises = list;
                        //Send message to refresh page
                        Utility.HelperClass.PopToPage<KenkoChooseYourWorkoutExercisePage>(this.Navigation);
                        CurrentLog.Instance.IsAddingExerciseFromWorkout = false;
                        string serializedModel = JsonConvert.SerializeObject(CurrentLog.Instance.CurrentWorkoutTemplate);

                        if (CurrentLog.Instance.CurrentWorkoutTemplate.UserId == "89c52f09-240c-40a8-96df-9e8e152b7d63" || CurrentLog.Instance.CurrentWorkoutTemplateGroup != null && CurrentLog.Instance.CurrentWorkoutTemplateGroup.IsFeaturedProgram && CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Any(x => x.Id == CurrentLog.Instance.CurrentWorkoutTemplate.Id))
                        {

                            BooleanModel result = await DrMuscleRestClient.Instance.CreateNewUserWorkoutTemplateWithoutLoader(wkModel);
                        }
                        else
                        {
                            if (CurrentLog.Instance.CurrentWorkoutTemplate.IsSystemExercise == false)
                            {
                                BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplateWithoutLoader(wkModel);
                            }
                            else
                            {
                                BooleanModel result = await DrMuscleRestClient.Instance.CreateNewUserWorkoutTemplateWithoutLoader(wkModel);
                            }
                        }
                    }
                }
                else if (CurrentLog.Instance.SwapContext != null)
                {
                    var swapContext = CurrentLog.Instance.SwapContext;
                    swapContext.TargetExerciseId = ((ExerciceModel)ex.Item).Id;
                    ExerciceModel model = ((ExerciceModel)ex.Item);
                    swapContext.Label = model.Label;
                    swapContext.IsBodyweight = model.IsBodyweight;
                    swapContext.IsSystemExercise = model.IsSystemExercise;
                    swapContext.IsEasy = model.IsEasy;
                    swapContext.BodyPartId = model.BodyPartId;
                    swapContext.VideoUrl = model.VideoUrl;
                    swapContext.IsTimeBased = model.IsTimeBased;
                    swapContext.IsPlate = model.IsPlate;
                    swapContext.IsUnilateral = model.IsUnilateral;
                    swapContext.IsMobility = model.IsFlexibility;
                    swapContext.IsWeighted = model.IsWeighted;
                    swapContext.IsOneHanded = model.IsOneHanded;
                    swapContext.IsAssisted = model.IsAssisted;
                    ((App)Application.Current).SwapExericesContexts.Swaps.Add(swapContext);
                    ((App)Application.Current).SwapExericesContexts.SaveContexts();
                    await DrMuscleRestClient.Instance.SetSwappedJson(new UserInfosModel()
                    {
                        SwappedJson = ((App)Application.Current).SwapExericesContexts.ToJson()
                    });

                    if (!Config.IsSwappedFirstTime && ((App)Application.Current).SwapExericesContexts.Swaps.Count == 1)
                    {
                        Config.IsSwappedFirstTime = true;
                        var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                        var modalPage = new Views.RGGeneralPopup("swappedsuccess.png", "Awesome!", "First exercise swapped", "View exercises");
                        
                        if(modalPage != null)
                        {
                            modalPage.Closed += (sender2, e2) =>
                            {
                                waitHandle.Set();
                            };
                            await Application.Current.MainPage.ShowPopupAsync(modalPage);
                            await Task.Run(() => waitHandle.WaitOne());
                        }
                        
                    }
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        Utility.HelperClass.PopToPage<KenkoChooseYourWorkoutExercisePage>(this.Navigation);
                        CurrentLog.Instance.SwapContext = null;
                    });
                    return;
                }
                //CurrentLog.Instance.IsFromExercise = true;
                //await RunExercise((ExerciceModel)e.Item);
            }
            
        }
        catch (NullReferenceException exc)
        {

        }
        catch (Exception excep)
        {

        }
    }

    private ExerciceModel GetSwappedExercise(long id)
    {
        try
        {

            SwapExerciseContext context = ((App)Application.Current).SwapExericesContexts.Swaps.First(c => c.TargetExerciseId == id);
            if (!string.IsNullOrEmpty(context.Label))
            {
                ExerciceModel model = new ExerciceModel()
                {
                    Id = (long)context.TargetExerciseId,
                    Label = context.Label,
                    IsBodyweight = context.IsBodyweight,
                    IsSwapTarget = true,
                    IsSystemExercise = context.IsSystemExercise,
                    VideoUrl = context.VideoUrl,
                    IsEasy = context.IsEasy,
                    BodyPartId = context.BodyPartId,
                    IsUnilateral = context.IsUnilateral,
                    IsTimeBased = context.IsTimeBased,
                    IsPlate = context.IsPlate,
                    IsFlexibility = context.IsFlexibility,
                    IsWeighted = context.IsWeighted
                };
                model.IsSwapTarget = true;

                return model;
            }

        }
        catch (Exception ex)
        {

        }
        return null;
    }




    public async void ResetExercisesAction(ExerciceModel model)
    {
        BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(model);

        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + model.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());

    }

    public async void OnReset(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            ExerciceModel m = (ExerciceModel)mi.CommandParameter;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise = m;
            if (m.IsSystemExercise)
                await Navigation.PushAsync(new ExerciseSettingsPage());
            else
                await Navigation.PushAsync(new ExerciseCustomSettingsPage());
            OnCancelClicked(sender, e);
        }
        catch (Exception ex)
        {

        }
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        ExerciceModel m = (ExerciceModel)((BindableObject)sender).BindingContext;

        var btnVideo = (DrMuscleButton)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[0];
        if (string.IsNullOrEmpty(m.VideoUrl))
            btnVideo.IsVisible = false;
        else
            btnVideo.IsVisible = true;
        if (m.IsSystemExercise)
        {

        }
    }

    void Section_Tapped(object sender, System.EventArgs e)
    {
        var obj = (BodyPartSection)((StackLayout)sender).BindingContext;
        obj.Expanded = !obj.Expanded;
        ExpandableList.ItemsSource = null;
        ExpandableList.ItemsSource = ExeList;
    }
    
    private async Task UpdateExerciseList()
    {
        if (exercises == null)
            return;
        exerciseItems.Clear();
        exerciseItemsResult.Clear();


        ExeList.Clear();
        List<ExerciceModel> exo;
        exo = exercises.ToList();


        if (CurrentLog.Instance.SwapContext != null)
        {
            var swapContext = CurrentLog.Instance.SwapContext;
            if (swapContext.SourceBodyPartId != null)
            {
                var bodyPart = exo.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
                var notBodyPart = exo.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();
                var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };

                foreach (var item in bodyPart)
                {
                    bodyPartShoulders.Exercices.Add(item);
                }
                BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                ExeList.Add(section);

                var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
                foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
                {
                    otherBodyPartShoulders.Exercices.Add(item);
                }
                BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders, false);
                ExeList.Add(otherSection);


                ExpandableList.ItemsSource = ExeList;
            }
            else
            {


            }
            System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
        }
        else
        {

        }

        if (ExeList?.Count == 0)
        {

            var customExerAdded = false;
            foreach (var groupItem in exo.GroupBy(x => x.BodyPartId).ToList())
            {
                var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key) };
                foreach (var item in groupItem)
                {
                    bodyPartShoulders.Exercices.Add(item);
                }
                
                BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
                ExeList.Add(section);
            }
            
            ExpandableList.ItemsSource = ExeList;
        }
        foreach (var item in exo)
        {
            exerciseItems.Add(item);
        }
        var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
        if (bodyPartSection.Count > 0)
        {

            BodyPartSection body = bodyPartSection[0];
            body.Clear();
            if (body.Expanded)
                body.AddRange(customItems);
            body._bodyPart.Exercices.Clear();
            body._bodyPart.Exercices.AddRange(customItems);
            body.Exercises.Clear();
            body.Exercises.AddRange(customItems);
        }
        else
        {
            var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(26), Id = 26 };
            bodyPartShoulders.Exercices.AddRange(customItems);
            BodyPartSection section = new BodyPartSection(bodyPartShoulders, false);
            ExeList.Insert(0, section);
            //ExpandableList.ItemsSource = ExeList;
            ExpandableList.ItemsSource = null;
            ExpandableList.ItemsSource = ExeList;
        }

        exerciseItemsResult = exerciseItems;

    }

    void Handle_SearchTextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            var list = exerciseItems?.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            var favList = favouriteItems?.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            var customList = customItems?.Where(x => x.Label.ToLowerInvariant().Contains(e.NewTextValue.ToLowerInvariant())).ToList();
            ExeList.Clear();

            if (CurrentLog.Instance.SwapContext != null)
            {
                var swapContext = CurrentLog.Instance.SwapContext;
                if (swapContext.SourceBodyPartId != null)
                {
                    var bodyPart = list.Where(x => x.BodyPartId == swapContext.SourceBodyPartId).ToList();
                    var notBodyPart = list.Where(x => x.BodyPartId != swapContext.SourceBodyPartId).ToList();

                    var bodyPartShoulders = new BodyPartModel() { Label = $"{AppThemeConstants.GetBodyPartName(swapContext.SourceBodyPartId)} exercises" };
                    foreach (var item in bodyPart)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                    ExeList.Add(section);

                    var otherBodyPartShoulders = new BodyPartModel() { Label = $"Other exercises" };
                    foreach (var item in notBodyPart.OrderBy(x => x.BodyPartId))
                    {
                        otherBodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection otherSection = new BodyPartSection(otherBodyPartShoulders);
                    ExeList.Add(otherSection);
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        ExpandableList.ItemsSource = ExeList;
                    });

                    foreach (var item in customList)
                    {
                        var bodyPartSection = ExeList.Where(x => x.Id == 26).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 26 };
                            bodyPartFavourite.Exercices.Add(item);
                            BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                            ExeList.Insert(0, sections);
                            Device.BeginInvokeOnMainThread(() =>
                            {
                                ExpandableList.ItemsSource = ExeList;
                            });
                        }
                    }

                    foreach (var item in favList)
                    {
                        var bodyPartSection = ExeList.Where(x => x.Id == 25).ToList();
                        if (bodyPartSection.Count > 0)
                        {
                            BodyPartSection body = bodyPartSection[0];
                            if (body.Expanded)
                                body.Add(item);
                            body._bodyPart.Exercices.Add(item);
                            body.Exercises.Add(item);
                        }
                        else
                        {
                            var bodyPartFavourite = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(25), Id = 25 };
                            bodyPartFavourite.Exercices.Add(item);
                            BodyPartSection sections = new BodyPartSection(bodyPartFavourite);
                            ExeList.Insert(0, sections);
                            Device.BeginInvokeOnMainThread(() =>
                            {
                                ExpandableList.ItemsSource = ExeList;
                            });
                        }
                    }
                }
                else
                {

                }
                System.Diagnostics.Debug.WriteLine($"SourceBodyPartId{swapContext.SourceBodyPartId}");
            }

            if (ExeList.Count == 0)
            {
                //foreach (ExerciceModel em in exo)
                //exerciseItems.Add(em);

                foreach (var groupItem in list.GroupBy(x => x.BodyPartId).ToList())
                {
                    var bodyPartShoulders = new BodyPartModel() { Label = AppThemeConstants.GetBodyPartName(groupItem.Key) };
                    foreach (var item in groupItem)
                    {
                        bodyPartShoulders.Exercices.Add(item);
                    }
                    BodyPartSection section = new BodyPartSection(bodyPartShoulders);
                    ExeList.Add(section);
                }
                Device.BeginInvokeOnMainThread(() =>
                {
                    ExpandableList.ItemsSource = ExeList;
                });
            }
            if (e.NewTextValue.Length > 0)
                BtnCancel.IsVisible = true;
            else
                BtnCancel.IsVisible = false;
        }
        catch (Exception ex)
        {

        }
    }

    void Handle_CancelTapped(object sender, System.EventArgs e)
    {
        SearchEntry.Text = "";
    }

    async Task ConnectionErrorPopup()
    {

        try
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError,
            //     OkText = "Try again"
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");
        }
        catch (Exception ex)
        {

        }
    }

    void ExerciseListView_Scrolled(System.Object sender, Microsoft.Maui.Controls.ScrolledEventArgs e)
    {
        if (!SearchEntry.IsFocused && (PopupNavigation.Instance.PopupStack?.Count()) == 0)
        {
            SearchEntry.HideSoftInputAsync(CancellationToken.None);
        }
    }
    void SearchEntry_Focused(object sender, FocusEventArgs e)
    {

        SearchEntry.Focus();
    }

    void SearchEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SearchEntry.HideSoftInputAsync(CancellationToken.None);
    }

    private void NewExerciseTapped(object sender, EventArgs e)
    {
        AddMyOwnExercise();
    }

    private async void AddMyOwnExercise()
    {
        try
        {
            CustomPromptConfig customPromptConfig = new CustomPromptConfig(AppResources.NewExercise, AppResources.TapHereToEnterName, AppResources.Create,
             AppResources.Cancel, AppResources.LetsNameYourNewExercise, Keyboard.Text, "");

            customPromptConfig.ActionSelected += async (sender1, action) =>
            {
                if (action == PopupAction.OK)
                {
                    PromptResult result = new PromptResult(true, customPromptConfig.text);
                    if (string.IsNullOrWhiteSpace(customPromptConfig.text))
                    {
                        return;
                    }
                    await Task.Delay(500);
                    AddExercisesAction(action, customPromptConfig.text);
                }
            };
            await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
        }
        catch (Exception ex)
        {

        }
        // PromptConfig p = new PromptConfig()
        // {
        //     InputType = InputType.Name,
        //     IsCancellable = true,
        //     Title = AppResources.NewExercise,
        //     Message = AppResources.LetsNameYourNewExercise,
        //     Placeholder = AppResources.TapHereToEnterName,
        //     OkText = AppResources.Create,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = new Action<PromptResult>(AddExercisesAction)
        // };
        // p.OnTextChanged += Name_OnTextChanged;
        // UserDialogs.Instance.Prompt(p);
    }
    private async void AddExercisesAction(PopupAction response, string Text)
    {
        if (response == PopupAction.OK)
        {
            try
            {
                ActionSheetConfig config = new ActionSheetConfig()
                {
                    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                };

                config.Add("Normal", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Cardio", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = false,
                        IsTimeBased = true,
                        BodyPartId = 12
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Weighted Bodyweight", () =>
                {
                    var userExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = false,
                        IsWeighted = true
                    };
                    AskForIsEasy(userExercise);
                });
                config.Add("Flexibility or mobility", () =>
                {
                    newAddUserExercise = new AddUserExerciseModel()
                    {
                        Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                        ExerciseName = Text,
                        IsBodyweight = true,
                        IsFlexibility = true,
                        IsEasy = false,
                        IsMedium = false,
                        IsTimeBased = false
                    };
                    CreateNewExercise();
                });
                config.SetTitle("Exercise type?");
                UserDialogs.Instance.ActionSheet(config);
                //ConfirmConfig IsEasyPopUp = new ConfirmConfig()
                //{
                //    Title = AppResources.IsThisABodyweightExercise,
                //    //Title = $"Let's set up your {response.Text}",
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    OkText = AppResources.YesBodyweight,
                //    CancelText = AppResources.No,
                //    OnAction = async (bool ok) =>
                //    {
                //        var userExercise = new AddUserExerciseModel()
                //        {
                //            Username = LocalDBManager.Instance.GetDBSetting("email").Value,
                //            ExerciseName = response.Text,
                //            IsBodyweight = ok
                //        };
                //        AskForIsEasy(userExercise);
                //    }
                //};
                //await Task.Delay(100);
                //UserDialogs.Instance.Confirm(IsEasyPopUp);

            }
            catch (Exception e)
            {
                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseCheckInternetConnection,
                //     Title = AppResources.ConnectionError
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection, "Try again", "");
                //await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
            }
        }
    }
    async void AskForIsEasy(AddUserExerciseModel newUserExercise)
    {
        newAddUserExercise = newUserExercise;
        CreatePopupForExerciseType();
    }
    async void CreatePopupForExerciseType()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Hard", () =>
            {
                CreateNewExercise();
            });

            actionSheetConfig.Add("Medium", () =>
            {
                newAddUserExercise.IsMedium = true;
                CreateNewExercise();
            });
            actionSheetConfig.Add("Easy", () =>
            {
                newAddUserExercise.IsEasy = true;
                CreateNewExercise();
            });
            actionSheetConfig.SetTitle("How hard should the exercise be?");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);

        }
        catch (Exception ex)
        {

        }
    }
    async void CreateNewExercise()
    {
        try
        {
            if (newAddUserExercise.BodyPartId == 12)
            {
                CreateExercise();
                return;
            }
            ActionSheetConfig actionSheetConfig = new ActionSheetConfig();
            actionSheetConfig.AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray);

            actionSheetConfig.Add("Abs", () =>
            {
                newAddUserExercise.BodyPartId = 7;
                CreateExercise();
            });

            actionSheetConfig.Add("Back", () =>
            {
                newAddUserExercise.BodyPartId = 4;
                CreateExercise();
            });
            actionSheetConfig.Add("Lower back, glutes & hamstrings", () =>
            {
                newAddUserExercise.BodyPartId = 14;
                CreateExercise();
            });
            actionSheetConfig.Add("Biceps", () =>
            {
                newAddUserExercise.BodyPartId = 5;
                CreateExercise();
            });
            actionSheetConfig.Add("Calves", () =>
            {
                newAddUserExercise.BodyPartId = 9;
                CreateExercise();
            });
            //actionSheetConfig.Add("Cardio", () =>
            //{
            //    newAddUserExercise.BodyPartId = 12;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Chest", () =>
            {
                newAddUserExercise.BodyPartId = 3;
                CreateExercise();
            });
            //actionSheetConfig.Add("Flexibility & Mobility", () =>
            //{
            //    newAddUserExercise.BodyPartId = 13;
            //    CreateExercise();
            //});
            actionSheetConfig.Add("Forearm", () =>
            {
                newAddUserExercise.BodyPartId = 11;
                CreateExercise();
            });
            actionSheetConfig.Add("Legs", () =>
            {
                newAddUserExercise.BodyPartId = 8;
                CreateExercise();
            });
            actionSheetConfig.Add("Neck", () =>
            {
                newAddUserExercise.BodyPartId = 10;
                CreateExercise();
            });
            actionSheetConfig.Add("Shoulders", () =>
            {
                newAddUserExercise.BodyPartId = 2;
                CreateExercise();
            });

            actionSheetConfig.Add("Triceps", () =>
            {
                newAddUserExercise.BodyPartId = 6;
                CreateExercise();
            });

            actionSheetConfig.SetTitle("Body part");
            UserDialogs.Instance.ActionSheet(actionSheetConfig);
        }
        catch (Exception ex)
        {

        }

    }
    async void CreateExercise()
    {
        try
        {
            var ShowPopUp = await HelperClass.DisplayCustomPopup("Left/right side separately?", "Train your left and right sides separately, or both sides together?",
                 "Left/right separately", "Both sides together");
            ShowPopUp.ActionSelected += async (sender, action) =>
            {

                if (action == Views.PopupAction.OK)
                {
                    newAddUserExercise.IsUnilateral = true;
                }

                if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
                    FinalCreateExercise();
                else
                    AskForTimeBasedExercise();

            };
            //await Task.Delay(100);



            // ConfirmConfig IsEasyPopUp = new ConfirmConfig()
            // {
            //     Title = "Left/right side separately?",
            //     Message = "Train your left and right sides separately, or both sides together?",
            //     //Title = $"Let's set up your {response.Text}",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Left/right separately",
            //     CancelText = "Both sides together",
            //     OnAction = async (bool ok) =>
            //     {
            //         newAddUserExercise.IsUnilateral = ok;
            //         if (newAddUserExercise.IsFlexibility || newAddUserExercise.BodyPartId == 12)
            //             FinalCreateExercise();
            //         else
            //             AskForTimeBasedExercise();
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(IsEasyPopUp);
        }
        catch (Exception ex)
        {

        }
    }
    async void AskForTimeBasedExercise()
    {
        try
        {
            var isConfirm = await HelperClass.DisplayCustomPopupForResult("", "Should this exercise progress in reps or seconds?",
                 "Seconds", AppResources.Reps);



            // ConfirmConfig TimebasePopUp = new ConfirmConfig()
            // {
            //     Message = "Should this exercise progress in reps or seconds?",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Seconds",
            //     CancelText = AppResources.Reps,
            // };

            // var isConfirm = await UserDialogs.Instance.ConfirmAsync(TimebasePopUp);
            if (isConfirm == PopupAction.OK)
            {
                newAddUserExercise.IsTimeBased = true;
                if (!newAddUserExercise.IsWeighted)
                    newAddUserExercise.IsBodyweight = true;
            }
            else
            {
                newAddUserExercise.IsTimeBased = false;
            }
            FinalCreateExercise();
        }
        catch (Exception ex)
        {

        }
    }
    async void FinalCreateExercise()
    {
        try
        {

            ExerciceModel newExercise = await DrMuscleRestClient.Instance.CreateNewExercise(newAddUserExercise);
            CurrentLog.Instance.IsAddedNewExercise = true;
            newExercise.BodyPartId = newAddUserExercise.BodyPartId;
            //exercises.Add(newExercise);
            customItems.Add(newExercise);
            await UpdateExerciseList();
            if (CurrentLog.Instance.SwapContext == null)
            {

                CurrentLog.Instance.IsFromExercise = true;
                CurrentLog.Instance.CurrentExercise = newExercise;

                if (!Config.IsFirstExerciseCreatedPopup && customItems.Count == 1)
                {
                    Config.IsFirstExerciseCreatedPopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.GeneralPopup("FirstWorkout.png", "Success!", "First exercise created", "Open exercise");
                    //TODO: MAUI
                    //modalPage.Disappearing += (sender2, e2) =>
                    //{
                    //    waitHandle.Set();
                    //};
                    //await PopupNavigation.Instance.PushAsync(modalPage);

                    //await Task.Run(() => waitHandle.WaitOne());
                }
                //      var kenko = new KenkoSingleExercisePage();
                //       await Navigation.PushAsync(kenko);

            }
        }
        catch (Exception ex)
        {

        }
    }
}
