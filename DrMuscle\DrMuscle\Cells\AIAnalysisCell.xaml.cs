﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DrMuscle.Helpers;
using DrMuscle.Message;
using DrMuscle.Screens.Subscription;
using DrMuscle.Utility;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace DrMuscle.Cells
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class AIAnalysisCell : ViewCell
	{
		public AIAnalysisCell ()
		{
			InitializeComponent ();
		}

		private void TapGestureRecognizer_OnTapped(object sender, EventArgs e)
		{
			PagesFactory.PushAsync<SubscriptionPage>();
		}

		protected override void OnBindingContextChanged()
		{
			base.OnBindingContextChanged();
			BotModel model = this.BindingContext as BotModel;
			BtnInspiredMe.IsVisible = false;
			BtnHelpWithGoal.IsVisible = false;
            BtnShare.IsVisible = false;
			if (model != null && !string.IsNullOrEmpty(model.Part2) && model.Part2.Contains("Learn more."))
			{
                gridChatButtons.IsVisible = false;
			}
			else
			{
                //model.LevelUpText.Contains("2") means it is card of "inner champion"
                if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("2"))
				{
                    gridChatButtons.IsVisible = true;
					BtnInspiredMe.IsVisible = true;
					BtnProgressAIChat.IsVisible = true;
                    return;
				}
                //model.LevelUpText.Contains("3") means it is card of "progress report"
                else if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("3"))
				{
                    iconImage.Margin = new Thickness(0,-8,0,0);
                    iconImage.WidthRequest = 18;
                    iconImage.HeightRequest = 23;
                    BtnInspiredMe.IsVisible = false;
                    gridChatButtons.IsVisible = true;
                    BtnHelpWithGoal.IsVisible = false;
                    BtnShare.IsVisible = true;
                    BtnProgressAIChat.IsVisible = false;
					BtnMoreTips.IsVisible = true;
                    LblStrengthUp.Text = "Progress Report";
                    return;
                }
                //model.LevelUpText.Contains("4") means it is for "tips to achieve goal"
                else if (model != null && !string.IsNullOrEmpty(model.LevelUpText) && model.LevelUpText.Contains("4"))
                {
                    gridChatButtons.IsVisible = true;
                    BtnHelpWithGoal.IsVisible = true;
                    BtnProgressAIChat.IsVisible = true;
                    BtnInspiredMe.IsVisible = false;
                    BtnShare.IsVisible = false;
                    BtnMoreTips.IsVisible = false;
                    return;
                }
			}
		}

		private async void HelpWithGoal_Clicked(object sender, EventArgs args)
		{
			((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
			await Task.Delay(300);
			Xamarin.Forms.MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "HelpWithGoalChatMessage");
		}

        private async void MoreTips_Clicked(object sender, EventArgs args)
        {
            ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            Xamarin.Forms.MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "FullReportChatMessage");
        }

        private async void InspireMe_Clicked(object sender, EventArgs args)
		{
			((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
			await Task.Delay(300);
			Xamarin.Forms.MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage() { IsInspireMe = true }, "HelpWithGoalChatMessage");
		}

		private void OpenChat_Clicked(object sender, EventArgs args)
		{
			((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
		}

        async void BtnShare_Clicked(System.Object sender, System.EventArgs e)
        {
			BotModel bindingContext = this.BindingContext as BotModel;
			await HelperClass.ShareApp("Progress_Report", "Share_Progress_Report", bindingContext.Part1);
        }
    }
}
