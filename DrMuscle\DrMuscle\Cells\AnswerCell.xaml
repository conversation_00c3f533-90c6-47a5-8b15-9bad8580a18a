﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="DrMuscle.Cells.AnswerCell" xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView">

    <pancakeView:PancakeView 
        Style="{StaticResource PancakeViewStyleBlue}"
        VerticalOptions="Start" x:Name="FrmContainer"  HorizontalOptions="End" OffsetAngle="225" Margin="40,10,10,5" Padding="20,12, 20,12" CornerRadius="12" >
        <Label x:Name="LblAnswer" Text="{Binding Answer}" FontSize="Medium" TextColor="#ffffff" HorizontalOptions="End" HorizontalTextAlignment="End" Margin="4,0" />
    </pancakeView:PancakeView>

</ViewCell>
