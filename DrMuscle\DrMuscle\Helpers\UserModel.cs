﻿using System;
using System.Collections.Generic;

namespace DrMuscle.Models
{
    //public class UserModel 
    //{
    //    public UserModel()
    //    {
    //    }

    //    public Guid Id { get; set; }
    //    public string Firstname { get; set; }
    //    public string Email { get; set; }
    //    public string Password { get; set; }
    //    public string WeightUnit { get; set; }
    //    public double? LastMonthWeight { get; set; }
    //    public double? CurrentWeight { get; set; }
    //    public double? PredictedWeight { get; set; }

    //    public DateTime? LastMealPlanOrderDate { get; set; }
    //    public double? Height { get; set; }
    //    public int? Age { get; set; }
    //    public string Gender { get; set; }
      
    //    public double? TargetIntake { get; set; }
    //    public int? MealCount { get; set; }
    //    public DateTime LastActiveDate { get; set; }

    //    public virtual ICollection<MealModel> DmmMeal { get; set; }
    //    public virtual ICollection<MealPlanModel> DmmMealPlan { get; set; }
    //}
}
