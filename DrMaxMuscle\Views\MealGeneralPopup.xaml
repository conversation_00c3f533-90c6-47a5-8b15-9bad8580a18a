<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.MealGeneralPopup">

    <Frame
    Padding="0"
    CornerRadius="4"
    HasShadow="False"
    IsClippedToBounds="True"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="CenterAndExpand"
    BackgroundColor="White"
    Margin="20,20,20,0">
        <StackLayout
        Orientation="Vertical"
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="Center"
        Padding="0,15,0,10">
            <StackLayout.Resources>
                <ResourceDictionary>
                    <Style
                    TargetType="Button"
                    x:Key="ButtonStyle">
                        <Setter
                        Property="FontSize"
                        Value="Medium" />
                        <Setter
                        Property="TextColor"
                        Value="{x:Static constants:AppThemeConstants.BlueColor}" />
                        <Setter
                        Property="BorderColor"
                        Value="Transparent" />
                        <Setter
                        Property="HorizontalOptions"
                        Value="End" />
                        <Setter
                        Property="VerticalOptions"
                        Value="CenterAndExpand" />
                        <Setter
                        Property="BackgroundColor"
                        Value="Transparent" />
                    </Style>
                </ResourceDictionary>
            </StackLayout.Resources>
            <Label
            x:Name="LblTitle"
            IsVisible="false"
            Margin="15,0,10,0"
            Text="Last meal was..."
            FontAttributes="Bold"
            FontSize="Medium"
            TextColor="Black"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Start" />
            <Label
             x:Name="LblSubTitle"
            Margin="15,0,10,0"
            Text=""
            TextColor="Black"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Start" />
            <StackLayout
            Margin="7,10"
            VerticalOptions="Center"
            HorizontalOptions="FillAndExpand">
                <Editor
                Margin="5,0,5,0"
                BackgroundColor="#f4f4f4"
                x:Name="EditorMealInfo"
                TextColor="Black"
                VerticalOptions="Start"
                Text=""
                HeightRequest="80"
                Placeholder=""
                HorizontalOptions="FillAndExpand"
                 />
            </StackLayout>
            <Grid
                Margin="10,0,10,15"
                VerticalOptions="EndAndExpand"
                HorizontalOptions="EndAndExpand">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width=".5*"/>
                    <ColumnDefinition Width=".5*"/>
                </Grid.ColumnDefinitions>
                <Button
                    Grid.Column="0"
                     x:Name="BtnCancel"
                     Text="Cancel"
                     Style="{StaticResource ButtonStyle}"
                     HorizontalOptions="FillAndExpand"
                     Clicked="BtnCancel_Clicked" />
                <Button
                    Grid.Column="1"
                     x:Name="BtnSave"
                     Text=""
                     Style="{StaticResource ButtonStyle}"
                     HorizontalOptions="FillAndExpand"
                     Clicked="BtnSave_Clicked" />
            </Grid>
           
        </StackLayout>
    </Frame>
</toolkit:PopupPage>
