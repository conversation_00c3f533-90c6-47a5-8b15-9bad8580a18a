﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.WorkoutSettingsPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
                xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             Title="WorkoutSettingsPage">
    <ScrollView>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0">
        <StackLayout Margin="20,20,20,0" Spacing="0">
            <Label x:Name="LblNotes" Style="{StaticResource BoldLabelStyle}" />
            <Button IsVisible="false" />

            <controls:ExtendedEditorControl BackgroundColor="Transparent" Unfocused="NotesEntry_Unfocused" x:Name="NotesEnrty" TextColor="White" Placeholder="Enter note" PlaceholderColor="Silver" />

            <BoxView HeightRequest="1" BackgroundColor="White" />

        </StackLayout>
        <StackLayout Margin="20,20,20,0">
            <Label x:Name="LblSettings" Style="{StaticResource BoldLabelStyle}" />
        </StackLayout>

        <StackLayout Margin="20,20,20,0">
            <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                <Label x:Name="LblCustomReps" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                <Switch x:Name="CustomRepsSwitch" HorizontalOptions="End" />
            </StackLayout>

            <StackLayout x:Name="RepsStack" IsVisible="false">
                <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                    <Label x:Name="LblMin" Margin="15,0,0,0" Style="{StaticResource NormalLabelStyle}" />
                    <t:DrMuscleButton x:Name="RepsMinimumLess" Text="-" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="5,0,0,0" />
                    <Label x:Name="RepsMinimumLabel" FontSize="14" HorizontalOptions="CenterAndExpand" VerticalOptions="Center" HorizontalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                    <t:DrMuscleButton x:Name="RepsMinimumMore" Text="+" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" />
                </StackLayout>
                <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                    <Label x:Name="LblMax" VerticalOptions="Center" Margin="15,0,0,0" Style="{StaticResource LabelStyle}" />
                    <t:DrMuscleButton x:Name="RepsMaximumLess" Text="-" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="1,0,0,0" />
                    <Label x:Name="RepsMaximumLabel" FontSize="14" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource NormalLabelStyle}"></Label>
                    <t:DrMuscleButton x:Name="RepsMaximumMore" Text="+" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" />
                </StackLayout>
                <StackLayout Margin="15,10,0,0">
                    <t:DrMuscleButton x:Name="SaveCustomRepsButton" Style="{StaticResource buttonStyle}" />
                </StackLayout>
            </StackLayout>
            <StackLayout Margin="0,20,0,0" HorizontalOptions="FillAndExpand"> 
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblCustomWarmUp" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="WorksetSwitch" HorizontalOptions="End"/>
                </StackLayout>
                <StackLayout x:Name="StackWarmup" IsVisible="false" Margin="20,0,0,0">
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="LblHowManyWarmups" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                      <t:DrMuscleEntry x:Name="WorksetEntry" HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="SetEntry_TextChanged"  />
                    </StackLayout>                    
                    <t:DrMuscleButton Margin="0,10,0,0" x:Name="SaveSetButton" Text="Save work sets"  Style="{StaticResource buttonStyle}" />                
                </StackLayout>    
          </StackLayout>
            <StackLayout Margin="0,20,0,0" Spacing="3">
                <Label x:Name="LblBackOffSets" Text="Use a back-off set:" Style="{StaticResource NormalLabelStyle}" />
                <!--<Picker x:Name="BackOffPicker" Margin="0,10,0,0" Style="{StaticResource PickerStyle}" />-->
                <Frame Margin="0" HasShadow="False" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
          <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
              <Frame BorderColor="Transparent" Padding="0"
                                  Margin="0"
                  x:Name="DefaultGradient" HorizontalOptions="FillAndExpand" VerticalOptions="End" CornerRadius="0" >
                            
                        <Label Text="Default" x:Name="BtnDefault" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="White" BackgroundColor="Transparent"  HeightRequest="40" ></Label>
                  <Frame.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnDefault_Clicked" />
                  </Frame.GestureRecognizers>
              </Frame>
              <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <Frame BorderColor="Transparent" Padding="0"
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="NoGradient" VerticalOptions="End" CornerRadius="0" >
                            
              <Label Text="No" x:Name="BtnNo" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <Frame.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnNo_Clicked" />
                  </Frame.GestureRecognizers>
              </Frame>
              <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <Frame BorderColor="Transparent" Padding="0"
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="YesGradient" VerticalOptions="End"  CornerRadius="0" >
                            
              <Label Text="Yes" x:Name="BtnYes" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <Frame.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnYes_Clicked" />
                  </Frame.GestureRecognizers>
              </Frame>
              
          </StackLayout>
              </Frame>
                <Label x:Name="LblBackOffSetDesc" Text="Do more reps with less weight on your last set to build muscle faster" Style="{StaticResource NormalLabelStyle}" />
            </StackLayout>

        </StackLayout>

        <StackLayout x:Name="StackCustom" HorizontalOptions="FillAndExpand" Spacing="10" VerticalOptions="EndAndExpand" Margin="20,20,20,20">
            <Label x:Name="LblMore" Text="MORE" Style="{StaticResource BoldLabelStyle}" />
            <t:DrMuscleButton x:Name="DeleteButton" Text="Delete Workout" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}" BackgroundColor="Red" BorderColor="Red" TextColor="White" />
        </StackLayout>
    </StackLayout>
        </ScrollView>
</ContentPage>
