using DrMaxMuscle.Screens.User;
namespace DrMaxMuscle.Cells;

public partial class LinkGestureCell : ContentView
{
	public LinkGestureCell()
	{
		InitializeComponent(); FrmContainer.Opacity = 0;
        FrmContainer.Opacity = 0;
        //if (Device.RuntimePlatform.Equals(Device.iOS))
        //    LblQuestion.TextColor = Color.FromHex("#5063EE");
    }
    protected override async void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        await FrmContainer.FadeTo(1, 500, Easing.CubicInOut);
        await LblQuestion.FadeTo(1, 500);

    }

    void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        try
        {
            Navigation.PushAsync(new FAQPage());
        }
        catch(Exception ex)
        {

        }
    }
}