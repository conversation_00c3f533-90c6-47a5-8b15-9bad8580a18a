using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.User;

namespace DrMaxMuscle.Cells;

public partial class WelcomeCell : ContentView
{
    public WelcomeCell()
    {
        InitializeComponent();
        LblWelcome.Text = AppResources.ThisIsBeginningWithSupport;
        if (App.IsV1User)
            LblGroupChat.IsVisible = false;
        else
            LblGroupChat.IsVisible = false;
    }

    private async void GroupChatTapped(object sender, EventArgs args)
    {
        try
        {
            GroupChatPage page = new GroupChatPage();
            await Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<GroupChatPage>();
        }
        catch(Exception ex)
        {

        }
    }
}