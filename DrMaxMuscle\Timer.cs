﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Message;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public delegate void TimerChange(int remaining);
    public delegate void TimerDone();

    public class Timer
    {
        private static Timer _instance;
        public event TimerChange OnTimerChange;
        public event TimerDone OnTimerDone;
        public event TimerDone OnTimerStop;

        public string State = "STOPPED";
        public bool stopRequest = false;
        public int NextRepsCount = 0;
        public bool IsWorkTimer = false;

        private Timer()
        {
            Remaining = 60;
        }

        public static Timer Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new Timer();
                return _instance;
            }
        }

        public int Remaining { get; set; }

        public async Task StartTimer()
        {
            if (stopRequest)
                await Task.Run(() => { while (stopRequest) { } });
            Debug.WriteLine("StartTimer");
            Remaining = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("timer_remaining").Value);
            if (Remaining > 0)
            {
                State = "RUNNING";


                if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                {
                    PCLStartTimer();
                }
                else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                {
                    var message = new StartTimerMessage();
                    MessagingCenter.Send(message, "StartTimerMessage");
                }

            }
        }

        public void PCLStartTimer()
        {
            Device.StartTimer(new TimeSpan(0, 0, 1), () =>
            {
                if (Remaining > 0)
                {
                    Remaining--;
                    //Debug.WriteLine("Remaining : " + Remaining.ToString());
                    if (OnTimerChange != null)
                        OnTimerChange(Remaining);
                    if (Remaining == 3)
                        Timer321Done();
                    if (Remaining > 0 && !stopRequest)
                        return true;
                }
                TimerDone();
                return false;
            });
        }

        private void DecreaseRemaining()
        {
            Remaining--;
            //Debug.WriteLine("Remaining : " + Remaining.ToString());
            if (OnTimerChange != null)
                OnTimerChange(Remaining);
            if (Remaining == 9)
                TimerPlayEmptyAudio();

            if (Remaining == 4)
                Timer321Done();

            if (Remaining > 0 && !stopRequest)
                Device.StartTimer(new TimeSpan(0, 0, 1), () =>
                {
                    DecreaseRemaining();
                    return false;
                });
            else
            {
                TimerDone();
            }
        }
        public void TimerPlayEmptyAudio()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                var fileName = "emptyAudio.mp3";
                if (LocalDBManager.Instance.GetDBSetting("timer_123_sound").Value == "true")
                    DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
            }
            else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                var message = new PlayAudioFileMessage();
                message.IsEmptyAudio = true;
                if (LocalDBManager.Instance.GetDBSetting("timer_123_sound").Value == "true")
                    MessagingCenter.Send(message, "PlayAudioFileMessage");
            }
        }

        public async void Timer321Done()
        {
            try
            {
                if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                {
                    var fileName = "timer123.mp3";
                    if (LocalDBManager.Instance.GetDBSetting("timer_123_sound").Value == "true" && !Timer.Instance.IsWorkTimer)
                    {
                        DependencyService.Get<IAudio>().PlayAudioFile(fileName, true, false);
                    }

                }
                else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                {
                    var message = new PlayAudioFileMessage();
                    message.Is321 = true;
                    if (LocalDBManager.Instance.GetDBSetting("timer_123_sound").Value == "true" && !Timer.Instance.IsWorkTimer)
                        MessagingCenter.Send(message, "PlayAudioFileMessage");
                }

            }
            catch (Exception ex)
            {

            }
        }

        public async void TimerDone()
        {
            try
            {

                Debug.WriteLine("TimerDone");
                if (!stopRequest)
                {
                    if (LocalDBManager.Instance.GetDBSetting("timer_sound") == null)
                        LocalDBManager.Instance.SetDBSetting("timer_sound", "true");

                    if (LocalDBManager.Instance.GetDBSetting("timer_reps_sound") == null)
                        LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "true");
                    bool isSound = LocalDBManager.Instance.GetDBSetting("timer_reps_sound")?.Value == "true";
                    if (DeviceInfo.Current.Platform == DevicePlatform.Android)
                    {
                        if (NextRepsCount != 0)
                            await Task.Delay(700);

                        var fileName = "alarma.mp3";
                        if (LocalDBManager.Instance.GetDBSetting("timer_sound").Value == "true" || LocalDBManager.Instance.GetDBSetting("timer_reps_sound").Value == "true")
                        {

                            if (Timer.Instance.NextRepsCount <= 0 || Timer.Instance.NextRepsCount > 60)
                            {

                            }
                            else if (LocalDBManager.Instance.GetDBSetting("timer_reps_sound").Value == "true")
                            {
                                fileName = $"reps{Timer.Instance.NextRepsCount}.mp3";
                            }
                        }
                        await Task.Delay(450);
                        DependencyService.Get<IAudio>().PlayAudioFile(fileName, LocalDBManager.Instance.GetDBSetting("timer_sound").Value == "true" || isSound, LocalDBManager.Instance.GetDBSetting("timer_vibrate").Value == "true");

                    }
                    else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
                    {
                        if (NextRepsCount != 0)
                            await Task.Delay(700);
                        var message = new PlayAudioFileMessage();
                        MessagingCenter.Send(message, "PlayAudioFileMessage");
                    }

                }


            }
            catch (Exception ex)
            {

            }

            if (OnTimerDone != null && !stopRequest)
                OnTimerDone();

            stopRequest = false;
            State = "STOPPED";
        }

        public async Task StopTimer()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                await PCLStopTimer();
            }
            else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                var message = new StopTimerMessage();
                MessagingCenter.Send(message, "StopTimerMessage");
            }
            //Device.OnPlatform(
            //    Android: async () =>
            //    {
            //        await PCLStopTimer();
            //    },
            //    iOS: () =>
            //    {
            //        var message = new StopTimerMessage();
            //        MessagingCenter.Send(message, "StopTimerMessage");
            //    }
            //);
            if (OnTimerStop != null)
            {
                OnTimerStop();
            }
        }


        public async Task StopAllTimer()
        {
            if (DeviceInfo.Current.Platform == DevicePlatform.Android)
            {
                await PCLStopTimer();
            }
            else if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
            {
                var message = new StopTimerMessage();
                MessagingCenter.Send(message, "StopTimerMessageOff");
            }
            //Device.OnPlatform(
            //    Android: async () =>
            //    {
            //        await PCLStopTimer();
            //    },
            //    iOS: () =>
            //    {
            //        var message = new StopTimerMessage();
            //        MessagingCenter.Send(message, "StopTimerMessageOff");
            //    }
            //);
            if (OnTimerStop != null)
            {
                OnTimerStop();
            }
        }

        public async Task PCLStopTimer()
        {
            try
            {
                stopRequest = true;
                await Task.Run(() => { while (stopRequest) { } });
            }
            catch (Exception ex)
            {

            }
        }
    }
}
