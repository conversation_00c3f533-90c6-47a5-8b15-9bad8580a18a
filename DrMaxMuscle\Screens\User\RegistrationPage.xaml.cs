﻿using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Entity;
#if IOS
using DrMaxMuscle.Plateforms.iOS.Firebase;
#endif
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.User.OnBoarding;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Controls;
using Microsoft.Maui.Networking;
using Plugin.Firebase.Auth;
using Plugin.Firebase.Auth.Google;
using Plugin.Firebase.Core.Exceptions;
using RGPopup.Maui.Services;
using System;
using System.Globalization;
#if IOS
using GoogleUser = DrMaxMuscle.Screens.User;
// #elif ANDROID
// using Plugin.GoogleClient.MAUI;
// using GoogleUser = Plugin.GoogleClient.MAUI.GoogleUser;
#endif

namespace DrMaxMuscle.Screens.User;

public class GoogleUser
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Picture { get; set; }
    }
public partial class RegistrationPage : ContentPage
{
    private bool isPasswordVisible = false;
    private bool isRequestInProgress = false;

    // #if ANDROID
    //     private IGoogleClientManager _googleClientManager;
    // #endif
    // uncomment code please
    //IFacebookManager _manager;
    private IAppleSignInService appleSignInService;
    public RegistrationPage()
    {
        InitializeComponent();

        // Add safe initialization for entry fields
        InitializeEntryFields();

        ClearFormValues();
        SetUIAccordingToScreenSizes();
        // #if ANDROID
        // _googleClientManager = CrossGoogleClient.Current;
        // #endif
        appleSignInService = DependencyService.Get<IAppleSignInService>();
    }

    private void InitializeEntryFields()
    {
        try
        {
            // Add safety wrapper around text changed events
            EmailEntry.TextChanged += EmailTextChanged ;
            PasswordEntry.TextChanged += PasswordTextChanged;

            // Set initial state
            EmailValidator.IsVisible = false;
            PasswordValidator.IsVisible = false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing entry fields: {ex.Message}");
        }
    }

    private void SetUIAccordingToScreenSizes()
    {
        if (App.ScreenWidth > 375)
        {
            LblHeader1.FontSize = 22;
            LblHeader2.FontSize = 19;
            LblHeader3.FontSize = 19;
            LblOr.FontSize = 16;
        }
    }
    private void ClearFormValues()
    {
        EmailEntry.Text = "";
        PasswordEntry.Text = "";
        EmailValidator.IsVisible = false;
        PasswordValidator.IsVisible = false;
    }
    protected override void OnAppearing()
    {
        base.OnAppearing();
        ClearFormValues();
        CancelNotification();
    }
    private void CancelNotification()
    {
        // uncomment code please
        //DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);
    }
    public void OnBeforeShow()
    {
    }
    private async void Login_btn_clicked(object sender, EventArgs e)
    {
        try
        {
            ((App)Application.Current).displayCreateNewAccount = true;
            WelcomePage page = new WelcomePage();
            page.OnBeforeShow();
            await Navigation.PushAsync(page);
        }
        catch (Exception ex)
        {

        }
    }

    private void TermsClicked(object sender, EventArgs e)
    {
        Browser.OpenAsync("https://dr-muscle.com/terms/", BrowserLaunchMode.SystemPreferred);
    }

    private void PrivacyClicked(object sender, EventArgs e)
    {
        Browser.OpenAsync("https://dr-muscle.com/privacy/", BrowserLaunchMode.SystemPreferred);
    }

    private async void CreateAccountByEmail(object sender, EventArgs e)
    {
        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
                return;
            }
            if (await DataValidation())
            {
                App.IsNewUser = true;
                LocalDBManager.Instance.SetDBSetting("email", EmailEntry.Text);
                LocalDBManager.Instance.SetDBSetting("LoginType", "Email");
                LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
                LocalDBManager.Instance.SetDBSetting("isAccountCreatedInBackground", "false");
                App.IsIntroBack = true;
                // Navigate to the next page
                MainOnboardingPage page = new MainOnboardingPage();
                page.OnBeforeShow();
                await Navigation.PushAsync(page);


            }
        }
        catch (Exception ex)
        {

        }
    }

    private async Task<bool> DataValidation()
    {
        try
        {
            EmailValidator.IsVisible = false;
            PasswordValidator.IsVisible = false;
            if (string.IsNullOrEmpty(EmailEntry.Text) && string.IsNullOrEmpty(PasswordEntry.Text))
            {
                EmailValidator.IsVisible = true;
                EmailValidator.Text = AppResources.EnterYourEmail;
                PasswordValidator.IsVisible = true;
                PasswordValidator.Text = "Enter your password";
                await Task.Delay(1000);
                EmailValidator.IsVisible = false;
                PasswordValidator.IsVisible = false;
                return false;
            }
            else if (string.IsNullOrEmpty(EmailEntry.Text))
            {
                EmailValidator.IsVisible = true;
                EmailValidator.Text = AppResources.EnterYourEmail;
                await Task.Delay(1000);
                EmailValidator.IsVisible = false;
                return false;
            }
            else if (string.IsNullOrEmpty(PasswordEntry.Text))
            {
                PasswordValidator.IsVisible = true;
                PasswordValidator.Text = "Enter your password";
                await Task.Delay(1000);
                PasswordValidator.IsVisible = false;
                return false;
            }
            else
            {
                bool isEmailValid = CheckEmailValidity(EmailEntry.Text?.ToLowerInvariant());
                bool isPasswordValid = CheckPasswordValidity(PasswordEntry.Text);
                if (isEmailValid && isPasswordValid)
                    return true;
                else
                    return false;
            }
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    private bool CheckPasswordValidity(string text)
    {
        try
        {
            if (text.Length < 6)
            {
                PasswordValidator.IsVisible = true;
                PasswordValidator.Text = "At least 6 characters";
                return false;
            }
            else
            {
                LocalDBManager.Instance.SetDBSetting("password", text);
                return true;
            }
        }
        catch (Exception ex)
        {
            return false;
        }
    }

    private bool CheckEmailValidity(string email)
    {
        var text = email;
        if (!string.IsNullOrEmpty(email) && text.Contains("@"))
        {
            var newEmail = email.Substring(0, email.IndexOf('@'));
            if (newEmail.Length == 1)
                text = $"a{text}";
        }
        if (!Emails.ValidateEmail(text))
        {
            EmailValidator.IsVisible = true;
            EmailValidator.Text = AppResources.InvalidEmailError;
            return false;
        }
        if (email.Contains("#") || email.Contains("%") || email.Contains("{") || email.Contains("}") || email.Contains("(") || email.Contains("}") || email.Contains("$") || email.Contains("^") || email.Contains("&") || email.Contains("=") || email.Contains("`") || email.Contains("'") || email.Contains("\"") || email.Contains(",") || email.Contains("?") || email.Contains("/") || email.Contains("\\") || email.Contains("<") || email.Contains(">") || email.Contains(":") || email.Contains(";") || email.Contains("|") || email.Contains("[") || email.Contains("]") || email.Contains("*") || email.Contains("*") || email.Contains("!") || email.Contains("~") || email.Count(t => t == '@') > 1)
        {
            EmailValidator.IsVisible = true;
            EmailValidator.Text = AppResources.InvalidEmailError;
            return false;
        }
        try
        {
            var domain = email.Substring(email.IndexOf('@'));
            var extension = email.Substring(email.IndexOf('.') + 1).ToUpper();
            if (domain.Contains("gnail") || domain.Contains("gmaill") || domain.Contains(".cam"))
            {
                EmailValidator.IsVisible = true;
                EmailValidator.Text = AppResources.InvalidEmailError;
                return false;
            }
            else
            {
                EmailValidator.IsVisible = false;
                EmailValidator.Text = "";
                return true;
            }
        }
        catch (Exception ex)
        {
            return false;
        }
        return false;
    }

    private async Task<bool> CheckEmailExist(string email)
    {
        BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExistWithoutLoader(new IsEmailAlreadyExistModel() { email = email });
        if (existingUser != null)
        {
            if (existingUser.Result)
            {
                var actionOk = await HelperClass.DisplayCustomPopupForResult("Email already in use","Use another email or log into your existing account.",
                        "Use another email",AppResources.LogIn);



                // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                // {
                //     Title = "Email already in use",
                //     Message = "Use another email or log into your existing account.",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Use another email",
                //     CancelText = AppResources.LogIn,

                // };
                // var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                if (actionOk == PopupAction.OK)
                {
                    //GetEmail();
                }
                else
                {
                    ((App)Application.Current).displayCreateNewAccount = true;
                    WelcomePage page = new WelcomePage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                }

                return true;
            }
            else
            {
                return false;
            }

        }
        else
        {
            return false;
        }


    }

    private async void CreateAccountByGmail(object sender, EventArgs e)
    {
        //OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = "<EMAIL>", Name = "" }, GoogleActionStatus.Completed));
        //return;
        if (Connectivity.NetworkAccess != NetworkAccess.Internet)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
            return;
        }

// #if IOS
        try
        {
            Console.WriteLine("Login Started with google...");
            IFirebaseUser user = null;
            await CrossFirebaseAuthGoogle.Current.SignOutAsync();
            #if IOS
            {
                user = await MyFirebaseAuthGoogleImplementation.Instance.SignInWithGoogleAsync();
            }
            #else
            {
                user = await CrossFirebaseAuthGoogle.Current.SignInWithGoogleAsync();
            }
            #endif

            Console.WriteLine("Login complete...");
            if (user != null)
            {
                GoogleUser newUser = new GoogleUser() {
                    Id = user.Uid,
                    Name = user.DisplayName,
                    Email = user.Email,
                    Picture = user.PhotoUrl
                };

                await OnLoginCompleted(newUser);
                Console.WriteLine($"Email: {user.Email}");
                Console.WriteLine($"Name: {user.DisplayName}");
            }
            else
            {
                var message = "User is null.";
                Console.WriteLine(message);
                await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                           message, "OK", "");
            }
        }
        catch (FirebaseAuthException ex)
        {
            Console.WriteLine($"Firebase Auth exception: {ex.Message}");
            if (ex.Message != "12501: ") // USER CANCELLED ERROR FOR ANDROID
            {
            await HelperClass.DisplayCustomPopupForResult(AppResources.Error, ex.Message, "OK", "");
            }
        }
        catch (NullReferenceException)
        {
            Console.WriteLine("Null Reference exception.");
            await HelperClass.DisplayCustomPopupForResult(AppResources.Error, "Google Sign-In was cancelled.", "OK", "");
        }
        catch (TaskCanceledException)
        {
            // User canceled the sign-in
            Console.WriteLine("User cancelled the login.");
        }
        catch (Exception ex)
        {
            // Other unexpected errors
            Console.WriteLine("Login failed: " + ex.Message);
            await HelperClass.DisplayCustomPopupForResult(AppResources.Error, ex.Message, "OK", "");
        }

// #elif ANDROID
//         _googleClientManager.OnLogin += async (sender, loginEventArgs) => {
//             if (loginEventArgs != null && loginEventArgs.Data != null)
//             {
//                 Plugin.GoogleClient.MAUI.GoogleUser user = loginEventArgs.Data;
//                 if (user != null)
//                 {
//                     GoogleUser newUser = new GoogleUser()
//                     {
//                         Id = user.Id,
//                         Name = user.Name,
//                         Email = user.Email,
//                         Picture = user.Picture.ToString()
//                     };

//                     await OnLoginCompleted(newUser);
//                     Console.WriteLine($"Email: {user.Email}");
//                     Console.WriteLine($"Name: {user.Name}");
//                 }
//                 else
//                 {
//                     var message = "User is null.";
//                     Console.WriteLine(message);
//                     await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                                message, "OK", "");
//                 }
//             }
//         };

//         try
//         {
//             await _googleClientManager.LoginAsync();
//         }
//         catch (GoogleClientSignInNetworkErrorException ex)
//         {

//             await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                        ex.Message,"OK","");
//         }
//         catch (GoogleClientSignInCanceledErrorException ex)
//         {

//             await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                        ex.Message,"OK","");
//         }
//         catch (GoogleClientSignInInvalidAccountErrorException ex)
//         {
//         await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                    ex.Message,"OK","");
//         }
//         catch (GoogleClientSignInInternalErrorException ex)
//         {

//             await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                        ex.Message,"OK","");
//         }
//         catch (GoogleClientNotInitializedErrorException ex)
//         {

//             await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                        ex.Message,"OK","");
//         }
//         catch (GoogleClientBaseException ex)
//         {

//             await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
//                        ex.Message,"OK","");
//         }
// #endif
    }

    private async Task OnLoginCompleted(GoogleUser googleUser)
    {
        try
        {
            // _googleClientManager.OnLogin -= OnLoginCompleted;
            if (googleUser != null)
            {
                UserProfile user = new UserProfile();
                googleUser.Name = !string.IsNullOrEmpty(googleUser.Name) ? ((googleUser.Name.Contains(' ')) ? googleUser.Name.Split(' ')[0] : googleUser.Name) : googleUser.Name;
                user.Name = googleUser.Name;
                user.Email = googleUser.Email;
                if (user.Picture != null)
                    user.Picture = new Uri(googleUser.Picture);
                //var token = CrossGoogleClient.Current.ActiveToken;
                LocalDBManager.Instance.SetDBSetting("LoginType", "Social");
                LocalDBManager.Instance.SetDBSetting("GToken", "");
                if (user.Picture != null)
                    LocalDBManager.Instance.SetDBSetting("ProfilePic", user.Picture.OriginalString);

                //IsLoggedIn = true;
                bool IsExistingUser = false;
                BooleanModel existingUser = new BooleanModel();
                try
                {
                    if (string.IsNullOrEmpty(googleUser.Email) && !string.IsNullOrEmpty(googleUser.Id))
                    {
                        existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExistbyAppleId(new IsEmailAlreadyExistModel() { email = googleUser.Id });
                    }
                    else
                        existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(new IsEmailAlreadyExistModel() { email = user.Email });
                    if (existingUser != null)
                    {
                        if (existingUser.Result)
                        {
                            await LoginWithoutPopup(googleUser);
                            return;

                        }

                    }
                }
                catch (Exception ex)
                {

                }


                string mass = null;
                if (LocalDBManager.Instance.GetDBSetting("massunit") != null)
                    mass = LocalDBManager.Instance.GetDBSetting("massunit").Value;
                string body = null;
                if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                    body = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), mass).Kg.ToString();
                else
                    body = "60";

                try
                {
                    LoginSuccessResult lr = await DrMuscleRestClient.Instance.GoogleLogin("", user.Email, user.Name, body, mass, googleUser.Id);
                    if (lr != null)
                    {
                        UserInfosModel uim = null;
                        if (existingUser != null && existingUser.Result)
                        {
                            uim = await DrMuscleRestClient.Instance.GetUserInfo();

                            if (uim != null)
                            {
                                if (uim.Age == null || uim.Age == 0)
                                    uim.Age = 35;

                                if (uim.WeightGoal == null)
                                {
                                    uim.WeightGoal = new MultiUnityWeight();
                                    uim.WeightGoal.Entered = 75;
                                }

                                if (uim.BodyWeight == null)
                                {
                                    uim.BodyWeight = new MultiUnityWeight();
                                    uim.BodyWeight.Entered = 70;
                                }

                                if (uim.Height == null || uim.Height == 0)
                                {
                                    uim.Height = 173;
                                }
                            }
                        }
                        else
                        {
                            RegisterModel registerModel = new RegisterModel();
                            registerModel.Firstname = !string.IsNullOrEmpty(user.Name) ? ((user.Name.Contains(' ')) ? user.Name.Split(' ')[0] : user.Name) : user.Name;
                            registerModel.EmailAddress = user.Email;
                            LocalDBManager.Instance.SetDBSetting("email", user.Email);
                            LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(user.Name) ? ((user.Name.Contains(' ')) ? user.Name.Split(' ')[0] : user.Name) : user.Name);
                            registerModel.MassUnit = (LocalDBManager.Instance.GetDBSetting("massunit") != null) ? LocalDBManager.Instance.GetDBSetting("massunit").Value : null;
                            registerModel.Password = "";
                            registerModel.ConfirmPassword = "";
                            if (LocalDBManager.Instance.GetDBSetting("BodyWeight") != null)
                                registerModel.BodyWeight = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");
                            if (LocalDBManager.Instance.GetDBSetting("WeightGoal") != null)
                                registerModel.WeightGoal = new MultiUnityWeight(Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal").Value.Replace(",", ".").Replace("٫", "."), CultureInfo.InvariantCulture), "kg");


                            DependencyService.Get<IFirebase>().LogEvent("account_created", "");
                            LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                            LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                            await AccountCreatedPopup();
                            LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                            MainOnboardingPage page = new MainOnboardingPage();
                            page.OnBeforeShow();
                            await Navigation.PushAsync(page);
                        }
                        try
                        {
                            LocalDBManager.Instance.SetDBSetting("lastname", uim?.Lastname);
                            LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                            LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                            LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                            LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                            LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                            LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                            LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                            LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                            LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                            LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                            LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                            LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                            if (uim.Age != null)
                                LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                            if (uim.TargetIntake != null && uim.TargetIntake != 0)
                                LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());

                            LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                            LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                            LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

                            LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                            LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                            if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                            {
                                LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                                LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                            }
                            else
                            {
                                LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                                LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                            }
                            if (uim.Increments != null)
                                LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                            if (uim.Max != null)
                                LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                            if (uim.Min != null)
                                LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                            if (uim.BodyWeight != null)
                            {
                                LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                            }
                            if (uim.WeightGoal != null)
                            {
                                LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                            }
                            if (uim.WarmupsValue != null)
                            {
                                LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                            }

                            if (uim.EquipmentModel != null)
                            {
                                LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                                LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                                LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                                LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                                LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                            }
                            else
                            {
                                LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                                LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                                LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                                LocalDBManager.Instance.SetDBSetting("Plate", "true");
                                LocalDBManager.Instance.SetDBSetting("Pully", "true");
                            }
                            if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                                LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                            else
                                LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                            ((App)Application.Current).displayCreateNewAccount = true;

                            if (uim.Gender.Trim().ToLowerInvariant().Equals("man"))
                                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "Background2.png");
                            else
                                LocalDBManager.Instance.SetDBSetting("BackgroundImage", "BackgroundFemale.png");

                            if (IsExistingUser)
                            {
                                App.IsDemoProgress = false;
                                LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                                await Navigation.PopToRootAsync(true);
                                return;
                            }
                            await AccountCreatedPopup();
                            //SetUpRestOnboarding();
                            LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                            MainOnboardingPage page = new MainOnboardingPage();
                            page.OnBeforeShow();
                            await Navigation.PushAsync(page);
                            // CancelNotification();
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                    else
                    {
                        UserDialogs.Instance.Alert(new AlertConfig()
                        {
                            Message = AppResources.EmailAndPasswordDoNotMatch,
                            Title = AppResources.UnableToLogIn,
                            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                        });
                    }
                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                // UserDialogs.Instance.Alert(new AlertConfig()
                // {
                //     Message = AppResources.EmailAndPasswordDoNotMatch,
                //     Title = AppResources.UnableToLogIn,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.UnableToLogIn,
                       AppResources.EmailAndPasswordDoNotMatch, "OK", "");
            }

            // _googleClientManager.OnLogin -= OnLoginCompleted;
        }
        catch (Exception ex)
        {
            // UserDialogs.Instance.Alert(new AlertConfig()
            // {
            //     Message = loginEventArgs.Message,
            //     Title = AppResources.Error,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            // });
            await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                       ex.Message, "OK", "");

        }

    }
    public async Task LoginWithoutPopup(GoogleUser googleUser)
    {
        LoginSuccessResult lr = await DrMuscleRestClient.Instance.GoogleLogin("", googleUser.Email, googleUser.Name, null, null,googleUser.Id);
        if (lr != null)
        {
            UserInfosModel uim = null;

                uim = await DrMuscleRestClient.Instance.GetUserInfo();
                CancelNotification();
                try
                {

                    if (uim != null)
                    {
                        if (uim.Age == null || uim.Age == 0)
                            uim.Age = 35;

                        if (uim.WeightGoal == null)
                        {
                            uim.WeightGoal = new MultiUnityWeight();
                            uim.WeightGoal.Entered = 75;
                        }

                        if (uim.BodyWeight == null)
                        {
                            uim.BodyWeight = new MultiUnityWeight();
                            uim.BodyWeight.Entered = 70;
                        }

                        if (uim.Height == null || uim.Height == 0)
                        {
                            uim.Height = 173;
                        }
                    }


                    LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                    LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                    LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                    LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                    LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                    LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                    LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                    LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                    LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                    LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                    LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false");
                    LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                    LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");

                    LocalDBManager.Instance.SetDBSetting("DailyReset", Convert.ToString(uim.DailyExerciseCount));
                    LocalDBManager.Instance.SetDBSetting("WeeklyReset", Convert.ToString(uim.WeeklyExerciseCount));

                    LocalDBManager.Instance.SetDBSetting("IsEmailReminder", uim.IsReminderEmail ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("ReminderHours", uim.ReminderBeforeHours.ToString());
                    if (uim.ReminderTime != null)
                        LocalDBManager.Instance.SetDBSetting("ReminderTime", uim.ReminderTime.ToString());
                    if (uim.ReminderDays != null)
                        LocalDBManager.Instance.SetDBSetting("ReminderDays", uim.ReminderDays);
                    if (uim.Age != null)
                        LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                    LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                    LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Reminder5th", uim.IsReminder ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("LastWorkoutWas", uim.LastWorkoutWas);
                    LocalDBManager.Instance.SetDBSetting("IsMobility", uim.IsMobility == null ? null : uim.IsMobility == false ? "false" : "true");
                    LocalDBManager.Instance.SetDBSetting("MaxWorkoutDuration", uim.WorkoutDuration.ToString());
                    LocalDBManager.Instance.SetDBSetting("IsExerciseQuickMode", uim.IsExerciseQuickMode == null ? null : uim.IsExerciseQuickMode == false ? "false" : "true");
                    LocalDBManager.Instance.SetDBSetting("MobilityLevel", uim.MobilityLevel);
                    LocalDBManager.Instance.SetDBSetting("MobilityRep", uim.MobilityRep == null ? "" : Convert.ToString(uim.MobilityRep));
                    SetupEquipment(uim);
                    if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                    else
                        LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                    if (uim.IsPyramid)
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                        LocalDBManager.Instance.SetDBSetting("IsRPyramid", "true");
                    }
                    else if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                    }
                    else
                    {
                        LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                        LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                    }
                    if (uim.Increments != null)
                        LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                    if (uim.Max != null)
                        LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                    if (uim.Min != null)
                        LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                    if (uim.BodyWeight != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WeightGoal != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                    }
                    if (uim.WarmupsValue != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                    }
                    if (uim.SetCount != null)
                    {
                        LocalDBManager.Instance.SetDBSetting("WorkSetCount", Convert.ToString(uim.SetCount));
                    }

                ((App)Application.Current).displayCreateNewAccount = true;

                    if (uim.Height != null)
                        LocalDBManager.Instance.SetDBSetting("Height", uim.Height.ToString());
                    if (uim.TargetIntake != null)
                        LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());
                    LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("RecommendedReminder", uim.IsRecommendedReminder == true ? "true" : uim.IsRecommendedReminder == null ? "null" : "false");
                    await Navigation.PopToRootAsync(true);
                    App.RegisterDeviceToken();
                    MessagingCenter.Send(this, "BackgroundImageUpdated");
                    //await PagesFactory.PushAsync<MainAIPage>();


                }
                catch (Exception ex)
                {

                }
                try
                {
                    DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
                    if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                    {
                        LocalDBManager.Instance.SetDBSetting("IsPurchased", "true");
                        App.IsV1UserTrial = true;
                    }
                }
                catch (Exception ex)
                {

                }
            }
            else
            {
                // UserDialogs.Instance.Alert(new AlertConfig()
                // {
                //     Message = AppResources.EmailAndPasswordDoNotMatch,
                //     Title = AppResources.UnableToLogIn,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.UnableToLogIn,
                           AppResources.EmailAndPasswordDoNotMatch,"OK","");
            }
        }
    private void SetupEquipment(UserInfosModel uim)
    {
        LocalDBManager.Instance.SetDBSetting("KgBarWeight", uim.KgBarWeight == null ? "20" : Convert.ToString(uim.KgBarWeight).ReplaceWithDot());
        LocalDBManager.Instance.SetDBSetting("LbBarWeight", uim.LbBarWeight == null ? "45" : Convert.ToString(uim.LbBarWeight).ReplaceWithDot());
        if (uim.EquipmentModel != null)
        {
            LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("Bands", uim.EquipmentModel.IsBands ? "true" : "false");

            LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", uim.EquipmentModel.IsHomeEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeChinUp", uim.EquipmentModel.IsHomeChinupBar ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", uim.EquipmentModel.IsHomeDumbbell ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomePlate", uim.EquipmentModel.IsHomePlate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomePully", uim.EquipmentModel.IsHomePully ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("HomeBands", uim.EquipmentModel.IsHomeBands ? "true" : "false");

            LocalDBManager.Instance.SetDBSetting("OtherMainEquipment", uim.EquipmentModel.IsOtherEquipmentEnabled ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherChinUp", uim.EquipmentModel.IsOtherChinupBar ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", uim.EquipmentModel.IsOtherDumbbell ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherPlate", uim.EquipmentModel.IsOtherPlate ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherPully", uim.EquipmentModel.IsOtherPully ? "true" : "false");
            LocalDBManager.Instance.SetDBSetting("OtherBands", uim.EquipmentModel.IsOtherBands ? "true" : "false");

            if (uim.EquipmentModel.Active == "gym")
                LocalDBManager.Instance.SetDBSetting("GymEquipment", "true");
            if (uim.EquipmentModel.Active == "home")
                LocalDBManager.Instance.SetDBSetting("HomeEquipment", "true");
            if (uim.EquipmentModel.Active == "other")
                LocalDBManager.Instance.SetDBSetting("OtherEquipment", "true");

            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableDumbbell))
            {
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", uim.EquipmentModel.AvilableDumbbell);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", uim.EquipmentModel.AvilableHomeDumbbell);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", uim.EquipmentModel.AvilableOtherDumbbell);

                LocalDBManager.Instance.SetDBSetting("DumbbellLb", uim.EquipmentModel.AvilableLbDumbbell);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", uim.EquipmentModel.AvilableHomeLbDumbbell);
            }
            else
            {
                var kgString = "50_2_True|47.5_2_True|45_2_True|42.5_2_True|40_2_True|37.5_2_True|35_2_True|32.5_2_True|30_2_True|27.5_2_True|25_2_True|22.5_2_True|20_2_True|17.5_2_True|15_2_True|12.5_2_True|10_2_True|7.5_2_True|5_2_True|2.5_2_True|1_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellKg", kgString);

                var lbString = "90_2_True|85_2_True|80_2_True|75_2_True|70_2_True|65_2_True|60_2_True|55_2_True|50_2_True|45_2_True|40_2_True|35_2_True|30_2_True|25_2_True|20_2_True|15_2_True|12_2_True|10_2_True|8_2_True|5_2_True|3_2_True|2_2_True";
                LocalDBManager.Instance.SetDBSetting("DumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeDumbbellLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherDumbbellLb", lbString);
            }
            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePlate))
            {
                LocalDBManager.Instance.SetDBSetting("PlatesKg", uim.EquipmentModel.AvilablePlate);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", uim.EquipmentModel.AvilableHomePlate);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", uim.EquipmentModel.AvilableOtherPlate);

                LocalDBManager.Instance.SetDBSetting("PlatesLb", uim.EquipmentModel.AvilableLbPlate);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", uim.EquipmentModel.AvilableHomeLbPlate);
            }
            else
            {
                var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesKg", kgString);

                var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
                LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePlatesLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPlatesLb", lbString);
            }

            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilablePulley))
            {

                LocalDBManager.Instance.SetDBSetting("PulleyKg", uim.EquipmentModel.AvilablePulley);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", uim.EquipmentModel.AvilableHomePulley);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", uim.EquipmentModel.AvilableOtherPulley);


                LocalDBManager.Instance.SetDBSetting("PulleyLb", uim.EquipmentModel.AvilableLbPulley);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", uim.EquipmentModel.AvilableHomeLbPulley);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", uim.EquipmentModel.AvilableOtherLbPulley);
            }
            else
            {

                var kgString = "5_20_True|1.5_2_True";
                var lbString = "10_20_True|5_2_True|2.5_2_True";

                LocalDBManager.Instance.SetDBSetting("PulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyKg", kgString);


                LocalDBManager.Instance.SetDBSetting("PulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomePulleyLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherPulleyLb", lbString);
            }

            if (!string.IsNullOrEmpty(uim.EquipmentModel.AvilableBands))
            {

                LocalDBManager.Instance.SetDBSetting("BandsKg", uim.EquipmentModel.AvilableBands);
                LocalDBManager.Instance.SetDBSetting("HomeBandsKg", uim.EquipmentModel.AvilableHomeBands);
                LocalDBManager.Instance.SetDBSetting("OtherBandsKg", uim.EquipmentModel.AvilableOtherBands);


                LocalDBManager.Instance.SetDBSetting("BandsLb", uim.EquipmentModel.AvilableLbBands);
                LocalDBManager.Instance.SetDBSetting("HomeBandsLb", uim.EquipmentModel.AvilableHomeLbBands);
                LocalDBManager.Instance.SetDBSetting("OtherBandsLb", uim.EquipmentModel.AvilableOtherLbBands);
            }
            else
            {

                var kgString = "Black_40_2_True|Blue_30_2_True|Green_20_2_True|Red_10_2_True|Yellow_4_2_True";
                var lbString = "Black_90_2_True|Blue_65_2_True|Green_45_2_True|Red_25_2_True|Yellow_10_2_True";

                LocalDBManager.Instance.SetDBSetting("BandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsKg", kgString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsKg", kgString);

                LocalDBManager.Instance.SetDBSetting("BandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("HomeBandsLb", lbString);
                LocalDBManager.Instance.SetDBSetting("OtherBandsLb", lbString);
            }
        }
        else
        {
            LocalDBManager.Instance.SetDBSetting("Equipment", "false");
            LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("Plate", "true");
            LocalDBManager.Instance.SetDBSetting("Pully", "true");
            LocalDBManager.Instance.SetDBSetting("Bands", "true");

            LocalDBManager.Instance.SetDBSetting("HomeMainEquipment", "false");
            LocalDBManager.Instance.SetDBSetting("HomeChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("HomeDumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("HomePlate", "true");
            LocalDBManager.Instance.SetDBSetting("HomePully", "true");
            LocalDBManager.Instance.SetDBSetting("HomeBands", "true");

            LocalDBManager.Instance.SetDBSetting("OtherEquipment", "false");
            LocalDBManager.Instance.SetDBSetting("OtherChinUp", "true");
            LocalDBManager.Instance.SetDBSetting("OtherDumbbell", "true");
            LocalDBManager.Instance.SetDBSetting("OtherPlate", "true");
            LocalDBManager.Instance.SetDBSetting("OtherPully", "true");
            LocalDBManager.Instance.SetDBSetting("OtherBands", "true");

        }


    }
    private async Task AccountCreatedPopup()
    {
        var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);

        var modalPage = new Views.GeneralPopup("truestate.png", "Success!", "Account created", "Customize program");
        modalPage.Closed += (sender2, e2) =>
        {
            waitHandle.Set();
        };
        await Application.Current.MainPage.ShowPopupAsync(modalPage);

        await Task.Run(() => waitHandle.WaitOne());

    }
    private async void CreateAccountByFacebook(object sender, EventArgs e)
    {
        // uncomment code please
        //_manager = DependencyService.Get<IFacebookManager>();
        //if (!CrossConnectivity.Current.IsConnected)
        //{
        //    await UserDialogs.Instance.AlertAsync(new AlertConfig()
        //    {
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //        Message = AppResources.PleaseCheckInternetConnection,
        //        Title = AppResources.ConnectionError
        //    });
        //    return;
        //}
        //FacebookUser result = await _manager.Login();
        //if (result == null)
        //{
        //    UserDialogs.Instance.Alert(new AlertConfig()
        //    {
        //        Message = AppResources.AnErrorOccursWhenSigningIn,
        //        Title = AppResources.UnableToLogIn,
        //        AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        //    });
        //    return;
        //}

        //Device.BeginInvokeOnMainThread(async () =>
        //{
        //    await WelcomePage_OnFBLoginSucceded(result.Id, result.Email, "", result.Token, !string.IsNullOrEmpty(result.FirstName) ? result.FirstName.Contains(' ') ? result.FirstName.Split(' ')[0] : result.FirstName : result.FirstName);
        //});
    }
    private async Task WelcomePage_OnFBLoginSucceded(string FBId, string FBEmail, string FBGender, string FBToken, string firstname)
    {
        if (string.IsNullOrEmpty(FBEmail))
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = "Your Facebook account is not connected with email (or we do not have permission to access it). Please sign up with email.",
            //     Title = AppResources.Error
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                       "Your Facebook account is not connected with email (or we do not have permission to access it). Please sign up with email.","OK","");

            return;
        }
        LocalDBManager.Instance.SetDBSetting("LoginType", "Social");
        LocalDBManager.Instance.SetDBSetting("FBId", FBId);
        LocalDBManager.Instance.SetDBSetting("FBEmail", FBEmail);
        LocalDBManager.Instance.SetDBSetting("firstname", firstname);
        LocalDBManager.Instance.SetDBSetting("FBGender", FBGender);
        LocalDBManager.Instance.SetDBSetting("FBToken", FBToken);
        var url = $"http://graph.facebook.com/{FBId}/picture?type=square";
        LocalDBManager.Instance.SetDBSetting("ProfilePic", url);



        BooleanModel existingUser = await DrMuscleRestClient.Instance.IsEmailAlreadyExist(new IsEmailAlreadyExistModel() { email = FBEmail });
        bool IsExistingUser = false;
        if (existingUser != null)
        {
            if (existingUser.Result)
            {
                var actionOk = await HelperClass.DisplayCustomPopupForResult("You are already registered","Use another account or log into your existing account.",
                        "Use another account",AppResources.LogIn);



                // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
                // {
                //     Title = "You are already registered",
                //     Message = "Use another account or log into your existing account.",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Use another account",
                //     CancelText = AppResources.LogIn,

                // };
                // var actionOk = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
                if (actionOk == PopupAction.OK)
                {
                    return;
                }
                else
                {
                    IsExistingUser = true;
                }

            }

        }
        //Log in d'un compte existant avec Facebook
        string mass = "lb";
        string body = null;
        body = new MultiUnityWeight(150, "lb").Kg.ToString();
        try
        {


            LoginSuccessResult lr = await DrMuscleRestClient.Instance.FacebookLogin(FBToken, body, mass);
            if (lr != null)
            {
                DateTime current = DateTime.Now;
                UserInfosModel uim = null;
                if (existingUser.Result)
                {
                    uim = await DrMuscleRestClient.Instance.GetUserInfo();
                    if (uim != null)
                    {
                        if (uim.Age == null || uim.Age == 0)
                            uim.Age = 35;

                        if (uim.WeightGoal == null)
                        {
                            uim.WeightGoal = new MultiUnityWeight();
                            uim.WeightGoal.Entered = 75;
                        }

                        if (uim.BodyWeight == null)
                        {
                            uim.BodyWeight = new MultiUnityWeight();
                            uim.BodyWeight.Entered = 70;
                        }

                        if (uim.Height == null || uim.Height == 0)
                        {
                            uim.Height = 173;
                        }
                    }

                }
                else
                {
                    RegisterModel registerModel = new RegisterModel();
                    registerModel.Firstname = firstname;
                    registerModel.EmailAddress = FBEmail;

                    registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;

                    if (LocalDBManager.Instance.GetDBSetting("Age") != null)
                        registerModel.Age = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("Age").Value);

                    registerModel.BodyWeight = new MultiUnityWeight(150, "lb");
                    registerModel.Password = "";
                    registerModel.ConfirmPassword = "";

                    await AccountCreatedPopup();
                    LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");
                    var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
                    _firebase.LogEvent("account_created", "");
                    //New Code
                    MainOnboardingPage page1 = new MainOnboardingPage();
                    page1.OnBeforeShow();
                    await Navigation.PushAsync(page1);
                    //New Code
                    return;
                }
                LocalDBManager.Instance.SetDBSetting("email", uim.Email);
                if (!string.IsNullOrEmpty(uim.Firstname))
                    LocalDBManager.Instance.SetDBSetting("firstname", !string.IsNullOrEmpty(uim.Firstname) ? ((uim.Firstname.Contains(' ')) ? uim.Firstname.Split(' ')[0] : uim.Firstname) : uim.Firstname);
                LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
                LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
                LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
                LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
                LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
                LocalDBManager.Instance.SetDBSetting("reprange", "Custom");
                LocalDBManager.Instance.SetDBSetting("reprangeType", uim.ReprangeType.ToString());
                LocalDBManager.Instance.SetDBSetting("repsminimum", Convert.ToString(uim.RepsMinimum));
                LocalDBManager.Instance.SetDBSetting("repsmaximum", Convert.ToString(uim.RepsMaximum));
                LocalDBManager.Instance.SetDBSetting("QuickMode", uim.IsQuickMode == true ? "true" : uim.IsQuickMode == null ? "null" : "false"); LocalDBManager.Instance.SetDBSetting("WorkoutTypeList", "0");
                LocalDBManager.Instance.SetDBSetting("ExerciseTypeList", "0");
                LocalDBManager.Instance.SetDBSetting("onboarding_seen", "true");
                if (uim.Age != null)
                    LocalDBManager.Instance.SetDBSetting("Age", Convert.ToString(uim.Age));
                if (uim.TargetIntake != null && uim.TargetIntake != 0)
                    LocalDBManager.Instance.SetDBSetting("TargetIntake", uim.TargetIntake.ToString());

                LocalDBManager.Instance.SetDBSetting("timer_vibrate", uim.IsVibrate ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_sound", uim.IsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_123_sound", uim.IsTimer321 ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_reps_sound", uim.IsRepsSound ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autostart", uim.IsAutoStart ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_autoset", uim.IsAutomatchReps ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", uim.IsFullscreen ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("timer_count", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("timer_remaining", uim.TimeCount.ToString());
                LocalDBManager.Instance.SetDBSetting("Cardio", uim.IsCardio ? "true" : "false");

                LocalDBManager.Instance.SetDBSetting("BackOffSet", uim.IsBackOffSet ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("1By1Side", uim.Is1By1Side ? "true" : "false");
                LocalDBManager.Instance.SetDBSetting("StrengthPhase", uim.IsStrength ? "true" : "false");
                if (uim.IsNormalSet == null || uim.IsNormalSet == true)
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "Normal");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", uim.IsNormalSet == null ? "true" : "false");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("SetStyle", "RestPause");
                    LocalDBManager.Instance.SetDBSetting("IsPyramid", "false");
                }
                if (uim.Increments != null)
                    LocalDBManager.Instance.SetDBSetting("workout_increments", uim.Increments.Kg.ToString().ReplaceWithDot());
                if (uim.Max != null)
                    LocalDBManager.Instance.SetDBSetting("workout_max", uim.Max.Kg.ToString().ReplaceWithDot());
                if (uim.Min != null)
                    LocalDBManager.Instance.SetDBSetting("workout_min", uim.Min.Kg.ToString().ReplaceWithDot());
                if (uim.BodyWeight != null)
                {
                    LocalDBManager.Instance.SetDBSetting("BodyWeight", uim.BodyWeight.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WeightGoal != null)
                {
                    LocalDBManager.Instance.SetDBSetting("WeightGoal", uim.WeightGoal.Kg.ToString().ReplaceWithDot());
                }
                if (uim.WarmupsValue != null)
                {
                    LocalDBManager.Instance.SetDBSetting("warmups", Convert.ToString(uim.WarmupsValue));
                }
                if (uim.EquipmentModel != null)
                {
                    LocalDBManager.Instance.SetDBSetting("Equipment", uim.EquipmentModel.IsEquipmentEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("ChinUp", uim.EquipmentModel.IsChinUpBarEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", uim.EquipmentModel.IsDumbbellEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Plate", uim.EquipmentModel.IsPlateEnabled ? "true" : "false");
                    LocalDBManager.Instance.SetDBSetting("Pully", uim.EquipmentModel.IsPullyEnabled ? "true" : "false");
                }
                else
                {
                    LocalDBManager.Instance.SetDBSetting("Equipment", "false");
                    LocalDBManager.Instance.SetDBSetting("ChinUp", "true");
                    LocalDBManager.Instance.SetDBSetting("Dumbbell", "true");
                    LocalDBManager.Instance.SetDBSetting("Plate", "true");
                    LocalDBManager.Instance.SetDBSetting("Pully", "true");
                }
                ((App)Application.Current).displayCreateNewAccount = true;

                if (string.IsNullOrEmpty(uim.BodyPartPrioriy))
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", "");
                else
                    LocalDBManager.Instance.SetDBSetting("BodypartPriority", uim.BodyPartPrioriy.Trim());

                //await PagesFactory.PopToRootAsync(true);
                //await PagesFactory.PushAsync<MainAIPage>();
                //    App.IsWelcomeBack = true;
                //    App.IsDemoProgress = false;
                //LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                //await PopupNavigation.Instance.PushAsync(new ReminderPopup());
                if (IsExistingUser)
                {
                    App.IsDemoProgress = false;
                    LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                    await Navigation.PopToRootAsync();
                    //await PagesFactory.PopToRootAsync(true);
                    return;
                }
                await AccountCreatedPopup();
                //SetUpRestOnboarding();
                LocalDBManager.Instance.SetDBSetting("FirstStepCompleted", "true");

                MainOnboardingPage page = new MainOnboardingPage();
                page.OnBeforeShow();
                await Navigation.PushAsync(page);
            }
            else
            {
                // UserDialogs.Instance.Alert(new AlertConfig()
                // {
                //     Message = AppResources.EmailAndPasswordDoNotMatch,
                //     Title = AppResources.UnableToLogIn,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
                // });

                await HelperClass.DisplayCustomPopupForResult(AppResources.UnableToLogIn,
                       AppResources.EmailAndPasswordDoNotMatch,"OK","");
            }
        }
        catch (Exception ex)
        {

            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = "We are facing problem to signup with your facebook account. Please sign up with email.",
            //     Title = AppResources.Error
            // });
            await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                       "We are facing problem to signup with your facebook account. Please sign up with email.","OK","");

        }
        
    }

    private async void CreateAccountByApple(object sender, EventArgs e)
    {
        //OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = "nazim151.appleid.com", Name = "" }, GoogleActionStatus.Completed));
        //OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = "<EMAIL>", Name = "" }, GoogleActionStatus.Completed));
        //return;

        var account = await appleSignInService.SignInAsync();
        if (account != null)
        {
            if (!string.IsNullOrEmpty(account.Email))
            {
                await SecureStorage.SetAsync("Email", account.Email);
                if (!string.IsNullOrEmpty(account.Name))
                {
                    account.Name = !string.IsNullOrEmpty(account.Name) ? ((account.Name.Contains(' ')) ? account.Name.Split(' ')[0] : account.Name) : account.Name;
                    await SecureStorage.SetAsync("Name", account.Name);
                }
                else if (!string.IsNullOrEmpty(account.GivenName))
                {
                    account.GivenName = !string.IsNullOrEmpty(account.GivenName) ? ((account.GivenName.Contains(' ')) ? account.GivenName.Split(' ')[0] : account.GivenName) : account.GivenName;
                    await SecureStorage.SetAsync("Name", account.GivenName);
                }
                else if (!string.IsNullOrEmpty(account.FamilyName))
                {
                    account.FamilyName = !string.IsNullOrEmpty(account.FamilyName) ? ((account.FamilyName.Contains(' ')) ? account.FamilyName.Split(' ')[0] : account.FamilyName) : account.FamilyName;
                    await SecureStorage.SetAsync("Name", account.FamilyName);
                }
                else
                    await SecureStorage.SetAsync("Name", "  ");
            }
            else
            {
                string email = await SecureStorage.GetAsync("Email");
                string name = await SecureStorage.GetAsync("Name");
                account.Email = email;
                account.Name = name;
            }
            if (string.IsNullOrEmpty(account.Email))
            {
                if (!string.IsNullOrEmpty(account.UserId))
                {
                    await OnLoginCompleted(new GoogleUser() { Id = account.UserId, Name = account.Name, Email = account.Email});
                    // OnLoginCompleted(null, new GoogleClientResultEventArgs<GoogleUser>(new GoogleUser() { Email = account.Email, Name = account.Name, Id = account.UserId }, GoogleActionStatus.Completed));
                    return;
                }
                else
                {
                    await HelperClass.DisplayCustomPopupForResult("Email Not Available.",
                            "Please remove Dr. Muscle from Apple ID settings and sign in again.", "OK", "");
                    return;
                }
                    

                //await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                //       "We haven't get email. Please login with email.","OK","");

                
            }

            var user = new GoogleUser() {
                Name = account.Name,
                Email = account.Email
            };
            await OnLoginCompleted(user);
        }
    }

    private void EmailTextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            var text = e.NewTextValue as string;
            if (!string.IsNullOrEmpty(text))
            {
                EmailValidator.IsVisible = false;
            }
        }
        catch (Exception ex)
        {
            EmailValidator.IsVisible = false;
        }
    }

    private void PasswordTextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            var text = e.NewTextValue as string;
            if (!string.IsNullOrEmpty(text))
            {
                if (text.Length < 6)
                {
                    PasswordValidator.IsVisible = true;
                    PasswordValidator.Text = "At least 6 characters";
                }
                else
                {
                    PasswordValidator.IsVisible = false;
                }
            }
            else
            {
                PasswordValidator.IsVisible = false;
            }
        }
        catch (Exception ex)
        {
            PasswordValidator.IsVisible = false;
        }

    }


    protected override void OnDisappearing()
    {
        try
        {
            // Clean up event handlers to prevent memory leaks
            if (EmailEntry != null)
                EmailEntry.TextChanged -= EmailTextChanged;

            if (PasswordEntry != null)
                PasswordEntry.TextChanged -= PasswordTextChanged;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in OnDisappearing: {ex.Message}");
        }

        base.OnDisappearing();
    }

}