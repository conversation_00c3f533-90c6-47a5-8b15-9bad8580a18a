<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Exercises.SaveSetPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             Title="SaveSetPage">
    <AbsoluteLayout>
        <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,10,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
            <StackLayout VerticalOptions="FillAndExpand" BackgroundColor="Transparent">
                <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand" BackgroundColor="Transparent">
                    <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand" VerticalOptions="Center">
                        <Label x:Name="DoLabel" Style="{StaticResource LabelStyle}" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" FontSize="17"/>
                    </StackLayout>
                    <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand" VerticalOptions="Center">
                        <Label x:Name="DoLabel2" Text="Do this now:" Style="{StaticResource LabelStyle}" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" FontSize="17"/>
                    </StackLayout>
                    <Button IsVisible="false"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>


                        <Label x:Name="LblReps" Grid.Row="0" Grid.Column="0"  VerticalOptions="Center" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}"></Label>
                        <StackLayout Orientation="Horizontal" Grid.Row="0" Grid.Column="1" >
                            <t:DrMuscleButton x:Name="RepsLess" Text="-" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" WidthRequest="50" />
                            <t:DrMuscleEntry x:Name="RepsEntry" Text="10" HorizontalOptions="FillAndExpand" Keyboard="Numeric" HorizontalTextAlignment="Center" WidthRequest="100" Style="{StaticResource entryStyle}">
                            </t:DrMuscleEntry>
                            <t:DrMuscleButton x:Name="RepsMore" Text="+" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" WidthRequest="50" />
                        </StackLayout>
                        <t:DrMuscleButton  Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" x:Name="SaveSet" Style="{StaticResource buttonStyle}" Clicked="SaveSet_Clicked" HeightRequest="70">
                        </t:DrMuscleButton>

                        <Label Grid.Row="1" Grid.Column="0" VerticalOptions="Center" VerticalTextAlignment="Center" x:Name="LblWeight" Style="{StaticResource LabelStyle}" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" ></Label>
                        <StackLayout Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                            <t:DrMuscleButton x:Name="WeightLess" Text="-" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" WidthRequest="50" />
                            <t:DrMuscleEntry x:Name="WeightEntry" Text="10" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" WidthRequest="100" Style="{StaticResource entryStyle}" Unfocused="WeightEntry_Unfocused">
                            </t:DrMuscleEntry>
                            <t:DrMuscleButton x:Name="WeightMore" Text="+" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" WidthRequest="50" />
                        </StackLayout>
                    </Grid>
                    <StackLayout VerticalOptions="StartAndExpand">
                        <!--<t:DrMuscleListView x:Name="SetListView" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="White">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <ViewCell Height="45">
                                        <StackLayout Orientation="Horizontal" BackgroundColor="Transparent">
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                                                <Label Text="{Binding SetLabel}" HorizontalOptions="Center" VerticalOptions="CenterAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" WidthRequest="170">
                                                </Label>
                                            </StackLayout>
                                            <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                                                <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}" />
                                                <t:DrMuscleButton Clicked="OnEdit" Text="{Binding [Edit].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextEditButton}" />
                                                <t:DrMuscleButton Clicked="OnDelete" Text="{Binding [Delete].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextDeleteButton}" />
                                                <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" Style="{StaticResource ItemContextMoreButton}" />
                                            </StackLayout>
                                        </StackLayout>
                                    </ViewCell>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </t:DrMuscleListView>-->
                    </StackLayout>
                </StackLayout>
                <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
                    <t:DrMuscleButton x:Name="FinishedButton" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonLinkStyle}" />
                    <t:DrMuscleButton x:Name="SupersetButton" HorizontalOptions="End" Style="{StaticResource buttonLinkStyle}" WidthRequest="50" />
                </StackLayout>
            </StackLayout>
        </StackLayout>
    </AbsoluteLayout>
</ContentPage>