using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Cells;

public partial class MealSurveyTemplate : ContentView
{
    #region privateField
    private static bool _shouldAnimateButton = false;
    private static bool _isProcessRunning = false;
    #endregion
    public MealSurveyTemplate()
	{
		InitializeComponent();
	}
    void SadReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad)
            return;

        FireMessage(SatisfactionSurveyEnum.Sad.ToString());
    }

    void NeutralReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Neutral)
            return;

        FireMessage(SatisfactionSurveyEnum.Neutral.ToString());
    }

    void HappyReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Happy)
            return;

        FireMessage(SatisfactionSurveyEnum.Happy.ToString());
    }

    void FireMessage(string selectedSurveyType)
    {
        _shouldAnimateButton = true;
        MessagingCenter.Send<string>(selectedSurveyType, "MealResponseGiven");
        DependencyService.Get<IFirebase>().LogEvent($"Meal_Survey_{selectedSurveyType}", $"MealSatisfactionSurveyReported : {selectedSurveyType}");

    }

    /// <summary>
    /// Animate the given ImageButton.
    /// </summary>
    /// <param name="img">Represent the Image button to show animation on it.</param>
    private async void AnimateImage(View img)
    {
        System.Diagnostics.Debug.WriteLine($"Animate Image : {_shouldAnimateButton}");
        if (!_shouldAnimateButton)
            return;

        /* Shake Animation */
        uint Time = 100;
        int Rounds = 2;
        for (int i = 0; i < Rounds; i++)
        {
            await img.TranslateTo(15, 0, Time);
            await img.TranslateTo(0, 0, Time);
            await img.TranslateTo(-15, 0, Time);
            await img.TranslateTo(0, 0, Time);
        }
        _shouldAnimateButton = false;
    }
    protected override void OnParentSet()
    {
        base.OnParentSet();

        if (Parent != null)
        {
            OnAppearing();
        }
    }
    public void OnAppearing()
    {
        //base.OnAppearing();
        BotModel botModel = BindingContext as BotModel;

        LabelMessage.TextColor = Color.FromHex("#26262B");
        if (botModel.SelectedSurveyOption != SatisfactionSurveyEnum.None)
        {
            switch (botModel.SelectedSurveyOption)
            {
                case SatisfactionSurveyEnum.Sad:
                    AnimateImage(PancakeSadReview);
                    break;
                case SatisfactionSurveyEnum.Neutral:
                    AnimateImage(PancakeNeutralReview);
                    break;
                case SatisfactionSurveyEnum.Happy:
                    AnimateImage(PancakeHappyReview);
                    break;
            }
        }
    }

    async void BtnShareFreeTrial_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isProcessRunning)
            return;

        _isProcessRunning = true;
        //LogSatisfactionSurvey();
        await HelperClass.ShareApp("Meal_Satisfaction_survey");

        _isProcessRunning = false;
    }

    async void BtnRate5Stars_Clicked(System.Object sender, System.EventArgs e)
    {
        await Task.Delay(500);
        await HelperClass.RateApp("rate_5_star_meal_satisfaction_survey");
        //LogSatisfactionSurvey();

    }

    async void BtnEmailUs_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isProcessRunning)
            return;
        _isProcessRunning = true;
        //LogSatisfactionSurvey();

        BotModel botModel = BindingContext as BotModel;
        await HelperClass.SendMail(botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad ? "Bad experience with meal plan" : "Average experience with meal plan");

        _isProcessRunning = false;
    }

    async void BtnChatWithUs_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (_isProcessRunning)
                return;
            _isProcessRunning = true;

            BotModel botModel = BindingContext as BotModel;
            string messageToSend = string.Empty;
            if (botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad)
                messageToSend = "Unhappy with meal plan";
            else if (botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Neutral)
                messageToSend = "My experience with meal plan inside app was average";


            //open chat page
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<string>(messageToSend, "ChatWithUsFromSatisfactionSurvey");

            _isProcessRunning = false;
        }
        catch(Exception ex)
        {

        }
    }

    private async void LogSatisfactionSurvey()
    {
        BotModel botModel = BindingContext as BotModel;
        await DrMuscleRestClient.Instance.AddSatisfactionSurvey(new DrMuscleWebApiSharedModel.SatisfactionSurveyModel()
        {
            CreatedAt = System.DateTime.Now,
            Answer = (int)botModel.SelectedSurveyOption,
        });
    }

    private void BtnChangePlan(object sender, EventArgs e)
    {
        MessagingCenter.Send<string>("ChangePlan", "ChangePlan");
    }
}