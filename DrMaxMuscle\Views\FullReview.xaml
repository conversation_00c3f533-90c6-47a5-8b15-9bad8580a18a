<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
               BackgroundColor="#99000000"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
              xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.FullReview">
    <toolkit:PopupPage.Content>
        <Frame
        Padding="0"
        CornerRadius="4"
        HasShadow="False"
        IsClippedToBounds="True"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="CenterAndExpand"
            BorderColor="Transparent"
        BackgroundColor="Transparent"
        Margin="20,0,20,0">
            <Grid
            BackgroundColor="Transparent"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="Center"
            Padding="0,0,0,10">


                <Frame
                Padding="20,30,20,20"
                Grid.Row="0"
                Margin="0,25,0,0"
                    BorderColor="Transparent"
                CornerRadius="6"
                HasShadow="False"
                IsClippedToBounds="True"
                HorizontalOptions="FillAndExpand">
                    <StackLayout
                    Spacing="10">

                        <t:DrMuscleButton
                        x:Name="ReviewButton"
                        VerticalOptions="EndAndExpand"
                        Text="Rate 5 stars"
                        Style="{StaticResource buttonStyle}"
                        Clicked="ReviewButton_Clicked"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="60"
                        CornerRadius="0" />
                        <Button
                        Text="Share free trial"
                        x:Name="MonthFree"
                        Clicked="MonthFree_Clicked"
                        Style="{StaticResource buttonStyle}"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="60"
                        CornerRadius="0" />
                        <Button
                        Text="4 months free with annual plan"
                        x:Name="Upgrade"
                        Clicked="Upgrade_Clicked"
                        Style="{StaticResource buttonStyle}"
                        HorizontalOptions="FillAndExpand"
                        Margin="0,0"
                        HeightRequest="60"
                        CornerRadius="0" />

                    </StackLayout>
                </Frame>
                <Image Grid.Row="0" Source="heartIcon.png" WidthRequest="50" HeightRequest="50" HorizontalOptions="Center" VerticalOptions="Start" />
            </Grid>
        </Frame>
    </toolkit:PopupPage.Content>
</toolkit:PopupPage>
