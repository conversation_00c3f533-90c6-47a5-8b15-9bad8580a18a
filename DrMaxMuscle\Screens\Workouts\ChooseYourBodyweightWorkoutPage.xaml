﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.ChooseYourBodyweightWorkoutPage"
             xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"   
             Title="ChooseYourBodyweightWorkoutPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <AbsoluteLayout>
        <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,20,20,20" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
            <StackLayout VerticalOptions="FillAndExpand">
                <t:DrMuscleListView
                ios:ListView.SeparatorStyle="FullWidth"
                ios:ListView.GroupHeaderStyle="Grouped"  
                BackgroundColor="Transparent"
                x:Name="ExpandableList"
                SeparatorColor="#264457" 
                SeparatorVisibility="Default"
        ItemsSource="{Binding ExeList}"
        IsGroupingEnabled="True">
            <t:DrMuscleListView.ItemTemplate>
                <DataTemplate>
                    <ViewCell Height="45">
                  <StackLayout Orientation="Horizontal" BackgroundColor="Transparent" Padding="0,0,0,0">
                        <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}" ></Label>
                        <StackLayout Orientation="Horizontal" HorizontalOptions="End">
                            <t:DrMuscleButton Clicked="OnCancelClicked" Text="{Binding [Cancel].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextCancelButton}"  />
                            <t:DrMuscleButton Clicked="OnReset" Text="{Binding [More].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}" CommandParameter="{Binding .}" IsVisible="false" HorizontalOptions="End" Style="{StaticResource ItemContextResetButton}" />             
                            <t:DrMuscleButton Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" HorizontalOptions="End" IsVisible="{Binding IsSystemExercise}"  Style="{StaticResource ItemContextMoreButton}" />
                        </StackLayout>
                  </StackLayout>
                </ViewCell>
                </DataTemplate>
            </t:DrMuscleListView.ItemTemplate>
            <t:DrMuscleListView.GroupHeaderTemplate>
                <DataTemplate>
                    <ViewCell ios:Cell.DefaultBackgroundColor="Transparent">
                                <StackLayout Orientation="Vertical" BackgroundColor="Transparent" Padding="0">
                                    <StackLayout Orientation="Horizontal" Padding="0,10,0,10" HorizontalOptions="FillAndExpand" BackgroundColor="Transparent">
                                        <Label Text="{Binding Name}"
                                               BackgroundColor="Transparent"
                                               TextColor="{x:Static constnats:AppThemeConstants.OffBlackColor}"
                                               HorizontalOptions="StartAndExpand"
                                               VerticalOptions="Center"
                                               VerticalTextAlignment="Center" Style="{StaticResource BoldLabelStyle}" />
                                        <Image x:Name="StateImage" 
                                               PropertyChanged="StateImage_PropertyChanged"
                                               HorizontalOptions="End"
                                               VerticalOptions="CenterAndExpand"
                                               Source="{Binding StateIcon}"
                                               WidthRequest="25" 
                                               HeightRequest="32"
                                               Aspect="AspectFit" 
                                               BackgroundColor="Transparent"/>
                                        <StackLayout.GestureRecognizers>
                                            <TapGestureRecognizer  NumberOfTapsRequired="1" CommandParameter="{Binding .}" Tapped="Section_Tapped"/>
                                        </StackLayout.GestureRecognizers>
                                    </StackLayout>
                                    <BoxView HeightRequest="1"
                                             HorizontalOptions="FillAndExpand"
                                             VerticalOptions="End"
                                             BackgroundColor="#264457">
                                    </BoxView>
                                </StackLayout>
                            </ViewCell>
                </DataTemplate>
            </t:DrMuscleListView.GroupHeaderTemplate>
        </t:DrMuscleListView>
                
            </StackLayout>
        </StackLayout>
    </AbsoluteLayout>
    </ContentPage>

