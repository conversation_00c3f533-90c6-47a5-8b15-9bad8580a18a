﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.OnBoarding.Page3"
             Shell.NavBarIsVisible="false"
             NavigationPage.HasNavigationBar="False"
             >
    <Grid>
        <Image Source="page3" Aspect="AspectFill" />
        <StackLayout HorizontalOptions="Center" x:Name="mainView" Margin="{OnPlatform Android= '0,100,0,50',iOS='0,150,0,50'}">
            <Image HeightRequest="130" WidthRequest="150" Source="logo1.png" x:Name="ImgLogo" />

            <StackLayout HorizontalOptions="CenterAndExpand" Margin="0,10,0,0">

                <Label TextColor="White" Margin="25,0" x:Name="LblTitle" Text="Your workouts optimized&#10;in real time, on autopilot" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center" Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" >
                    
                </Label>
                <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                    <Label TextColor="LightGray" x:Name="LblLine1" Text="• RPE, DUP &amp; more"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    <Label TextColor="LightGray" x:Name="LblLine2" Text="• Get in shape faster"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    <Label TextColor="LightGray" x:Name="LblLine3" Text="• Build muscle &amp; burn fat" Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                </StackLayout>
                <StackLayout Margin="0,40" x:Name="LeavesStack" HorizontalOptions="Center" VerticalOptions="FillAndExpand" Orientation="Horizontal">
                    <Image x:Name="ImgLeave1" WidthRequest="45" Source="leaves.png"  Margin="{OnPlatform iOS='0'}" VerticalOptions="{OnPlatform iOS='Start'}" HeightRequest="{OnPlatform iOS='100'}"/>
                    <StackLayout Margin="0,16,0,0">
                        <Label TextColor="White" x:Name="LblSubHeading" Text="Optimizes workouts&#10;with cutting-edge tech" HorizontalOptions="Center" HorizontalTextAlignment="Center" VerticalOptions="Center"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                        <Label TextColor="LightGray" Text="-The Burn-In" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="13"  x:Name="LblAuthor1" VerticalOptions="Center"  HorizontalTextAlignment="Center" />
                    </StackLayout>
                    <Image x:Name="ImgLeave2" WidthRequest="45"  Source="leaves2.png" Margin="{OnPlatform iOS='0'}" VerticalOptions="{OnPlatform iOS='Start'}" HeightRequest="{OnPlatform iOS='100'}"/>
                </StackLayout>
            </StackLayout>

        </StackLayout>
    </Grid>
</ContentView>