using Acr.UserDialogs;
using RGPopup.Maui.Pages;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui.Core;

namespace DrMaxMuscle.Views;

public partial class GeneralPopup : Popup
{
    TapGestureRecognizer okGuesture;
    string buttonText = "";
    public event EventHandler OkButtonPress;
    public bool _isHide { get; set; }
    bool IsDataLoaded = false;
    bool IsChangesToMealPlanLoaded = false;
    bool _isMealPlanLoading = false;
    bool isFirstLayoutClicked = false;
    bool isReviewTips = false;
    string _totalMacros = "";
    string _title = "";
    int count = 0;
    public GeneralPopup(string image, string title, string subtitle, string buttonText, Thickness? thickness = null, bool isTips = false, bool isSummary = false, string isShowLearnMore = "false", string isShowSettings = "false", string ismealPlan = "false", string isNotNow = "false", string isAutoHide = "false", string isNewFeature = "false", string isNotYet = "false", bool isChatLoading = false, bool isMealPlanLoading = false, string totalMacros = "", string others = "")
    {
        InitializeComponent();
        try
        {

            //var displayInfo = DeviceDisplay.MainDisplayInfo;
            //var width = displayInfo.Width / displayInfo.Density;
            //mainStack.WidthRequest = width;

            var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
            mainStack.WidthRequest = screenWidth * 0.9;

            okGuesture = new TapGestureRecognizer();
            okGuesture.Tapped += OkButton_Clicked;
            OkAction.GestureRecognizers.Add(okGuesture);
            ContinueSkipBtnLayout.IsVisible = false;
            ImgName.Source = image;
            if (thickness != null)
                ImgName.Margin = (Thickness)thickness;
            LblHeading.Text = title;
            LblSubHead.Text = subtitle.Trim();
            _title = title;
            OkButton.Text = buttonText;
            this.buttonText = buttonText;
            LblTipText.IsVisible = false;
            OkAction.Margin = new Thickness(25,10,25,0);
            // uncomment code please
            //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
            if (isShowLearnMore == "true")
            {
                BtnLearnMore.IsVisible = false;
                // LblSubHead.Margin = new Thickness(15, 0, 15, 5);

            }
            else if (isShowSettings == "true")
            {
                BtnLearnMore.IsVisible = true;
                // LblSubHead.Margin = new Thickness(15, 0, 15, 5);
                BtnLearnMore.Text = "Open Settings";

            }
            else if (ismealPlan == "true")
            {
                BtnCancel.IsVisible = true;
                LblSubHead.Margin = new Thickness(15);
                OkAction.Margin = new Thickness(25,5,25,0);
                BtnLearnMore.IsVisible = false;
            }
            else if (isMealPlanLoading == true)
            {
                _isMealPlanLoading = isMealPlanLoading;
                isFirstLayoutClicked = false;
                BtnCancel.IsVisible = false;
                BtnLearnMore.IsVisible = false;
                OkButton.IsVisible = true;
                OkButton.Text = buttonText;
                OkAction.IsVisible = true;
                // uncomment code please
                //MyParticleCanvas.IsActive = true;
                //MyParticleCanvas.IsRunning = true;
                isReviewTips = false;
                if (buttonText == "Finalizing...")
                {
                    LblCountDown.IsVisible = true;
                    _totalMacros = totalMacros;
                    try
                    {

                        MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "FinalizeMealPlan");
                        MessagingCenter.Subscribe<Message.GeneralMessage>(this, "FinalizeMealPlan", async (obj) =>
                        {
                            if (obj.GeneralText == "Loaded")
                            {
                                IsDataLoaded = true;
                                //if (PopupNavigation.Instance.PopupStack.Count > 0)
                                //    this.Close();
                            }

                        });
                        UpdateButtonText();

                    }
                    catch (Exception ex)
                    {

                    }
                }
                else if (buttonText == "Review tips")
                {
                    _totalMacros = totalMacros;
                    isReviewTips = true;
                    OkButton.Text = "Continue";
                    OkAction.IsVisible = false;
                    stackOne.Padding = new Thickness(0, 0, 0, 0);
                    ContinueSkipBtnLayout.IsVisible = true;
                    // uncomment code please
                    //if (MyParticleCanvas != null)
                    //{
                    //    MyParticleCanvas.IsActive = false;
                    //    MyParticleCanvas.IsRunning = false;
                    //    MyParticleCanvas = null;
                    //}
                    
                    ContinueReviewBtn_Clicked(OkAction, EventArgs.Empty);
                }
                else if (others == "Change Result")
                {
                    MessagingCenter.Unsubscribe<Message.GeneralMessage>(this, "ChangesToMealPlan");
                    MessagingCenter.Subscribe<Message.GeneralMessage>(this, "ChangesToMealPlan", async (obj) =>
                    {
                        if (obj.GeneralText == "Loaded")
                        {
                            IsChangesToMealPlanLoaded = true;
                            //if (PopupNavigation.Instance.PopupStack.Count > 0)
                            //    this.Close();
                        }

                    });
                    UpdateButtonTextForGPT4();
                }
                else
                    SetTimerHidePopupForMeal();
            }
            else if (isNotNow == "true")
            {
                BtnCancel.IsVisible = true;
                BtnLearnMore.IsVisible = false;
                BtnCancel.Text = "Not now";
            }
            else if (isNotYet == "true")
            {
                BtnCancel.IsVisible = true;
                BtnLearnMore.IsVisible = false;
                BtnCancel.Text = "Not yet";
            }
            else if (isNewFeature == "true")
            {
                BtnCancel.IsVisible = false;
                BtnLearnMore.IsVisible = false;
                BtnCancel.Text = "Learn more";
                LblSubHead.HorizontalTextAlignment = TextAlignment.Start;
            }
            else
            {
                BtnLearnMore.IsVisible = false;
            }

            if (isTips)
            {
                // LblSubHead.Margin = new Thickness(15, 0, 15, 5);
                LblCountDown.IsVisible = true;
                if (buttonText == "Continue")
                {
                    SetLoadingWorkout(title);
                    //OkAction?.GestureRecognizers?.Add(okGuesture);
                }
                else
                {
                    SetLoading(title);
                    SetTimerHidePopup();
                }
                OkAction.IsVisible = false;
                LblCountDown.IsVisible = true;
                if(isShowSettings == "false"){
                    OkAction.Margin = new Thickness(25,25,25,0);
                }
                
                MessagingCenter.Subscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage", (obj) =>
                {
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        LblTipText.IsVisible = false;
                        OkAction.IsVisible = true;
                        LblCountDown.IsVisible = true;
                    });
                });
            }
            if (isChatLoading)
            {
                SetLoadingChat(title);
                SetTimerHidePopup();
                OkAction.GestureRecognizers.Remove(okGuesture);
                //OkButton.Text = "Customizing workout...";
                OkAction.IsVisible = false;
            }
            if (isSummary)
            {

                OkAction?.GestureRecognizers?.Remove(okGuesture);
                SetLoadingSummary(okGuesture);
            }
            if (isAutoHide == "true")
            {


            }

            this.Closed += Popup_Closed;
        }
        catch (Exception ex)
        {

        }
    }

    private async Task UpdateButtonText()
    {
        OkButton.Text = "Learn more";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 5:00";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                int firstOverlaySeconds = 10; // 5 minutes
                for (int seconds = firstOverlaySeconds; seconds >= 0; seconds--)
                {
                    if (isFirstLayoutClicked)
                        break;
                    int minutes = 0;
                    int remainingSeconds = seconds % 60;

                    MainThread.BeginInvokeOnMainThread(() => {
                        LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                        //LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {remainingSeconds:00}";
                    });

                    await Task.Delay(1000);
                    if (seconds == 0)
                    {
                        OkButton_Clicked(OkAction, EventArgs.Empty);
                        break;
                    }
                }
                if (isFirstLayoutClicked) // Only proceed to the second loop if not already terminated
                {
                    int totalSeconds = 5 * 60; // 5 minutes
                    for (int seconds = totalSeconds; seconds >= 0; seconds--)
                    {
                        int minutes = seconds / 60;
                        int remainingSeconds = seconds % 60;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                            //LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {remainingSeconds:00}";
                        });
                        if (IsDataLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
                        {
                            var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                            if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                            {
                                if (this.Handler != null)
                                    await this.CloseAsync();
                                //await MauiProgram.SafeDismissTopPopup();
                                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                                //   await PopupNavigation.Instance.PopAsync();
                            }
                            break;
                        }

                        await Task.Delay(1000);
                    }
                }
                MainThread.BeginInvokeOnMainThread(async () => {
                    LblCountDown.Text = $" ";
                    LblCountDown.IsVisible = false;
                    while (!IsDataLoaded)
                    {
                        OkButton.Text = "Finalizing.";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing..";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing...";
                        await Task.Delay(500);
                    }
                    if (PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            if (this.Handler != null)
                                await this.CloseAsync();
                            //await MauiProgram.SafeDismissTopPopup();
                            //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                            //   await PopupNavigation.Instance.PopAsync();
                        }
                    }
                });

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                int firstOverlaySeconds = 10; // 5 minutes
                for (int seconds = firstOverlaySeconds; seconds >= 0; seconds--)
                {
                    if (isFirstLayoutClicked)
                        break;
                    int minutes = 0;
                    int remainingSeconds = seconds % 60;
                    LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                    //LblCountDown.Text = $"{closingText} {minutes:00}:{remainingSeconds:00}";

                    await Task.Delay(1000);
                    if (seconds == 0)
                    {
                        OkButton_Clicked(OkAction, EventArgs.Empty);
                        break;
                    }
                }
                if (isFirstLayoutClicked) // Only proceed to the second loop if not already terminated
                {
                    int totalSeconds = 5 * 60; // 5 minutes
                    for (int seconds = totalSeconds; seconds >= 0; seconds--)
                    {
                        int minutes = seconds / 60;
                        int remainingSeconds = seconds % 60;
                        LblCountDown.Text = (minutes > 0) ? $"{closingText} {minutes:0}:{remainingSeconds:00}" : $"{closingText} {(remainingSeconds > 9 ? remainingSeconds.ToString("00") : remainingSeconds.ToString())}";
                        //LblCountDown.Text = $"{closingText} {minutes:00}:{remainingSeconds:00}";
                        if (IsDataLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
                        {
                            var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                            if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                            {
                                if (this.Handler != null)
                                    await this.CloseAsync();
                                //await MauiProgram.SafeDismissTopPopup();
                                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                                //   await PopupNavigation.Instance.PopAsync();
                            }
                        }

                        await Task.Delay(1000);
                    }
                }
                LblCountDown.Text = $" ";
                LblCountDown.IsVisible = false;
                while (!IsDataLoaded)
                {

                    OkButton.Text = "Finalizing.";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing..";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing...";
                    await Task.Delay(500);
                }

                if (PopupNavigation.Instance.PopupStack.Count > 0)
                {
                    var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                    if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                    {
                        if (this.Handler != null)
                            await this.CloseAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                        //   await PopupNavigation.Instance.PopAsync();
                    }
                }
            });
        }
        
    }
    private async Task UpdateButtonTextForGPT4()
    {
        OkButton.Text = "Continue";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 30";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                int totalSeconds = 30; // 5 minutes
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (IsChangesToMealPlanLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if ( topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            if (this.Handler != null)
                                await this.CloseAsync();
                            //await MauiProgram.SafeDismissTopPopup();
                            //await PopupNavigation.Instance.PopAsync();
                        }
                        break;
                    }

                    await Task.Delay(1000);
                }
                MainThread.BeginInvokeOnMainThread(async () => {
                    LblCountDown.Text = $" ";
                    LblCountDown.IsVisible = false;
                    while (!IsChangesToMealPlanLoaded)
                    {
                        OkButton.Text = "Finalizing.";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing..";
                        await Task.Delay(500);
                        OkButton.Text = "Finalizing...";
                        await Task.Delay(500);
                    }
                    if (PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            if (this.Handler != null)
                                await this.CloseAsync();
                            //await MauiProgram.SafeDismissTopPopup();
                            //await PopupNavigation.Instance.PopAsync();
                        }
                    }
                });

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                int totalSeconds = 30; // 5 minutes
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    LblCountDown.Text = $"{closingText} {seconds}";
                    if (IsChangesToMealPlanLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                        if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                        {
                            if (this.Handler != null)
                                await this.CloseAsync();
                            //await MauiProgram.SafeDismissTopPopup();
                            //await PopupNavigation.Instance.PopAsync();
                        }
                    }

                    await Task.Delay(1000);
                }
                LblCountDown.Text = $" ";
                LblCountDown.IsVisible = false;
                while (!IsChangesToMealPlanLoaded)
                {

                    OkButton.Text = "Finalizing.";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing..";
                    await Task.Delay(500);
                    OkButton.Text = "Finalizing...";
                    await Task.Delay(500);
                }

                if (PopupNavigation.Instance.PopupStack.Count > 0)
                {
                    var topPopup = PopupNavigation.Instance.PopupStack.LastOrDefault();
                    if (topPopup != null && topPopup.GetType() == typeof(GeneralPopup))
                    {
                        if (this.Handler != null)
                            await this.CloseAsync();
                        //MauiProgram.SafeDismissTopPopup();
                        //PopupNavigation.Instance.PopAsync();
                    }
                }
            });
        }
    }
    
    //protected override Task OnClosed(object result, bool wasDismissedByTappingOutsideOfPopup)
    //{
    //    _isHide = true;
    //    MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

    //    try
    //    {
    //        //if (MyParticleCanvas != null)
    //        //{
    //        //    MyParticleCanvas.IsActive = false;
    //        //    MyParticleCanvas.IsRunning = false;
    //        //    MyParticleCanvas = null;
    //        //}

    //    }
    //    catch (Exception ex)
    //    {

    //    }
    //    return base.OnClosed(result, wasDismissedByTappingOutsideOfPopup);
    //}
    // uncomment code please
    public void Popup_Closed(object sender, PopupClosedEventArgs e)
    {
        //base.OnDisappearing();
        _isHide = true;
        MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");

        try
        {
            //if (MyParticleCanvas != null)
            //{
            //    MyParticleCanvas.IsActive = false;
            //    MyParticleCanvas.IsRunning = false;
            //    MyParticleCanvas = null;
            //}

        }
        catch (Exception ex)
        {

        }
    }
    private async Task SetLoadingSummary(TapGestureRecognizer okGuesture)
    {
        await Task.Delay(250);

        OkButton.Text = "Loading.";

        await Task.Delay(700);
        OkButton.Text = "Loading..";

        await Task.Delay(700);

        OkButton.Text = "Loading...";
        await Task.Delay(700);
        OkButton.Text = this.buttonText;
        OkAction.GestureRecognizers.Add(okGuesture);

    }

    private async void SetLoadingChat(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            OkAction.IsVisible = false;

            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading chat...";
                });


                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading messages...";
                });
                await Task.Delay(750);
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    LblTipText.IsVisible = false;
                    OkAction.IsVisible = true;

                    //OkButton.Text = "Start workout";
                    //OkButton.Clicked += OkButton_Clicked;
                    OkAction.GestureRecognizers.Add(okGuesture);
                });

            });
        }
        else
        {
            LblTipText.IsVisible = true;

            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading chat...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading messages...";
                await Task.Delay(750);
                LblTipText.IsVisible = false;
                OkAction.IsVisible = true;

                //OkButton.Text = "Start workout";
                //OkButton.Clicked += OkButton_Clicked;
                OkAction.GestureRecognizers.Add(okGuesture);
            });
        }

    }

    private async void SetLoadingWorkout(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;
        try
        {

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                LblTipText.Text = "";
                LblTipText.IsVisible = true;
                await Task.Factory.StartNew(async () =>
                {

                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading equipment...";
                    });


                    await Task.Delay(1500);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading profile...";
                    });
                    await Task.Delay(1400);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {

                        LblTipText.Text = "Loading program...";
                    });
                    await Task.Delay(1300);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading calendar...";
                    });
                    await Task.Delay(1200);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading custom tips";
                    });
                    await Task.Delay(1100);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading progression...";
                    });
                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading progression...";
                    });

                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading new records...";
                    });

                    await Task.Delay(900);
                    if (LblTipText.Text == " ")
                        return;
                    MainThread.BeginInvokeOnMainThread(() => {
                        LblTipText.Text = "Loading a big pump...";
                    });
                    await Task.Delay(800);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblTipText.IsVisible = false;
                        OkAction.IsVisible = true;


                        //OkAction?.GestureRecognizers?.Add(okGuesture);
                        SetTimerForContinue();
                    });
                });
            }
            else
            {
                LblTipText.IsVisible = true;

                //ImgLoader.IsVisible = true;
                Device.BeginInvokeOnMainThread(async () =>
                {

                    LblTipText.Text = "Loading equipment...";


                    await Task.Delay(1500);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading profile...";
                    await Task.Delay(1400);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading program...";
                    await Task.Delay(1300);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading calendar...";
                    await Task.Delay(1200);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading custom tips";
                    await Task.Delay(1100);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading progression...";
                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;
                    LblTipText.Text = "Loading progression...";


                    await Task.Delay(1000);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading new records...";


                    await Task.Delay(900);
                    if (LblTipText.Text == " ")
                        return;

                    LblTipText.Text = "Loading a big pump...";

                    await Task.Delay(800);
                    LblTipText.IsVisible = false;
                    OkAction.IsVisible = true;



                    SetTimerForContinue();
                });
            }

        }
        catch (Exception ex)
        {

        }
    }

    private async void SetLoading(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            LblCountDown.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading sets...";
                });


                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading reps...";
                });
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading weights...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading a big pump...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Let's go!";
                });
            });
        }
        else
        {
            LblTipText.IsVisible = true;
            LblCountDown.IsVisible = true;
            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading sets...";
                await Task.Delay(700);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading reps...";
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading weights...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading a big pump...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Let's go!";

            });
        }
    }

    private async void SetTimerHidePopup()
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;
        await Task.Delay(1000);
        OkButton.Text = $"{this.buttonText}";
        var closingText = "Closing";
        LblCountDown.Text = $"{closingText} 5";
        LblCountDown.IsVisible = true;
        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = false;
            OkButton.IsVisible = true;
            OkAction.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {
                    LblCountDown.Text = $"{closingText} 4";
                });


                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 3";
                });

                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 2";
                });
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {

                    LblCountDown.Text = $"{closingText} 1";
                });
                await Task.Delay(1000);
                MainThread.BeginInvokeOnMainThread(() => {
                    LblCountDown.Text = $" ";
                });

                if (this.Handler != null)
                    await this.CloseAsync();
                //await MauiProgram.SafeDismissTopPopup();
                //if (PopupNavigation.Instance.PopupStack.Count > 0 && !_isHide)
                //    PopupNavigation.Instance.PopAsync();
            });
        }
        else
        {
            LblTipText.IsVisible = true;
            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblCountDown.Text = $"{closingText} 5";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 4";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 3";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 2";
                await Task.Delay(1000);
                LblCountDown.Text = $"{closingText} 1";
                await Task.Delay(1000);
                LblCountDown.Text = $" ";
                try
                {
                    if (this.Handler != null)
                        await this.CloseAsync();
                    //if (PopupNavigation.Instance.PopupStack?.Count > 0 && !_isHide)
                    //    PopupNavigation.Instance.PopAsync();
                }
                catch (ObjectDisposedException ex)
                {

                }
                catch (InvalidOperationException ex)
                {
                }
                catch (Exception ex)
                {

                }

            });
        }
    }
    private async void SetTimerHidePopupForMeal()
    {
        await Task.Delay(1300);
        var closingText = "Closing";
        LblCountDown.IsVisible = true;
        int totalSeconds = 20;
        LblCountDown.Text = $"{closingText} {totalSeconds}";

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            await Task.Factory.StartNew(async () =>
            {
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (App.IsMealReponseLoaded)
                    {
                        //if (PopupNavigation.Instance.PopupStack.Count > 0)
                        //    PopupNavigation.Instance.PopAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        if (this.Handler != null)
                            await this.CloseAsync();
                        break;
                    }
                    await Task.Delay(1000);
                    if (seconds == 0 && PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        if (this.Handler != null)
                            await this.CloseAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        //PopupNavigation.Instance.PopAsync();
                    }
                }

            });
        }
        else
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                for (int seconds = totalSeconds; seconds >= 0; seconds--)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        LblCountDown.Text = $"{closingText} {seconds}";
                    });
                    if (App.IsMealReponseLoaded)
                    {
                        if (this.Handler != null)
                            await this.CloseAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        //if (PopupNavigation.Instance.PopupStack.Count > 0)
                        //    PopupNavigation.Instance.PopAsync();
                        break;
                    }
                    await Task.Delay(1000);
                    if (seconds == 0 && PopupNavigation.Instance.PopupStack.Count > 0)
                    {
                        if (this.Handler != null)
                            await this.CloseAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        //PopupNavigation.Instance.PopAsync();
                    }
                }

            });
        }
    }
    public async void CheckMealLoaded()
    {
        try
        {
            if (this.Handler != null)
                await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (App.IsMealReponseLoaded && PopupNavigation.Instance.PopupStack.Count > 0)
            //    PopupNavigation.Instance.PopAsync();
        }
        catch (Exception ex)
        {

        }
    }
    private async void SetTimerForContinue()
    {

        try
        {

            OkButton.Text = $"{this.buttonText} 5";

            if (Device.RuntimePlatform.Equals(Device.Android))
            {
                LblTipText.Text = "";
                LblTipText.IsVisible = false;
                OkButton.IsVisible = true;
                OkAction.IsVisible = true;
                await Task.Factory.StartNew(async () =>
                {
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        OkButton.Text = $"{this.buttonText} 4";
                    });


                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 3";
                    });

                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 2";
                    });
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText} 1";
                    });
                    await Task.Delay(1000);
                    MainThread.BeginInvokeOnMainThread(() =>
                    {

                        OkButton.Text = $"{this.buttonText}";
                    });
                });
            }
            else
            {
                LblTipText.IsVisible = false;

                //ImgLoader.IsVisible = true;
                Device.BeginInvokeOnMainThread(async () =>
                {

                    OkButton.Text = $"{this.buttonText} 5";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 4";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 3";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 2";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText} 1";
                    await Task.Delay(1000);
                    OkButton.Text = $"{this.buttonText}";

                });


            }
        }
        catch (Exception ex)
        {

        }

    }

    void OkButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isMealPlanLoading && !string.IsNullOrEmpty(_totalMacros))
        {
            // uncomment code please
            //if (MyParticleCanvas != null)
            //{
            //    MyParticleCanvas.IsActive = false;
            //    MyParticleCanvas.IsRunning = false;
            //    MyParticleCanvas = null;
            //}
            LocalDBManager.Instance.SetDBSetting("totalMacros", _totalMacros);
            var splitedData = _totalMacros.Split(',');
            if (!App.IsProteinShakeExist && count == 7)
            {
                count++;
            }
            switch (count)
            {
                case 0:
                    SetLabelText("Free drinks", "", "You are free to drink coffee and tea, as long as they contain zero calories. Healthy in moderation, and may even help with fat loss.");
                    isFirstLayoutClicked = true;
                    break;
                case 1:
                    SetLabelText("2 cups water", "", "Essential for peak performance. Keeps your body running smoothly, energy high, and hunger low. Make drinking two cups with each meal non-negotiable.");
                    break;
                case 2:
                    SetLabelText(splitedData[0], "g protein", "Build muscle faster. Repair damage after you lift. Fuel the growth of stronger, larger muscles. Stave off hunger, and sculpt your physique.");
                    break;
                case 3:
                    SetLabelText("Protein timing", "", "Eat within 30 min of working out to jump-start muscle repair. Spread your intake across the day to fuel muscle growth.");
                    break;
                case 4:
                    SetLabelText(splitedData[1], "g carbs", "Your ally for powering through intense workouts. Carbs fuel your muscles and brain and help you push harder to achieve that lean, formidable frame.");
                    break;
                case 5:
                    SetLabelText(splitedData[2], "g fat", "Your secret weapon for hormonal balance and sustained energy. Helps produce testosterone, absorb vital nutrients, and support your metabolism.");
                    break;
                case 6:
                    SetLabelText("Meal timing", "", "Time your meals to optimize absorption and energy. Eat before and after your workouts and consider a protein-rich snack or shake just before bed for maximum benefit.");
                    break;
                case 7:
                    SetLabelText("Protein shake", "", "Drink before or after your workouts to maximize fat loss and muscle gain. On rest days, have it before going to bed, or whenever convenient.");
                    break;
                case 8:
                    SetLabelText("Snack smarter", "", "Easy, portable snacks keep your energy up and cravings down. Don't let long gaps between meals derail your progress. Maintain muscle, manage hunger.");
                    break;
                case 9:
                    SetLabelText("Stay flexible", "", "If you miss a meal or snack, don't stress. Instead, eat it later. Focus on maintaining your overall daily totals rather than perfect meal timing.");
                    break;
                case 10:
                    SetLabelText("Eating out", "", "Choose wisely. Opt for dishes with lean proteins and vegetables. Request sauces on the side to control calories. Drink plenty of water.");
                    break;
                case 11:
                    SetLabelText("Alcohol", "", "Impacts recovery and fat loss. One drink is okay, but too much can undo your efforts. Choose lighter options and keep your goals in sight.");
                    break;
                case 12:
                    SetLabelText("Supplementation", "", "Supplements can fill nutritional gaps, but they're not a silver bullet. Focus on whole foods first, supplements second. Creatine is a popular option.");
                    break;
                case 13:
                    SetLabelText("Creatine: proven power", "", "One of the most researched supplements for over 30 years. Boosts strength, muscle growth, and recovery. Try 3-5 g a day for best results. Safe, effective, and time-tested.");
                    break;
                case 14:
                    SetLabelText("Vitamin D", "", "Essential for muscle, bone, and hormonal health. Boosts performance and recovery. Ensure adequate intake through sunlight, food, or supplements.");
                    break;
                case 15:
                    SetLabelText("Prep smart", "", "Meal prep can save you time and keep you on track. Prepare meals in bulk for easy, grab-and-go eating. Set yourself up for success, make Sunday your meal prep day.");
                    break;
                case 16:
                    SetLabelText("Feedback loop", "", "Regularly assess how your meal plan is affecting your energy, performance, and body composition. Log your weight often. Update your plan when progress stalls.");
                    break;
                default:
                    OkButton.Text = isReviewTips ? "Continue" : "Finalizing...";
                    LblHeading.Text = isReviewTips ? "" : _title;
                    LblSubHead.Text = isReviewTips ? "" : "Your plan is almost ready.";
                    var finalMealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlan")?.Value;
                    if (IsDataLoaded || isReviewTips || !string.IsNullOrEmpty(finalMealPlan))
                    {
                        this.Close();
                         //MauiProgram.SafeDismissTopPopup();
                        //if (PopupNavigation.Instance.PopupStack.Count() > 0) 
                        //    PopupNavigation.Instance.PopAsync(); 
                    }
                    break;
            }
            count++;
        }
        else
        {
            try
            {
                this.Close();
                 //MauiProgram.SafeDismissTopPopup();
                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //    PopupNavigation.Instance.PopAsync();
            }
            catch (Exception ex)
            {

            }

            if (BtnCancel.IsVisible)
            {
                if (OkButtonPress != null)
                    OkButtonPress.Invoke(sender, EventArgs.Empty);
            }
        }


    }
    void SetLabelText(string heading, string unit, string subHead)
    {
        try
        {
            Device.BeginInvokeOnMainThread(() =>
            {
                OkButton.Text = "Continue";
                LblHeading.Text = heading + " " + unit;
                LblSubHead.Text = subHead;
            });
        }
        catch (Exception ex)
        {

        }
    }
    async void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (LblHeading.Text.Equals("New features!"))
            {
                await Browser.OpenAsync("https://dr-muscle.com/timeline", BrowserLaunchMode.SystemPreferred);
                //Device.OpenUri(new Uri("https://dr-muscle.com/timeline"));
            }
            else if (BtnLearnMore.Text.Equals("Open Settings"))
            {
                if (this.Handler != null)
                    await this.CloseAsync();
                //MauiProgram.SafeDismissTopPopup();
                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //    PopupNavigation.Instance.PopAsync();
                SettingsPage page = new SettingsPage();
                await Application.Current.MainPage.Navigation.PushAsync(page);
                //PagesFactory.PushAsync<SettingsPage>();
            }
            else if (BtnLearnMore.Text.Equals("Cancel"))
            {
                if (this.Handler != null)
                    await this.CloseAsync();
                //MauiProgram.SafeDismissTopPopup();
                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //    PopupNavigation.Instance.PopAsync();
            }
            else
            {
                if (await CheckTrialUserAsync())
                    return;

                if (this.Handler != null)
                    await this.CloseAsync();
                //MauiProgram.SafeDismissTopPopup();
                //if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //    PopupNavigation.Instance.PopAsync();
                LearnPage page = new LearnPage();
                await Application.Current.MainPage.Navigation.PushAsync(page);
                //PagesFactory.PushAsync<LearnPage>();
            }
        }
        catch (System.Exception ex)
        {

        }
    }



    private async Task<bool> CheckTrialUserAsync()
    {
        try
        {

        if (App.IsFreePlan)
        {
            var ShowWelcomePopUp2 = await HelperClass.DisplayCustomPopup("You discovered a premium feature!","Upgrading will unlock custom coaching tips based on your goals and progression.",
            "Upgrade","Maybe later");
            ShowWelcomePopUp2.ActionSelected += async (sender,action) =>{
                if(action == PopupAction.OK){

                    if (this.Handler != null)
                        await this.CloseAsync();
                    //await MauiProgram.SafeDismissTopPopup();
                    SubscriptionPage page = new SubscriptionPage();
                        page.OnBeforeShow();
                        await Application.Current.MainPage.Navigation.PushAsync(page);
                }
            };

                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                //     Title = "You discovered a premium feature!",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             if (PopupNavigation.Instance.PopupStack.Count() > 0)
                //                await PopupNavigation.Instance.PopAsync();
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             await Application.Current.MainPage.Navigation.PushAsync(page);
                //             // PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //         else
                //         {

                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
        }
        catch (Exception ex)
        {

        }
        return App.IsFreePlan;
    }

    async void DrMuscleButtonCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BtnCancel.Text == "Learn more")
        {
            Browser.OpenAsync("https://dr-muscle.com/timeline", BrowserLaunchMode.SystemPreferred);
            //Device.OpenUri(new Uri("https://dr-muscle.com/timeline"));
        }
        else
        {
            if (this.Handler != null)
                await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
        }
    }

    private async void SkipReviewBtn_Clicked(object sender, EventArgs e)
    {
        // myProgressBar.Progress -= 0.058;
        try
        {
            if (this.Handler != null)
                await this.CloseAsync();
            //await MauiProgram.SafeDismissTopPopup();
        }
        catch (Exception ex)
        {

        }
    }

    private async void ContinueReviewBtn_Clicked(object sender, EventArgs e)
    {
        try
        {
            LocalDBManager.Instance.SetDBSetting("totalMacros", _totalMacros);
            var splitedData = _totalMacros.Split(',');
            if (!App.IsProteinShakeExist && count == 7)
            {
                count++;
            }

            myProgressBar.Progress += 0.0625;

            // Ensure the progress value stays between 0 and 1
            if (myProgressBar.Progress > 1)
            {
                myProgressBar.Progress = 1;
            }

            switch (count)
            {
                case 0:
                    //SetLabelText("Free drinks", "", "You are free to drink coffee and tea, as long as they contain zero calories. Healthy in moderation, and may even help with fat loss.");
                    SetLabelText("Free drinks", "", "You are free to drink\ncoffee and tea, as long\nas they contain zero calories.\nHealthy in moderation,\nand may even help with fat loss.");
                    isFirstLayoutClicked = true;
                    break;
                case 1:
                    //SetLabelText("2 cups water", "", "Essential for peak performance. Keeps your body running smoothly, energy high, and hunger low. Make drinking two cups with each meal non-negotiable.");
                    SetLabelText("2 cups water", "", "Essential for peak performance.\nKeeps your body running smoothly,\nenergy high, and hunger low.\nMake drinking two cups\nwith each meal non-negotiable.");
                    break;
                case 2:
                    //SetLabelText(splitedData[0], "g protein", "Build muscle faster. Repair damage after you lift. Fuel the growth of stronger, larger muscles. Stave off hunger, and sculpt your physique.");
                    SetLabelText(splitedData[0], "g protein", "Build muscle faster.\nRepair damage after you lift.\nFuel the growth of stronger,\nlarger muscles. Stave off hunger,\nand sculpt your physique.");
                    break;
                case 3:
                    //SetLabelText("Protein timing", "", "Eat within 30 min of working out to jump-start muscle repair. Spread your intake across the day to fuel muscle growth.");
                    SetLabelText("Protein timing", "", "Eat within 30 min of working out\nto jump-start muscle repair.\nSpread your intake across the day\nto fuel\nmuscle growth.");
                    break;
                case 4:
                    //SetLabelText(splitedData[1], "g carbs", "Your ally for powering through intense workouts. Carbs fuel your muscles and brain and help you push harder to achieve that lean, formidable frame.");
                    SetLabelText(splitedData[1], "g carbs", "Your ally for powering through\nintense workouts.\nCarbs fuel your muscles and brain\nand help you push harder to achieve\nthat lean, formidable frame.");
                    break;
                case 5:
                    //SetLabelText(splitedData[2], "g fat", "Your secret weapon for hormonal balance and sustained energy. Helps produce testosterone, absorb vital nutrients, and support your metabolism.");
                    SetLabelText(splitedData[2], "g fat", "Your secret weapon for\nhormonal balance and sustained energy.\nHelps produce testosterone,\nabsorb vital nutrients,\nand support your metabolism.");
                    break;
                case 6:
                    //SetLabelText("Meal timing", "", "Time your meals to optimize absorption and energy. Eat before and after your workouts and consider a protein-rich snack or shake just before bed for maximum benefit.");
                    SetLabelText("Meal timing", "", "Time your meals to optimize absorption \nand energy. Eat before and after \nyour workouts and consider a \nprotein-rich snack or shake \njust before bed for maximum benefit.");
                    break;
                case 7:
                    //SetLabelText("Protein shake", "", "Drink before or after your workouts to maximize fat loss and muscle gain. On rest days, have it before going to bed, or whenever convenient.");
                    SetLabelText("Protein shake", "", "Drink before or after your workouts\nto maximize fat loss and muscle gain.\nOn rest days, have it before\ngoing to bed, or whenever\nconvenient.");
                    break;
                case 8:
                    //SetLabelText("Snack smarter", "", "Easy, portable snacks keep your energy up and cravings down. Don't let long gaps between meals derail your progress. Maintain muscle, manage hunger.");
                    SetLabelText("Snack smarter", "", "Easy, portable snacks keep\nyour energy up and cravings down.\nDon't let long gaps between\nmeals derail your progress.\nMaintain muscle, manage hunger.");
                    break;
                case 9:
                    //SetLabelText("Stay flexible", "", "If you miss a meal or snack, don't stress. Instead, eat it later. Focus on maintaining your overall daily totals rather than perfect meal timing.");
                    SetLabelText("Stay flexible", "", "If you miss a meal or snack,\ndon't stress. Instead, eat it later.\nFocus on maintaining your\noverall daily totals rather\nthan perfect meal timing.");
                    break;
                case 10:
                    //SetLabelText("Eating out", "", "Choose wisely. Opt for dishes with lean proteins and vegetables. Request sauces on the side to control calories. Drink plenty of water.");
                    SetLabelText("Eating out", "", "Choose wisely. Opt for dishes \nwith lean proteins and vegetables.\nRequest sauces on the side\nto control calories.\nDrink plenty of water.");
                    break;
                case 11:
                    //SetLabelText("Alcohol", "", "Impacts recovery and fat loss. One drink is okay, but too much can undo your efforts. Choose lighter options and keep your goals in sight.");
                    SetLabelText("Alcohol", "", "Impacts recovery and fat loss.\nOne drink is okay, but too much\ncan undo your efforts.\nChoose lighter options and\nkeep your goals in sight.");
                    break;
                case 12:
                    //SetLabelText("Supplementation", "", "Supplements can fill nutritional gaps, but they're not a silver bullet. Focus on whole foods first, supplements second. Creatine is a popular option.");
                    SetLabelText("Supplementation", "", "Supplements can fill nutritional gaps,\nbut they're not a silver bullet.\nFocus on whole foods first,\nsupplements second.\nCreatine is a popular option.");
                    break;
                case 13:
                    //SetLabelText("Creatine: proven power", "", "One of the most researched supplements for over 30 years. Boosts strength, muscle growth, and recovery. Try 3-5 g a day for best results. Safe, effective, and time-tested.");
                    SetLabelText("Creatine: proven power", "", "One of the most researched supplements\nfor over 30 years. Boosts strength,\nmuscle growth, and recovery.\nTry 3-5 g a day for best results.\nSafe, effective and time-tested.");
                    break;
                case 14:
                    //SetLabelText("Vitamin D", "", "Essential for muscle, bone, and hormonal health. Boosts performance and recovery. Ensure adequate intake through sunlight, food, or supplements.");
                    SetLabelText("Vitamin D", "", "Essential for muscle, bone,\nand hormonal health.\nBoosts performance and recovery.\nEnsure adequate intake through\nsunlight, food, or supplements.");
                    break;
                case 15:
                    //SetLabelText("Prep smart", "", "Meal prep can save you time and keep you on track. Prepare meals in bulk for easy, grab-and-go eating. Set yourself up for success, make Sunday your meal prep day.");
                    SetLabelText("Prep smart", "", "Meal prep can save you time and keep\nyou on track. Prepare meals in\nbulk for easy,grab-and-go\neating. Set yourself up for success,\nmake Sunday your meal prep day.");
                    break;
                case 16:
                    //SetLabelText("Feedback loop", "", "Regularly assess how your meal plan is affecting your energy, performance, and body composition. Log your weight often. Update your plan when progress stalls.");
                    SetLabelText("Feedback loop", "", "Regularly assess how your\nmeal plan is affecting your energy,\nperformance, and body composition.\nLog your weight often.\nUpdate your plan when progress stalls.");
                    break;
                default:
                    ContinueReviewBtn.Text = isReviewTips ? "Continue" : "Finalizing...";
                    LblHeading.Text = isReviewTips ? "" : _title;
                    LblSubHead.Text = isReviewTips ? "" : "Your plan is almost ready.";
                    var finalMealPlan = LocalDBManager.Instance.GetDBSetting("FinalMealPlan")?.Value;
                    if (IsDataLoaded || isReviewTips || !string.IsNullOrEmpty(finalMealPlan))
                    {
                        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
                        //    PopupNavigation.Instance.PopAsync();
                        //await MauiProgram.SafeDismissTopPopup();
                        if (this.Handler != null)
                            await this.CloseAsync();
                    }
                    break;
            }
            count++;
        }
        catch (Exception ex)
        {

        }
    }
}
