<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             x:Class="DrMaxMuscle.Cells.LinkGestureCell">
    <Frame
     Margin="10,10,40,5"
     CornerRadius="12"
     x:Name="FrmContainer"
     Padding="20,12,20,12"
     HorizontalOptions="Start"
     BorderColor="#ffffff"
     HasShadow="False"
     Opacity="0"
     BackgroundColor="#ffffff">
        <Label
         x:Name="LblQuestion"
         Text="{Binding Question}"
         TextDecorations="Underline"
         TextColor="{x:Static app:AppThemeConstants.BlueLightColor}"
         FontSize="17"
         LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
         HorizontalOptions="Start"
         Margin="4,0">

            <Label.GestureRecognizers>
                <TapGestureRecognizer
                 Tapped="TapGestureRecognizer_Tapped" />
            </Label.GestureRecognizers>
        </Label>
    </Frame>
</ContentView>
