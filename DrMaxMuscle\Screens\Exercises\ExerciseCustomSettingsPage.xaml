﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    xmlns:app="clr-namespace:DrMaxMuscle.Constants"
    xmlns:control="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Screens.Exercises.ExerciseCustomSettingsPage"
             Title="ExerciseCustomSettingsPage">
    <ScrollView x:Name="scrollView">
        
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0">
          <StackLayout Margin="20,20,20,0" Spacing="0">
            <Label x:Name="LblNotes" Style="{StaticResource BoldLabelStyle}" />
             <Button IsVisible="false"/>

            <control:ExtendedEditorControl BackgroundColor="Transparent" Unfocused="NotesEntry_Unfocused" x:Name="NotesEnrty" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" Placeholder="Enter note" PlaceholderColor="Silver" />

            <BoxView HeightRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}" />
            
          </StackLayout>
        
          <StackLayout Margin="20,20,20,0">
            <Label x:Name="LblSettings" Style="{StaticResource BoldLabelStyle}" />
          </StackLayout>
                        
          <StackLayout Margin="20,20,20,0">
              <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblFavorite" Text="Favorite" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}"  />
                    <Switch x:Name="FavoriteSwitch" HorizontalOptions="End"/>
                </StackLayout>
              <Label x:Name="LblFavoriteTips" Text="Show exercise above others" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}"/>
           <StackLayout Margin="0,10,0,15">
            <Label x:Name="LblBodyParts" Style="{StaticResource NormalLabelStyle}" />
            <control:DropDownPicker x:Name="BodyPartPicker" Margin="0,10,0,0" Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}" Style="{StaticResource PickerStyle}" HeightRequest="40">
                
                </control:DropDownPicker>
          </StackLayout>
              
              <StackLayout Margin="0,0,0,15">
            <Label x:Name="LblEquipment" Style="{StaticResource NormalLabelStyle}" />
            <control:DropDownPicker x:Name="EquipmentPicker" Margin="0,10,0,0" Style="{StaticResource PickerStyle}" Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}"  HeightRequest="40">
                
                </control:DropDownPicker>
          </StackLayout>
              <StackLayout Margin="0,0,0,15">
            <Label x:Name="LblExerciseLevel" Text="Exercise difficulty" Style="{StaticResource NormalLabelStyle}" />
            <control:DropDownPicker x:Name="ExerciseLevelPicker" Margin="0,10,0,0" Style="{StaticResource PickerStyle}" HeightRequest="40" Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}" >
          
                </control:DropDownPicker>
          </StackLayout>
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblCustomReps" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" FontSize="{OnPlatform Android='14', iOS='17'}" />
                    <Switch x:Name="CustomRepsSwitch" HorizontalOptions="End"/>
                </StackLayout>
                
                <StackLayout x:Name="RepsStack" IsVisible="false" >
                  <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                    <Label x:Name="LblMin" Margin="15,0,0,0" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='12', iOS='14'}" />
                    <t:DrMuscleButton x:Name="RepsMinimumLess" Text="-" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="5,0,0,0" />
                    <Label x:Name="RepsMinimumLabel" FontSize="14" HorizontalOptions="CenterAndExpand" VerticalOptions="Center" HorizontalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                    <t:DrMuscleButton x:Name="RepsMinimumMore" Text="+" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" />
                  </StackLayout>
                  <StackLayout Orientation="Horizontal" Margin="0,10,0,0">
                    <Label x:Name="LblMax" VerticalOptions="Center" Margin="15,0,0,0" Style="{StaticResource LabelStyle}" FontSize="{OnPlatform Android='12', iOS='14'}" />
                    <t:DrMuscleButton x:Name="RepsMaximumLess" Text="-" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="1,0,0,0" />
                    <Label x:Name="RepsMaximumLabel" FontSize="14" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center"  Style="{StaticResource NormalLabelStyle}"></Label>
                    <t:DrMuscleButton x:Name="RepsMaximumMore" Text="+" WidthRequest="40" HeightRequest="40" Style="{StaticResource buttonStyle}" Margin="0,0,0,0" />
                  </StackLayout>
                  <StackLayout Margin="15,10,0,0">
                    <t:DrMuscleButton x:Name="SaveCustomRepsButton" Style="{StaticResource buttonStyle}"/>
                  </StackLayout>
                </StackLayout>
              
          </StackLayout>
                
                <StackLayout Margin="20,20,20,0">
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblExercise" Text="Use seconds instead of reps" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="TimebaseSwitch" HorizontalOptions="End"/>
                    </StackLayout>
                </StackLayout>
       
<!---->
          <StackLayout Margin="20,20,20,0">
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblCustomSet" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="CustomSetsSwitch" HorizontalOptions="End"/>
                </StackLayout>
                <StackLayout x:Name="SetsStack" Margin="10,10,0,0" IsVisible="false">
               
                    <!--<Frame Margin="0" HasShadow="False" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
          <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
              <StackLayout 
                                  Margin="0"
                  x:Name="RestPauseGradient"
                                      HorizontalOptions="FillAndExpand" VerticalOptions="End"  >
                            
                        <Label FontSize="14" Text="Rest-pause" x:Name="BtnRestPause" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="White" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnRestPauseClicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>
              <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <StackLayout 
                                  Margin="0" 
                                     HorizontalOptions="FillAndExpand" x:Name="NormalGradient" VerticalOptions="End"  >
                            
              <Label FontSize="14" Text="Normal" x:Name="BtnNormal" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center" TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnNormalClicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>

              <BoxView WidthRequest="1" x:Name="BxSaperator3" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
                            <StackLayout 
                                  Margin="0" 
                                     HorizontalOptions="FillAndExpand" x:Name="RPyramidGradient" VerticalOptions="End" >

                                <Label FontSize="14"  Text="Pyramid" x:Name="BtnRPyramid" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"   TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                                <StackLayout.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="BtnRPyramid_Clicked" />
                                </StackLayout.GestureRecognizers>
                            </StackLayout>

              <BoxView WidthRequest="1" x:Name="BxSaperator2" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <StackLayout 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="PyramidGradient" VerticalOptions="End"  >
                            
              <Label  Text="Pyramid" x:Name="BtnPyramid" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"   TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnPyramid_Clicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>
          </StackLayout>
              </Frame>-->
                    <control:DropDownPicker x:Name="SetStylePicker" Margin="0,10,0,0" Image="{OnPlatform Android='white_down_arrow.png', iOS='black_down_arrow.png'}"  Style="{StaticResource PickerStyle}" Unfocused="SetStylePicker_Unfocused" HeightRequest="40">
                                    <control:DropDownPicker.Image>
                                        <OnPlatform x:TypeArguments="x:String"  />
                                    </control:DropDownPicker.Image>
                                </control:DropDownPicker>
                <Label x:Name="LblRestPauseSets" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}"/>
              </StackLayout>
          </StackLayout>
          
            <StackLayout Margin="20,20,20,0" HorizontalOptions="FillAndExpand"> 
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblCustomWarmUp" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="WarmupSwitch" HorizontalOptions="End"/>
                </StackLayout>
                <StackLayout x:Name="StackWarmup" IsVisible="false" Margin="20,0,0,0">
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="LblHowManyWarmups" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='15'}"/>
                      <control:DrEntry x:Name="WarmupEntry" HorizontalOptions="End" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                    </StackLayout>                    
                    <t:DrMuscleButton Margin="0,10,0,0" x:Name="SaveWarmupButton" Style="{StaticResource buttonStyle}" />                
                </StackLayout>    
          </StackLayout>
          <StackLayout x:Name="MainIncrementStack" Margin="20,20,20,0" HorizontalOptions="FillAndExpand"> 
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblCustomIncrements" Text="Use custom increments" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="IncrementSwitch" HorizontalOptions="End"/>
                </StackLayout>
                <StackLayout x:Name="StackIncrements" IsVisible="false" Margin="20,0,0,0">
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="Min" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='15'}"/>
                      <control:DrEntry x:Name="MinEntry" HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                    </StackLayout>
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="Increments" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='15'}"/>
                      <control:DrEntry x:Name="UnitEntry" HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                    </StackLayout>
                    <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="Max" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='15'}"/>
                      <control:DrEntry x:Name="MaxEntry" HorizontalOptions="End" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="UnitEntry_TextChanged"  />
                    </StackLayout>
                      <t:DrMuscleButton Margin="0,10,0,0" x:Name="SaveIncrementsButton" Style="{StaticResource buttonStyle}" />                
                </StackLayout>    
          </StackLayout>
        <StackLayout Margin="20,20,20,0" HorizontalOptions="FillAndExpand">
            <StackLayout Orientation="Horizontal" Spacing="0" Margin="0,10,0,0" >
                      <Label x:Name="LblSetCount" Text="Work sets" HorizontalOptions="StartAndExpand" Style="{StaticResource NormalLabelStyle}" />
                      <control:DrEntry x:Name="SetEntry" HorizontalOptions="End" MaxLength="2" Placeholder="Tap to set" VerticalOptions="Center" HorizontalTextAlignment="End" Keyboard="Telephone" Style="{StaticResource entryStyle}" TextChanged="SetEntry_TextChanged"  />
                    </StackLayout>
                      <t:DrMuscleButton Margin="0,10,0,0" Text="Save work sets" x:Name="SaveSetCountButton" Style="{StaticResource buttonStyle}" />                
            </StackLayout>
         <StackLayout Margin="20,20,20,0">
                <Label x:Name="LblBackOffSets" Text="Use a back-off set:" Style="{StaticResource NormalLabelStyle}" />
                <!--<Picker x:Name="BackOffPicker" Margin="0,10,0,0" Style="{StaticResource PickerStyle}" />-->
             <Frame Margin="0" HasShadow="False" IsClippedToBounds="True" Padding="0" BackgroundColor="Transparent" BorderColor="{x:Static app:AppThemeConstants.BlueColor}" CornerRadius="6">
          <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand" Spacing="0" BackgroundColor="Transparent">
              <StackLayout 
                                  Margin="0"
                  x:Name="DefaultGradient"
                                   HorizontalOptions="FillAndExpand" VerticalOptions="End"  >
                            
                        <Label Text="Default" x:Name="BtnDefault" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="White" BackgroundColor="Transparent"  HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnDefault_Clicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>
              <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <StackLayout 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="NoGradient" VerticalOptions="End"  >
                            
              <Label Text="No" x:Name="BtnNo" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnNo_Clicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>
              <BoxView WidthRequest="1" BackgroundColor="{x:Static app:AppThemeConstants.BlueColor}"/>
              <StackLayout 
                                  Margin="0" 
                                      HorizontalOptions="FillAndExpand" x:Name="YesGradient" VerticalOptions="End"  >
                            
              <Label Text="Yes" x:Name="BtnYes" FontSize="14" HorizontalOptions="FillAndExpand"  VerticalOptions="FillAndExpand" VerticalTextAlignment="Center" HorizontalTextAlignment="Center"  TextColor="#0C2432" BackgroundColor="Transparent" HeightRequest="40" ></Label>
                  <StackLayout.GestureRecognizers>
                      <TapGestureRecognizer Tapped="BtnYes_Clicked" />
                  </StackLayout.GestureRecognizers>
              </StackLayout>
              
          </StackLayout>
              </Frame>
                <Label x:Name="LblBackOffSetDesc" Text="Do more reps with less weight on your last set to build muscle faster" Style="{StaticResource NormalLabelStyle}" FontSize="{OnPlatform Android='13', iOS='14'}" />
            </StackLayout>
        <StackLayout Margin="20,20,20,0">
                <StackLayout Orientation="Horizontal" HorizontalOptions="FillAndExpand">
                    <Label x:Name="LblUnilateral" Text="Train your left and right sides separately?" HorizontalOptions="StartAndExpand" TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                    <Switch x:Name="UnilateralSwitch" HorizontalOptions="End"/>
                    </StackLayout>
            <!--<Label x:Name="LblUnilateralDesc" Text="A left/right side separately exercise is an exercise you do for your right and left arm or leg separately." Style="{StaticResource NormalLabelStyle}" />-->
                </StackLayout>
          <StackLayout HorizontalOptions="FillAndExpand" Spacing="10" VerticalOptions="EndAndExpand" Margin="20,20,20,20">
              <Label x:Name="LblMore" Text="MORE" Style="{StaticResource BoldLabelStyle}" />
              <t:DrMuscleButton x:Name="ResetButton" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"  BackgroundColor="Red" BorderColor="Red" TextColor="White"/>
              <t:DrMuscleButton x:Name="DeleteButton" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}" BackgroundColor="Red" BorderColor="Red" TextColor="White" />          </StackLayout>
    </StackLayout>
        </ScrollView>
</ContentPage>
