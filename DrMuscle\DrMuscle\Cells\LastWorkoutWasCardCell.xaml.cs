﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DrMuscle.Helpers;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace DrMuscle.Cells
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class LastWorkoutWasCardCell : ViewCell
    {
        public LastWorkoutWasCardCell()
        {
            InitializeComponent();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            var recordModel = (BotModel)this.BindingContext;
            if (recordModel == null)
                return;
            if (recordModel.IsNewRecordAvailable && recordModel.StrengthImage == "starTrophy.png")
                WeightProgress2.Margin = new Thickness(10, 0, 10, 10);
        }
    }
}
