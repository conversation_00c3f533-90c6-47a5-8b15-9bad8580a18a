using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Utility;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class FullReview : PopupPage
{
    public FullReview()
    {
        InitializeComponent();
        BindData();
        
    }

    private async void BindData()
    {
        if (CurrentLog.Instance.IsMonthlyUser == null)
        {
            var result = await DrMuscleRestClient.Instance.IsMonthlyUser();
            if (result != null)
            {
                CurrentLog.Instance.IsMonthlyUser = result.Result;
                Upgrade.IsVisible = result.Result;
            }
        }
        else
            Upgrade.IsVisible = (bool)CurrentLog.Instance.IsMonthlyUser;
    }

    async void MonthFree_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            await Task.Delay(1000);
        }
        catch (Exception ex)
        {

        }
        MessagingCenter.Send<ShareMessage>(new ShareMessage() { }, "ShareMessage");

    }

    async void Upgrade_Clicked(System.Object sender, System.EventArgs e)
    {
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //   PopupNavigation.Instance.PopAsync();
        //await PagesFactory.PushAsync<DrMuscle.Screens.Subscription.SubscriptionPage>();
        //
        await HelperClass.SendMail("Upgrade to annual (4 months free)", "I would like to upgrade to the annual plan and get 4 months free. Please tell me more.");
    }

    async void ReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();
        await Task.Delay(500);
        await HelperClass.RateApp("Full_Review_Rate");
    }
}
