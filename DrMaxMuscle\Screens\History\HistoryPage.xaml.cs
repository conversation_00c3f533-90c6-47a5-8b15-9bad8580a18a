using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using RGPopup.Maui.Services;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Screens.History;

public partial class HistoryPage : ContentPage
{
    private Dictionary<string, long> ExercisesLabelToID;
    private Dictionary<string, TimeSpan> DurationToDays;
    ObservableCollection<HistoryItem> historyModel = new ObservableCollection<HistoryItem>();
    private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel2 = new Dictionary<double, string>();
    GetUserWorkoutLogAverageResponse mainWorkoutLog;
    bool isHistoryLoaded = false;
    double chartWidth = 0;
    public HistoryPage()
	{
		InitializeComponent();
        HistoryListView.ItemsSource = historyModel;
        HistoryListView.HasUnevenRows = true;
        HistoryListView.ItemTemplate = new HistoryDataTemplateSelector
        {
            HistoryDateTemplate = DateTemplate,
            HistorySetTemplate = SetTemplate,
            HistoryExerciseTemplate = ExerciseTemplate,
            HistoryStatisticTemplate = StatisticTemplate
        };
        ExericsesPicker.Unfocused += ExericsesPicker_Unfocused;
        DatePicker.Unfocused += DatePicker_Unfocused;
        ButtonStartWorkout.Clicked += ButtonStartWorkout_Clicked;
        if (LocalDBManager.Instance.GetDBSetting("AppLanguage") == null)
        {
            LocalDBManager.Instance.SetDBSetting("AppLanguage", "en");
        }
        RefreshLocalized();

        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
            GetHistory();
        });
    }
    private void RefreshLocalized()
    {
        Title = AppResources.History;
        DurationToDays = new Dictionary<string, TimeSpan>();
        DurationToDays.Add(AppResources.Last3Workouts, new TimeSpan(0, 0, 0));
        DurationToDays.Add(AppResources.LastMonth, (DateTime.Now - DateTime.Now.AddMonths(-1)));
        DurationToDays.Add(AppResources.Last3Months, (DateTime.Now - DateTime.Now.AddMonths(-3)));
        DurationToDays.Add(AppResources.Last6Months, (DateTime.Now - DateTime.Now.AddMonths(-6)));
        DurationToDays.Add(AppResources.LastYear, (DateTime.Now - DateTime.Now.AddYears(-1)));
        DurationToDays.Add(AppResources.AllTime, (DateTime.Now - DateTime.Now.AddYears(-100)));
        //DatePicker.IsVisible = false;
        DatePicker.Items.Clear();
        DatePicker.Items.Add(AppResources.Last3Workouts);
        //DatePicker.Items.Add(AppResources.AllTime);
        DatePicker.SelectedIndex = 0;
        LblTimeFrame.Text = "Show logs for:";

    }
    void OnCancelClicked(object sender, System.EventArgs e)
    {

        StackLayout s = ((StackLayout)((Button)sender).Parent);
        if (s.Children.Count == 4)
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], false);
            SetVisibility(s.Children[3], true);
        }
        else
        {
            SetVisibility(s.Children[0], false);
            SetVisibility(s.Children[1], false);
            SetVisibility(s.Children[2], true);
        }
    }

    void OnContextMenuClicked(object sender, System.EventArgs e)
    {
        StackLayout s = ((StackLayout)((Button)sender).Parent);
        if (s.Children.Count == 4)
        {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], true);
            SetVisibility(s.Children[3], false);
        }
        else
        {
            SetVisibility(s.Children[0], true);
            SetVisibility(s.Children[1], true);
            SetVisibility(s.Children[2], false);
        }

    }
    void SetVisibility(IView view, bool isVisible)
    {
        if (view is View mauiView)
        {
            mauiView.IsVisible = isVisible;
        }
    }

    void ScChartLogs_ValueChanged(object sender, EventArgs e)
    {

    }

    async void DatePicker_Unfocused(object sender, FocusEventArgs e)
    {
        try
        {
            if (ExericsesPicker.SelectedIndex == 0 && DatePicker.SelectedIndex == 1)
            {
                GetHistory();
                return;
            }
            else if (ExericsesPicker.SelectedIndex == 0)
            {
                LoadDefaultChartOfflineAvailable();
                return;
            }

            string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
            long exerciseId = ExercisesLabelToID[selected];
            GetHistory();
        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }

    async void ExericsesPicker_Unfocused(object sender, EventArgs e)
    {
        try
        {
            if (ExericsesPicker.Items.Count > 1)
            {
                if (ExericsesPicker.SelectedIndex == 0)
                {
                    DatePicker.Items.Clear();
                    DatePicker.Items.Add(AppResources.Last3Workouts);
                    //DatePicker.Items.Add(AppResources.AllTime);
                    DatePicker.SelectedIndex = 0;
                    LoadDefaultChartOfflineAvailable();
                    GetHistory();
                    return;
                }
                else
                {
                    int currentDatePickerIndex = DatePicker.SelectedIndex;
                    DatePicker.Items.Clear();
                    foreach (var i in DurationToDays)
                        DatePicker.Items.Add(i.Key);
                    DatePicker.Items.RemoveAt(0);
                    DatePicker.SelectedIndex = currentDatePickerIndex;
                }

                if (ExericsesPicker.SelectedIndex >= 0)
                {
                    string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
                    long exerciseId = ExercisesLabelToID[selected];
                    GetHistory();
                }
            }
        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }


    public async void OnBeforeShow()
    {
        //base.OnBeforeShow();
        var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        _firebase.SetScreenName("history_page");

        
        LoadDefaultChartOfflineAvailable();
        ExericsesPicker.Items.Clear();
        ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
        ExericsesPicker.SelectedIndex = 0;
        DatePicker.Items.Clear();
        DatePicker.Items.Add(AppResources.Last3Workouts);
        //DatePicker.Items.Add(AppResources.AllTime);
        DatePicker.SelectedIndex = 0;
        if (CurrentLog.Instance.ShowCurentExercise)
        {
            GetLast3MonthHistory();
            CurrentLog.Instance.ShowCurentExercise = false;
        }

        else if (CurrentLog.Instance.PastWorkoutDate == null)
            GetHistory();
        else
            LoadPastDateHistory();
        //var historyModels = ((App)Application.Current).WorkoutHistoryContextList.Histories;
        //if (historyModels != null )
        //{
        //    List<HistoryModel> history = await DrMuscleRestClient.Instance.GetHistoryWithoutLoader();
        //    if (history != null)
        //    {
        //        ((App)Application.Current).WorkoutHistoryContextList.Histories = history;
        //        ((App)Application.Current).WorkoutHistoryContextList.SaveContexts();
        //        GetHistory();
        //    }
        //}
    }

    private async void GetHistory()
    {
        try
        {
            int lastworkoutday = 28;
            try
            {
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                if (workouts != null && workouts.LastWorkoutDate != null)
                {
                    lastworkoutday = (int)(DateTime.Now - (DateTime)workouts.LastWorkoutDate).TotalDays + 28;
                }

            }
            catch (Exception ex)
            {

            }
            HistoryListView.ItemsSource = null;
            historyModel = new ObservableCollection<HistoryItem>();

            List<HistoryModel> historyModels = new List<HistoryModel>();

            long exerciseId = ExericsesPicker.SelectedIndex == 0 || ExericsesPicker.Items.Count == 0 ? -1 : (long)ExercisesLabelToID[ExericsesPicker.Items[ExericsesPicker.SelectedIndex]];
            DateTime since = DatePicker.SelectedIndex <= 0 ? new DateTime(1900, 1, 1) : DateTime.Now.ToUniversalTime().AddDays(-DurationToDays[DatePicker.Items[DatePicker.SelectedIndex]].TotalDays);
            var time = DatePicker.Items[DatePicker.SelectedIndex] == AppResources.Last3Workouts ? lastworkoutday : DatePicker.Items[DatePicker.SelectedIndex] == AppResources.AllTime ? 5000 : DurationToDays[DatePicker.Items[DatePicker.SelectedIndex]].TotalDays;
            GetUserWorkoutLogAverageForExerciseRequest request = DatePicker.Items[DatePicker.SelectedIndex] == AppResources.Last3Workouts ? new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = null, PeriodSinceToday = (DateTime.Now - DateTime.Now.AddDays(time)) } : new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = exerciseId, PeriodSinceToday = (DateTime.Now - DateTime.Now.AddDays(time)) };
            if (exerciseId == -1)
                request.ExerciseId = null;

            List<HistoryModel> history = historyModels.Count == 0 ? await DrMuscleRestClient.Instance.GetHistoryAllTime(request) : historyModels;
            isHistoryLoaded = true;
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;

            if(history != null && history.Count > 0)
                foreach (HistoryModel hm in history.Where(h => h.WorkoutDate >= since))
                {
                    if (exerciseId == -1 || hm.Exercises.Any(e => e.Exercise.Id == exerciseId))
                    {
                        HistoryItem i = new HistoryItem() { WorkoutLogId = hm.Id, Label = hm.WorkoutDate.ToLocalTime().ToString(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value == "fr-FR" ? "dddd, MMMM dd | HH:mm" : "dddd, MMMM dd | hh:mm tt"), ItemType = HistoryItemType.DateType };

                        historyModel.Add(i);
                        foreach (HistoryExerciseModel hem in hm.Exercises.OrderBy(e => e.Sets.Min(s => s.LogDate)))
                        {
                            if (exerciseId == -1 || hem.Exercise.Id == exerciseId)
                            {
                                bool isTimebase = hem.Exercise.IsTimeBased;
                                string repString = AppResources.Reps.ToLower();
                                if (isTimebase)
                                {
                                    repString = isTimebase ? "secs" : AppResources.Reps.ToLower();
                                }
                                historyModel.Add(new HistoryItem()
                                {
                                    WorkoutLogId = hm.Id,
                                    ExerciseId = hem.Exercise.Id,
                                    Label = hem.Exercise.Label,
                                    ItemType = HistoryItemType.ExerciseType,
                                    TotalReps = hem.Reps.ToString() + " " + repString,
                                    TotalSets = hem.Sets.Count().ToString() + " " + AppResources.Sets,
                                    TotalWeight = string.Format("{0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                                string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                                string.Format("{0:0.00} lbs", hem.TotalWeight.Lb), AppResources.Total.ToLower()).ReplaceWithDot()
                                });
                                bool isWarmups = false;
                                foreach (WorkoutLogSerieModel l in hem.Sets.OrderBy(ei => ei.LogDate))
                                {
                                    var hItem = new HistoryItem()
                                    {
                                        WorkoutLogId = hm.Id,
                                        ExerciseId = hem.Exercise.Id,
                                        WorkoutLogSerieId = l.Id,

                                        Label = string.Format("{0:0.00} {1} x {2} {3}",
                                                       LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? l.Weight.Kg : l.Weight.Lb,
                                                     LocalDBManager.Instance.GetDBSetting("massunit").Value,
                                                     l.Reps, repString).ReplaceWithDot(),
                                        ItemType = HistoryItemType.SetType,
                                        Model = l
                                    };
                                    if (hem.Sets.FirstOrDefault().Exercice.IsFlexibility)
                                        hItem.Label = string.Format("{0} x {1} {2}",
                                                       "Body",
                                                     l.Reps, repString);
                                    if (hem.Exercise.Id == 16508)
                                    {
                                        hItem.Label = string.Format("{0} x {1} {2}",
                                                    l.IsWarmups ? "Brisk" : "Fast",
                                                     l.Reps, repString).ReplaceWithDot();
                                    }
                                    else if (hem.Exercise.BodyPartId == 12)
                                    {
                                        hItem.Label = string.Format("{0} x {1} {2}",
                                                    l.IsWarmups ? "Brisk" : !isWarmups ? "Fast" : "Cooldown",
                                                     l.Reps, repString).ReplaceWithDot();
                                    }
                                    if (!l.IsWarmups)
                                        isWarmups = true;
                                    if (l.IsWarmups && isWarmups)
                                        isWarmups = false;
                                    historyModel.Add(hItem);
                                }
                                historyModel.Add(new HistoryItem()
                                {
                                    WorkoutLogId = hm.Id,
                                    ExerciseId = hem.Exercise.Id,
                                    Label = hem.Sets.FirstOrDefault().Exercice.IsFlexibility ? string.Format("{0} {1} | {2} {3}",
                                                          hem.Sets.Count(), AppResources.Sets,
                                                          hem.Reps, repString) : string.Format("{0} {1} | {2} {3} | {4}",
                                                          hem.Sets.Count(), AppResources.Sets,
                                                          hem.Reps, repString,
                                                          LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                                string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                            string.Format("{0:0.00} lbs", hem.TotalWeight.Lb)).ReplaceWithDot(),
                                    ItemType = HistoryItemType.StatisticType

                                });
                            }
                        }
                    }

                }
            HistoryListView.ItemsSource = historyModel;
            if (historyModel.Count > 0)
            {
                PickerStack.IsVisible = true;
                LblTimeFrame.IsVisible = true;
                NoDataLabel.IsVisible = false;
            }
            else
            {
                if (request.ExerciseId == null)
                {
                    PickerStack.IsVisible = false;
                    LblTimeFrame.IsVisible = false;
                    NoDataLabel.IsVisible = true;
                    // uncomment code please
                    //await Task.Delay(300);
                    //TooltipEffect.SetHasShowTooltip(ToolTipButton, true);
                }
            }
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();

        }
    }

    private async void GetLast3MonthHistory()
    {
        try
        {
            int lastworkoutday = 90;
            try
            {
                var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
                if (workouts != null && workouts.LastWorkoutDate != null)
                {
                    lastworkoutday = (int)(DateTime.Now - (DateTime)workouts.LastWorkoutDate).TotalDays + 90;
                }

            }
            catch (Exception ex)
            {

            }
            HistoryListView.ItemsSource = null;
            historyModel = new ObservableCollection<HistoryItem>();



            //if (DatePicker.Items.Count > 0)

            List<HistoryModel> historyModels = new List<HistoryModel>();

            long exerciseId = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id;
            DateTime since = DatePicker.SelectedIndex == 0 ? new DateTime(1900, 1, 1) : DateTime.Now.ToUniversalTime().AddDays(-DurationToDays[DatePicker.Items[DatePicker.SelectedIndex]].TotalDays);
            var time = lastworkoutday;
            GetUserWorkoutLogAverageForExerciseRequest request = DatePicker.Items[DatePicker.SelectedIndex] == AppResources.Last3Workouts ? new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = null, PeriodSinceToday = (DateTime.Now - DateTime.Now.AddDays(time)) } : new GetUserWorkoutLogAverageForExerciseRequest() { ExerciseId = exerciseId, PeriodSinceToday = (DateTime.Now - DateTime.Now.AddDays(time)) };


            DatePicker.Items.Clear();
            DatePicker.Items.Add(AppResources.Last3Workouts);
            DatePicker.Items.Add(AppResources.Last3Months);
            DatePicker.SelectedIndex = 1;
            List<HistoryModel> history = await DrMuscleRestClient.Instance.GetHistoryAllTime(request);
            isHistoryLoaded = true;
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;


            foreach (HistoryModel hm in history.Where(h => h.WorkoutDate >= since))
            {
                if (exerciseId == -1 || hm.Exercises.Any(e => e.Exercise.Id == exerciseId))
                {
                    HistoryItem i = new HistoryItem() { WorkoutLogId = hm.Id, Label = hm.WorkoutDate.ToLocalTime().ToString(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value == "fr-FR" ? "dddd, MMMM dd | HH:mm" : "dddd, MMMM dd | hh:mm tt"), ItemType = HistoryItemType.DateType };

                    historyModel.Add(i);
                    foreach (HistoryExerciseModel hem in hm.Exercises.OrderBy(e => e.Sets.Min(s => s.LogDate)))
                    {
                        if (exerciseId == -1 || hem.Exercise.Id == exerciseId)
                        {
                            bool isTimebase = hem.Exercise.IsTimeBased;
                            string repString = AppResources.Reps.ToLower();
                            if (isTimebase)
                            {
                                repString = isTimebase ? "secs" : AppResources.Reps.ToLower();
                            }
                            historyModel.Add(new HistoryItem()
                            {
                                WorkoutLogId = hm.Id,
                                ExerciseId = hem.Exercise.Id,
                                Label = hem.Exercise.Label,
                                ItemType = HistoryItemType.ExerciseType,
                                TotalReps = hem.Reps.ToString() + " " + repString,
                                TotalSets = hem.Sets.Count().ToString() + " " + AppResources.Sets,
                                TotalWeight = string.Format("{0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                            string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                            string.Format("{0:0.00} lbs", hem.TotalWeight.Lb), AppResources.Total.ToLower()).ReplaceWithDot()
                            });
                            bool isWarmups = false;
                            foreach (WorkoutLogSerieModel l in hem.Sets.OrderBy(ei => ei.LogDate))
                            {
                                var hItem = new HistoryItem()
                                {
                                    WorkoutLogId = hm.Id,
                                    ExerciseId = hem.Exercise.Id,
                                    WorkoutLogSerieId = l.Id,
                                    Label = string.Format("{0:0.00} {1} x {2} {3}",
                                                 LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? l.Weight.Kg : l.Weight.Lb,
                                                 LocalDBManager.Instance.GetDBSetting("massunit").Value,
                                                 l.Reps, repString).ReplaceWithDot(),
                                    ItemType = HistoryItemType.SetType,
                                    Model = l
                                };
                                if (hem.Sets.FirstOrDefault().Exercice.IsFlexibility)
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                   "Body",
                                                 l.Reps, repString);
                                if (hem.Exercise.Id == 16508)
                                {
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                l.IsWarmups ? "Brisk" : "Fast",
                                                 l.Reps, repString).ReplaceWithDot();
                                }
                                else if (hem.Exercise.BodyPartId == 12)
                                {
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                l.IsWarmups ? "Brisk" : !isWarmups ? "Fast" : "Cooldown",
                                                 l.Reps, repString).ReplaceWithDot();
                                }
                                if (!l.IsWarmups)
                                    isWarmups = true;
                                if (l.IsWarmups && isWarmups)
                                    isWarmups = false;
                                historyModel.Add(hItem);
                            }
                            historyModel.Add(new HistoryItem()
                            {
                                WorkoutLogId = hm.Id,
                                ExerciseId = hem.Exercise.Id,
                                Label = hem.Sets.FirstOrDefault().Exercice.IsFlexibility ? string.Format("{0} {1} | {2} {3}",
                                                      hem.Sets.Count(), AppResources.Sets,
                                                      hem.Reps, repString) : string.Format("{0} {1} | {2} {3} | {4}",
                                                      hem.Sets.Count(), AppResources.Sets,
                                                      hem.Reps, repString,
                                                      LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                            string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                        string.Format("{0:0.00} lbs", hem.TotalWeight.Lb)).ReplaceWithDot(),
                                ItemType = HistoryItemType.StatisticType

                            });
                        }
                    }
                }

            }
            HistoryListView.ItemsSource = historyModel;
            if (historyModel.Count > 0)
            {
                PickerStack.IsVisible = true;
                LblTimeFrame.IsVisible = true;
                NoDataLabel.IsVisible = false;
            }
            else
            {
                if (request.ExerciseId == null)
                {
                    PickerStack.IsVisible = false;
                    LblTimeFrame.IsVisible = false;
                    NoDataLabel.IsVisible = true;
                    // uncomment code please
                    //await Task.Delay(300);
                    //TooltipEffect.SetHasShowTooltip(ToolTipButton, true);
                }
            }
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();

        }
    }

    private async void LoadPastDateHistory()
    {
        try
        {
            NoDataLabel.IsVisible = false;
            PickerStack.IsVisible = false;
            LblTimeFrame.IsVisible = false;
            List<HistoryModel> historyModels = new List<HistoryModel>();

            historyModel = new ObservableCollection<HistoryItem>();
            List<HistoryModel> history = await DrMuscleRestClient.Instance.GetPastDateHistory(new GetUserWorkoutLogAverageForExerciseRequest() { Date = ((DateTime)CurrentLog.Instance.PastWorkoutDate) });

            isHistoryLoaded = true;
            if (LocalDBManager.Instance.GetDBSetting("email") == null)
                return;
            var exerciseId = -1;

            foreach (HistoryModel hm in history)
            {
                if (exerciseId == -1)
                {
                    HistoryItem i = new HistoryItem() { WorkoutLogId = hm.Id, Label = hm.WorkoutDate.ToLocalTime().ToString(LocalDBManager.Instance.GetDBSetting("AppLanguage").Value == "fr-FR" ? "dddd, MMMM dd | HH:mm" : "dddd, MMMM dd | hh:mm tt"), ItemType = HistoryItemType.DateType };

                    historyModel.Add(i);
                    foreach (HistoryExerciseModel hem in hm.Exercises.OrderBy(e => e.Sets.Min(s => s.LogDate)))
                    {
                        if (exerciseId == -1 || hem.Exercise.Id == exerciseId)
                        {
                            bool isTimebase = hem.Exercise.IsTimeBased;
                            string repString = AppResources.Reps.ToLower();
                            if (isTimebase)
                            {
                                repString = isTimebase ? "secs" : AppResources.Reps.ToLower();
                            }
                            historyModel.Add(new HistoryItem()
                            {
                                WorkoutLogId = hm.Id,
                                ExerciseId = hem.Exercise.Id,
                                Label = hem.Exercise.Label,
                                ItemType = HistoryItemType.ExerciseType,
                                TotalReps = hem.Reps.ToString() + " " + repString,
                                TotalSets = hem.Sets.Count().ToString() + " " + AppResources.Sets,
                                TotalWeight = string.Format("{0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                            string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                            string.Format("{0:0.00} lbs", hem.TotalWeight.Lb), AppResources.Total.ToLower()).ReplaceWithDot()
                            });
                            bool isWarmups = false;
                            foreach (WorkoutLogSerieModel l in hem.Sets.OrderBy(ei => ei.LogDate))
                            {
                                var hItem = new HistoryItem()
                                {
                                    WorkoutLogId = hm.Id,
                                    ExerciseId = hem.Exercise.Id,
                                    WorkoutLogSerieId = l.Id,
                                    Label = string.Format("{0:0.00} {1} x {2} {3}",
                                                 LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? l.Weight.Kg : l.Weight.Lb,
                                                 LocalDBManager.Instance.GetDBSetting("massunit").Value,
                                                 l.Reps, repString).ReplaceWithDot(),
                                    ItemType = HistoryItemType.SetType,
                                    Model = l
                                };
                                if (hem.Sets.FirstOrDefault().Exercice.IsFlexibility)
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                   "Body",
                                                 l.Reps, repString);
                                if (hem.Exercise.Id == 16508)
                                {
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                l.IsWarmups ? "Brisk" : "Fast",
                                                 l.Reps, repString).ReplaceWithDot();
                                }
                                else if (hem.Exercise.BodyPartId == 12)
                                {
                                    hItem.Label = string.Format("{0} x {1} {2}",
                                                l.IsWarmups ? "Brisk" : !isWarmups ? "Fast" : "Cooldown",
                                                 l.Reps, repString).ReplaceWithDot();
                                }
                                if (!l.IsWarmups)
                                    isWarmups = true;
                                if (l.IsWarmups && isWarmups)
                                    isWarmups = false;
                                historyModel.Add(hItem);
                            }
                            historyModel.Add(new HistoryItem()
                            {
                                WorkoutLogId = hm.Id,
                                ExerciseId = hem.Exercise.Id,
                                Label = hem.Sets.FirstOrDefault().Exercice.IsFlexibility ? string.Format("{0} {1} | {2} {3}",
                                                      hem.Sets.Count(), AppResources.Sets,
                                                      hem.Reps, repString) : string.Format("{0} {1} | {2} {3} | {4}",
                                                      hem.Sets.Count(), AppResources.Sets,
                                                      hem.Reps, repString,
                                                      LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
                                                            string.Format("{0:0.00} kg", hem.TotalWeight.Kg) :
                                                        string.Format("{0:0.00} lbs", hem.TotalWeight.Lb)).ReplaceWithDot(),
                                ItemType = HistoryItemType.StatisticType

                            });
                        }
                    }
                }

            }
            HistoryListView.ItemsSource = historyModel;
            if (historyModel.Count > 0)
            {
                PickerStack.IsVisible = true;
                LblTimeFrame.IsVisible = true;
                NoDataLabel.IsVisible = false;
                PickerStack.IsVisible = false;
                LblTimeFrame.IsVisible = false;
            }
            else
            {

                PickerStack.IsVisible = false;
                LblTimeFrame.IsVisible = false;
                NoDataLabel.IsVisible = true;
                // uncomment code please
                //await Task.Delay(300);
                //TooltipEffect.SetHasShowTooltip(ToolTipButton, true);

            }
        }
        catch (Exception ex)
        {

        }
    }
    async void updateHistory()
    {
        try
        {
            HistoryListView.ItemsSource = null;

            HistoryListView.ItemsSource = historyModel;
            if (historyModel.Count > 0)
            {
                PickerStack.IsVisible = true;
                LblTimeFrame.IsVisible = true;
                NoDataLabel.IsVisible = false;
            }
            else
            {

                PickerStack.IsVisible = false;
                LblTimeFrame.IsVisible = false;
                NoDataLabel.IsVisible = true;
                // uncomment code please
                //await Task.Delay(300);
                //TooltipEffect.SetHasShowTooltip(ToolTipButton, true);
            }
        }
        catch (Exception e)
        {
            ConnectionErrorPopup();

        }
    }
    private bool isPresented = false;
    protected async Task ConnectionErrorPopup()
    {
        if (isPresented)
            return;
        isPresented = true;
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
        isPresented = false;
    }
    private string _formatter(double d)
    {
        return IndexToDateLabel.ContainsKey(d) ? IndexToDateLabel[d] : "";
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();

        if (Config.ViewWebHistoryPopup == false)
        {
            await Task.Delay(300);
            Config.ViewWebHistoryPopup = true;
            var alert = await HelperClass.DisplayCustomPopup("History","All your workouts and sets. Select exercises at the bottom. View more stats on the Web.",
            "View more stats",AppResources.GotIt);
            
            alert.ActionSelected += (sender,action) => {
            if(action == PopupAction.OK){
                 Browser.OpenAsync("https://dashboard.dr-muscle.com/", BrowserLaunchMode.SystemPreferred);
            }
            };
            
            // ConfirmConfig ShowSorryPopUp = new ConfirmConfig()
            // {
            //     Title = "History",
            //     Message = "All your workouts and sets. Select exercises at the bottom. View more stats on the Web.",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "View more stats",
            //     CancelText = AppResources.GotIt,
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             //Move to history
            //             Browser.OpenAsync("https://dashboard.dr-muscle.com/", BrowserLaunchMode.SystemPreferred);
            //             //Device.OpenUri(new Uri("https://dashboard.dr-muscle.com/"));
            //         }
            //     }
            // };
            // UserDialogs.Instance.Confirm(ShowSorryPopUp);
        }

    }

    private async void LoadDefaultChartOfflineAvailable()
    {
        try
        {

            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            if (mainWorkoutLog == null)
            {

                if (workouts.Sets != null)
                {
                    GetDefaultChartAsync(workouts);
                }
            }
            else
            {
                GetDefaultChartAsync(mainWorkoutLog);
            }

            mainWorkoutLog = workouts == null ? await DrMuscleRestClient.Instance.GetUserWorkoutLogAverage() : await DrMuscleRestClient.Instance.GetUserWorkoutLogAverageWithoutLoader();
            GetDefaultChartAsync(mainWorkoutLog);

        }
        catch (Exception ex)
        {

        }
    }
    private async void GetDefaultChartAsync(GetUserWorkoutLogAverageResponse workoutLogAverage)
    {
        try
        {

            if (!workoutLogAverage.Averages.Any())
            {

                if (workoutLogAverage.AllExercises.Any())
                {

                    ExericsesPicker.Items.Clear();
                    ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
                    ExericsesPicker.SelectedIndex = 0;
                    try
                    {
                        ExercisesLabelToID = new Dictionary<string, long>();
                        foreach (ExerciceModel e in workoutLogAverage.AllExercises)
                        {
                            if (ExericsesPicker.Items.Count(i => i == e.Label) == 0)
                                ExericsesPicker.Items.Add(e.Label);
                            if (!ExercisesLabelToID.ContainsKey(e.Label))
                                ExercisesLabelToID.Add(e.Label, e.Id);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                }
                return;
            }
            else
            {
            }
            try
            {

                //if (workoutLogAverage == null)
                //return;
                if (workoutLogAverage == null || !workoutLogAverage.Averages.Any())
                {

                }
                else
                {
                    // NoDataLabel.IsVisible = false;

                    if (workoutLogAverage.AllExercises != null && ExericsesPicker.Items.Count != workoutLogAverage.AllExercises.Count + 1)
                    {
                        try
                        {
                            if (ExericsesPicker.Items.Count() > 0)
                            {
                                ExericsesPicker.Items.Clear();
                            }

                            ExericsesPicker.Items.Add(AppResources.AverageOfAllRecentExercises);
                            ExericsesPicker.SelectedIndex = 0;
                            ExercisesLabelToID = new Dictionary<string, long>();
                        
                            foreach (ExerciceModel e in workoutLogAverage.AllExercises)
                            {
                                if (ExericsesPicker.Items.Count(i => i == e.Label) == 0)
                                    ExericsesPicker.Items.Add(e.Label);
                                if (!ExercisesLabelToID.ContainsKey(e.Label))
                                    ExercisesLabelToID.Add(e.Label, e.Id);
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                    }
                }
            }
            catch (Exception e)
            {
                var properties = new Dictionary<string, string>
                    {
                        { "HistoryPage_ChartPage", $"{e.StackTrace}" }
                    };
                // uncomment code please
                //Crashes.TrackError(e, properties);
            }
        }
        catch (Exception e)
        {
            var properties = new Dictionary<string, string>
                    {
                        { "HistoryPage_ChartPage", $"{e.StackTrace}" }
                    };
            // uncomment code please
            //Crashes.TrackError(e, properties);
        }
    }

    private void RemoveWorkoutLog(long workoutLogId)
    {
        try
        {

            List<HistoryItem> toDelete = new List<HistoryItem>();
            foreach (HistoryItem hi in historyModel.Where(h => h.WorkoutLogId == workoutLogId))
                toDelete.Add(hi);

            foreach (HistoryItem hi in toDelete)
                historyModel.Remove(hi);

        }
        catch (Exception ex)
        {

        }
    }
    public async void OnDeleteWorkoutLogClicked(object sender, EventArgs e)
    {
        try
        {

            var mi = ((Button)sender);
            HistoryItem m = (HistoryItem)mi.CommandParameter;

            BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutLog(new DeleteWorkoutLogExerciseModel() { WorkoutLogId = (long)m.WorkoutLogId });
            if (result.Result)
            {
                RemoveWorkoutLog((long)m.WorkoutLogId);
            }

        }
        catch (Exception ex)
        {

        }
    }

    private void RemoveWorkoutLogExercise(long workoutLogId, long exerciseId)
    {
        try
        {

            List<HistoryItem> toDelete = new List<HistoryItem>();
            foreach (HistoryItem hi in historyModel.Where(h => h.WorkoutLogId == workoutLogId && h.ExerciseId == exerciseId))
                toDelete.Add(hi);

            foreach (HistoryItem hi in toDelete)
                historyModel.Remove(hi);

            if (historyModel.Count(h => h.WorkoutLogId == workoutLogId && h.ExerciseId != null) == 0)
                RemoveWorkoutLog(workoutLogId);

            if (Device.RuntimePlatform.Equals(Device.Android))
                updateHistory();
        }
        catch (Exception ex)
        {

        }
    }

    public async void OnDeleteWorkoutLogExerciseClicked(object sender, EventArgs e)
    {
        try
        {

            var mi = ((Button)sender);
            HistoryItem m = (HistoryItem)mi.CommandParameter;

            BooleanModel result = await DrMuscleRestClient.Instance.DeleteUserWorkoutLogExercise(new DeleteWorkoutLogExerciseModel() { WorkoutLogId = (long)m.WorkoutLogId, WorkoutLogExerciseId = (long)m.ExerciseId });
            if (result.Result)
            {
                RemoveWorkoutLogExercise((long)m.WorkoutLogId, (long)m.ExerciseId);
            }


        }
        catch (Exception ex)
        {

        }
    }

    public async void OnEditClicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);
        HistoryItem m = (HistoryItem)mi.CommandParameter;
        var model = (WorkoutLogSerieModel)m.Model;
        var newWorkOutLog = new WorkoutLogSerieModel()
        {
            Id = model.Id,
            Reps = model.Reps,
            Weight = model.Weight,
            Exercice = model.Exercice,
            UserId = model.UserId
        };


        //Edit workout log
        var massUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? "lbs" : "kg";
        CustomPromptConfig customPromptConfig = new CustomPromptConfig($"{AppResources.Edit} {model.Exercice.Label}","",AppResources.Edit,AppResources.Cancel,$"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
        Keyboard.Numeric,LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? Math.Round(model.Weight.Lb, 2).ToString().ReplaceWithDot() : Math.Round(model.Weight.Kg, 2).ToString().ReplaceWithDot());
        await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);

        customPromptConfig.ActionSelected += (sender1, action) =>
        {
            if (action == PopupAction.OK)
            {
                PromptResult result = new PromptResult(true, customPromptConfig.text);
                if (string.IsNullOrWhiteSpace(customPromptConfig.text) || Convert.ToDecimal(customPromptConfig.text, CultureInfo.InvariantCulture) < 0)
                {
                    return;
                }
                try
                {
                    var weightText = customPromptConfig.text.Replace(",", ".");
                    decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
                    newWorkOutLog.Weight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                    AskForEditReps(newWorkOutLog);
                }
                catch (Exception ex){

                }
            }
            
                
        };
        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = Device.RuntimePlatform.Equals(Device.Android) ? InputType.Phone : InputType.DecimalNumber,
        //     IsCancellable = true,

        //     Title = $"{AppResources.Edit} {model.Exercice.Label}",

        //     Message = $"{AppResources.EnterWeights} {AppResources._in} {massUnit}",
        //     Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "lb" ? Math.Round(model.Weight.Lb, 2).ToString().ReplaceWithDot() : Math.Round(model.Weight.Kg, 2).ToString().ReplaceWithDot(),

        //     OkText = AppResources.Edit,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
        //         {
        //             return;
        //         }
        //         var weightText = weightResponse.Value.Replace(",", ".");
        //         decimal weight1 = Convert.ToDecimal(weightText, CultureInfo.InvariantCulture);
        //         newWorkOutLog.Weight = new MultiUnityWeight(weight1, LocalDBManager.Instance.GetDBSetting("massunit").Value);
        //         AskForEditReps(newWorkOutLog);

        //     }
        // };
        // firsttimeExercisePopup.OnTextChanged += FirsttimeExercisePopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);
        //}
    }

    private void FirsttimeExercisePopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
        var text = obj.Value.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    async void AskForEditReps(WorkoutLogSerieModel newWorkOutLog)
    {

        CustomPromptConfig customPromptConfig = new CustomPromptConfig(string.Format("{0} {1}", AppResources.Edit, newWorkOutLog.Exercice.Label),
        AppResources.TapToEnterHowMany,
        AppResources.Edit,AppResources.Cancel,AppResources.EnterNewReps,
        Keyboard.Numeric,newWorkOutLog.Reps.ToString());
        await Application.Current.MainPage.ShowPopupAsync(customPromptConfig);
        customPromptConfig.ActionSelected += async (sender1, action) =>
        {
            if (action == PopupAction.OK)
            {
            PromptResult r = new PromptResult(true, customPromptConfig.text);
            if (string.IsNullOrWhiteSpace(customPromptConfig.text) || Convert.ToDecimal(customPromptConfig.text, CultureInfo.InvariantCulture) < 0)
            {
                return;
            }
            try
            {
                  int reps = Convert.ToInt32(customPromptConfig.text, CultureInfo.InvariantCulture);
                    newWorkOutLog.Reps = reps;
                    BooleanModel result = await DrMuscleRestClient.Instance.EditWorkoutLogSeries(newWorkOutLog);
                    if (result.Result)
                    {
                        ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
                        GetHistory();
                    }
            }
            catch (Exception ex){

            }
            }
            
        };
        // PromptConfig firsttimeExercisePopup = new PromptConfig()
        // {
        //     InputType = InputType.Number,
        //     IsCancellable = true,

        //     Title = string.Format("{0} {1}", AppResources.Edit, newWorkOutLog.Exercice.Label),
        //     Message = AppResources.EnterNewReps,
        //     Placeholder = AppResources.TapToEnterHowMany,
        //     Text = newWorkOutLog.Reps.ToString(),
        //     OkText = AppResources.Edit,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     OnAction = async (weightResponse) =>
        //     {
        //         if (string.IsNullOrWhiteSpace(weightResponse.Value) || Convert.ToDecimal(weightResponse.Value, CultureInfo.InvariantCulture) < 1)
        //         {
        //             return;
        //         }
        //         try
        //         {
        //             int reps = Convert.ToInt32(weightResponse.Value, CultureInfo.InvariantCulture);
        //             newWorkOutLog.Reps = reps;
        //             BooleanModel result = await DrMuscleRestClient.Instance.EditWorkoutLogSeries(newWorkOutLog);
        //             if (result.Result)
        //             {
        //                 ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
        //                 GetHistory();
        //             }
        //         }
        //         catch (Exception ex)
        //         {

        //         }
        //     }
        // };
        // firsttimeExercisePopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
        // UserDialogs.Instance.Prompt(firsttimeExercisePopup);
    }
    protected void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:)?$";
        bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }
    private void RemoveWorkoutLogSerie(HistoryItem hi)
    {
        historyModel.Remove(hi);
        int nbSets = historyModel.Count(h => h.WorkoutLogId == hi.WorkoutLogId && h.ExerciseId == hi.ExerciseId && hi.ItemType == HistoryItemType.SetType);

        // Il y a visiblement un bug dans Linq, �a devrait ramener 0 mais �a ram�ne 2 item de type Date et Statistic... Etrange
        if (nbSets == 2)
        {
            HistoryItem statItem = historyModel.FirstOrDefault(s => s.WorkoutLogId == hi.WorkoutLogId && s.ExerciseId == hi.ExerciseId && s.ItemType == HistoryItemType.StatisticType);
            if (statItem != null)
            {
                historyModel.Remove(statItem);
            }
            if (hi != null && hi.WorkoutLogId != null && hi.ExerciseId != null)
            {
                RemoveWorkoutLogExercise((long)hi.WorkoutLogId, (long)hi.ExerciseId);
            }
        }
        else
        {
            updateHistory();
        }
    }

    public async void OnDeleteWorkoutLogSerieClicked(object sender, EventArgs e)
    {
        try
        {
            var mi = ((Button)sender);
            if (mi == null || mi.CommandParameter == null)
                return;

            HistoryItem m = (HistoryItem)mi.CommandParameter;
            if (m == null || m.Model == null)
                return;

            BooleanModel result = await DrMuscleRestClient.Instance.DeleteWorkoutLogSeries((WorkoutLogSerieModel)m.Model);
            if (result.Result)
            {
                RemoveWorkoutLogSerie(m);
            }
        }
        catch (Exception ex)
        {
            // Log or display the exception details
            Console.WriteLine($"Exception: {ex.Message}");
        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        //BackgroundImage.Source = null;
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;
        HistoryItem m = (HistoryItem)((BindableObject)sender).BindingContext;
        if (m.ItemType != HistoryItemType.ExerciseType)
        {
            ((ViewCell)sender).Height = 20;
        }

    }

    async void ButtonStartWorkout_Clicked(object sender, System.EventArgs e)
    {
        //PagesFactory.PushAsync<ChooseDrMuscleOrCustomPage>();
        try
        {
            if (App.Current.MainPage.Navigation.NavigationStack.First() is MainTabbedPage)
                ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];

        }
        catch (Exception ex)
        {

        }

        await Navigation?.PopToRootAsync();
        if (Device.RuntimePlatform == Device.iOS)
            await Task.Delay(100);
        Device.BeginInvokeOnMainThread(() =>
        {
            MessagingCenter.Send<StartNormalWorkout>(new StartNormalWorkout() { }, "StartNormalWorkout");
        });
    }
    public class HistoryItem
    {
        public object Model { get; set; }
        public long? WorkoutLogId { get; set; }
        public long? ExerciseId { get; set; }
        public long? WorkoutLogSerieId { get; set; }
        public string Label { get; set; }
        public HistoryItemType ItemType { get; set; }
        public string TotalSets { get; set; }
        public string TotalReps { get; set; }
        public string TotalWeight { get; set; }
        public string AverageWeightPerRep { get; set; }
    }

    public class LineTypeToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            switch (value.ToString().ToLower())
            {
                case "DATE":
                    return Colors.Black;
                case "EXERCISE":
                    return Colors.Gray;
                case "SET":
                    return Colors.Transparent;
            }

            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, System.Globalization.CultureInfo culture)
        {
            // You probably don't need this, this is used to convert the other way around
            // so from color to yes no or maybe
            return value;
            //throw new NotImplementedException();
        }
    }

    public enum HistoryItemType
    {
        DateType,
        ExerciseType,
        SetType,
        StatisticType
    }

    private void ExericsesPicker_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            if (Device.RuntimePlatform == Device.Android)
            {
                if (ExericsesPicker.SelectedIndex >= 0 && ExericsesPicker.Items.Count > 1)
                {
                    if (ExericsesPicker.SelectedIndex == 0)
                    {
                        DatePicker.Items.Clear();
                        DatePicker.Items.Add(AppResources.Last3Workouts);
                        //DatePicker.Items.Add(AppResources.AllTime);
                        DatePicker.SelectedIndex = 0;
                        LoadDefaultChartOfflineAvailable();
                        GetHistory();
                        return;
                    }
                    else
                    {
                        int currentDatePickerIndex = DatePicker.SelectedIndex;
                        DatePicker.Items.Clear();
                        foreach (var i in DurationToDays)
                            DatePicker.Items.Add(i.Key);
                        DatePicker.Items.RemoveAt(0);
                        DatePicker.SelectedIndex = currentDatePickerIndex;
                    }

                    string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
                    long exerciseId = ExercisesLabelToID[selected];

                    GetHistory();
                }
            }            
        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }

    private void DatePicker_SelectedIndexChanged(object sender, EventArgs e)
    {
        try
        {
            if (Device.RuntimePlatform == Device.Android)
            {
                if (ExericsesPicker.SelectedIndex == 0 && DatePicker.SelectedIndex == 1)
                {
                    GetHistory();
                    return;
                }
                else if (ExericsesPicker.SelectedIndex == 0)
                {
                    LoadDefaultChartOfflineAvailable();
                    return;
                }

                if (ExericsesPicker.SelectedIndex >= 0 && DatePicker.SelectedIndex >= 0)
                {
                    string selected = ExericsesPicker.Items[ExericsesPicker.SelectedIndex];
                    long exerciseId = ExercisesLabelToID[selected];
                    GetHistory();
                }
            }

        }
        catch (Exception ex)
        {
            ConnectionErrorPopup();

        }
    }

    private void ExericsesPicker_Focused(object sender, FocusEventArgs e)
    {
        // Check if the Picker has data
        if (ExericsesPicker.Items == null || ExericsesPicker.Items.Count == 1)
        {
            // Prevent the picker from opening by unfocusing it
            ExericsesPicker.Unfocus();
        }
    }
}