﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.Android.Dependencies;
using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//[assembly: Dependency(typeof(SQLite_Android))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class SQLite_Android : ISQLite
    {
        public SQLite_Android() { }
        //public SQLite.SQLiteConnection GetConnection()
        //{
        //    var sqliteFilename = "dmmdb.db3";
        //    string documentsPath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.Personal); // Documents folder
        //    var path = Path.Combine(documentsPath, sqliteFilename);
        //    // Create the connection
        //    var conn = new SQLite.SQLiteConnection(path);
        //    // Return the database connection
        //    return conn;
        //}
        public SQLite.SQLiteConnection GetConnection()
        {
            var sqliteFilename = "dmmdb.db3";
            string documentsPath = System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData); // Local app data folder
            var dbPath = Path.Combine(documentsPath, sqliteFilename); // Full path to the database file

            try
            {
                Directory.CreateDirectory(documentsPath);
                // Create the connection
                var conn = new SQLite.SQLiteConnection(dbPath);
                // Return the database connection
                return conn;
            }
            catch (SQLiteException ex)
            {
                // Handle exceptions
                Console.WriteLine($"SQLiteException: {ex.Message}");
                return null;
            }
        }
    }
}
