<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
             x:Name="WelcomeAIOverlayPage"
               xmlns:control="clr-namespace:DrMaxMuscle.Controls"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
               xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Views.WelcomeAIOverlay">
    <ContentPage.Content>
        <ScrollView Grid.Row="0" x:Name="scrollView" Margin="0" Padding="0" HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
            <StackLayout Spacing="0" >
                <Grid >

                    <Frame
            Margin="0"
        Padding="0"
        CornerRadius="0"
        Grid.Row="0"
        HasShadow="False"
        IsClippedToBounds="True">

                        <ffimageloading:CachedImage
            x:Name="ImgGender"
            Source="bottom_two.png"
            HeightRequest="225"
            VerticalOptions="Start"
            Aspect="AspectFill" />

                    </Frame>
                    <Image Source="close_gray.png" WidthRequest="50" Aspect="AspectFit" Grid.Row="0" VerticalOptions="Start" HorizontalOptions="End" Margin="0,10,8,0" >
                        <Image.GestureRecognizers>
                            <TapGestureRecognizer Tapped="Close_Tapped" />
                        </Image.GestureRecognizers>
                    </Image>


                </Grid>

                <!--Cards-->

                <control:CustomFrame
            Margin="10,10,10,10"
        Padding="10,5,10,5"
        CornerRadius="4"
        Grid.Row="0"
        HasShadow="True"
        IsClippedToBounds="True" >
                    <StackLayout>
                        <Label
                    LineBreakMode="WordWrap"
                    x:Name="LblGptTitle"
                    Text=""
                    HorizontalOptions="Start"
                    VerticalOptions="Start"
                    TextColor="Black"
                    BackgroundColor="Transparent"
                    FontAttributes="Bold"
                    Margin="0,5,10,12"
                    FontSize="24" />
                        <StackLayout
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand">

                            <!--<Label Text=""  Margin="0,4,10,5  " Style="{StaticResource LabelStyle}" FontSize="15" />-->
                            <Grid HorizontalOptions="CenterAndExpand" Padding="0,0,5,10" RowSpacing="0">
                                <Grid.RowDefinitions>

                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>

                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Label x:Name="LblGptDesc" Text=""
                           FontSize="17"
                           LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                           TextColor="#26262B" />
                            </Grid>
                        </StackLayout>
                        <Label x:Name="LblAssistUser" IsVisible="false" Grid.Row="1"
                       FontSize="17"
                        LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                        TextColor="#26262B"
                       Text="" 
                        />

                        <StackLayout
                    Margin="0,20,0,0"
                    Spacing="0"
                    IsVisible="false"
                    x:Name="btnStack">

                            <!--<Frame
                    Padding="2"
                    Margin="0,0,0,6"
                    BackgroundColor="White"
                    IsClippedToBounds="true"
                    BorderColor="#143D57"
                    HasShadow="False"
                    CornerRadius="6"
                    VerticalOptions="Start"
                    x:Name="BtnInspiredMe"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="48">

                                <Label 
                        Margin="0"
                        BackgroundColor="White"
                        FontSize="13"
                        FontAttributes="Bold"
                        HorizontalOptions="CenterAndExpand"
                        Text="Inspire me"
                        TextColor="#143D57"
                        VerticalOptions="Center" />
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="InspireMe_Clicked"/>
                                </Frame.GestureRecognizers>
                            </Frame>-->

                            <!--<Button 
                                Text="Continue"
                                TextColor="White"
                                BackgroundColor="#143D57"
                                HeightRequest="55"
                                BorderWidth="2"
                                BorderColor="#143D57"
                                Margin="0,4,0,0"
                                FontSize="15"
                                FontAttributes="Bold"
                                Clicked="HelpWithGoal_Clicked"
                                CornerRadius="0"/>-->


                            <Frame
                                Padding="0"
                                Margin="0,0,0,8"
                                IsClippedToBounds="true"
                                CornerRadius="0"
                                BorderColor="Transparent"
                                HorizontalOptions="FillAndExpand" 
                                HeightRequest="60"> 
                                <StackLayout
                                    HeightRequest="60"
                                    Padding="0"
                                    Margin="0"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="FillAndExpand"
                                    >
                                    <StackLayout.Background>
                                        <LinearGradientBrush EndPoint="1,0">
                                            <GradientStop Color="#0C2432" Offset="0.0" />
                                            <GradientStop Color="#195276" Offset="1.0" />
                                        </LinearGradientBrush>
                                    </StackLayout.Background>

                                    <Button
                                        HeightRequest="60" 
                                        Text="Continue" 
                                        BackgroundColor="Transparent"
                                        BorderColor="Transparent"
                                        TextColor="White" Style="{StaticResource buttonStyle}" 
                                        HorizontalOptions="FillAndExpand" 
                                        Clicked="HelpWithGoal_Clicked" 
                                        Margin="0,0,0,5" >
                                    </Button>
                                </StackLayout>
                            </Frame>


                            <Button 
                                Text="Share"
                                TextColor="#195377"
                                BackgroundColor="Transparent"
                                HeightRequest="55"
                                BorderWidth="2"
                                BorderColor="#195377"
                                Margin="0,0,0,5" 
                                CornerRadius="0"
                                FontSize="15"
                                FontAttributes="Bold"
                                Clicked="BtnShare_Clicked"/>

                            <!--<Frame
                    Padding="2"
                    Margin="0,0,0,6"
                    BackgroundColor="White"
                    IsClippedToBounds="true"
                    BorderColor="#143D57"
                    HasShadow="False"
                    CornerRadius="6"
                    VerticalOptions="Start"
                    x:Name="BtnHelpWithGoal"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="48">

                                <Label 
                        Margin="0"
                        BackgroundColor="White"
                        FontSize="13"
                        FontAttributes="Bold"
                        HorizontalOptions="CenterAndExpand"
                        Text="Help with goal"
    
                        TextColor="#143D57"
                        VerticalOptions="Center" />
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="HelpWithGoal_Clicked"/>
                                </Frame.GestureRecognizers>
                            </Frame>-->
                            <!--<Border
    Stroke="Transparent"
    StrokeShape="RoundRectangle 6,6,6,6"
    Padding="0"
    Margin="0"
    BackgroundColor="Transparent"
   VerticalOptions="Start"
    x:Name="BtnAiCHat"
    HorizontalOptions="FillAndExpand"
    HeightRequest="48">
                                <StackLayout
        HeightRequest="48"
        Padding="2"
        Margin="0"
        VerticalOptions="FillAndExpand"
        HorizontalOptions="FillAndExpand"
        Style="{StaticResource GradientStackStyleBlue}"
        >
                                    <t:DrMuscleButton
            BackgroundColor="Transparent"
            BorderWidth="0"
                                        Margin="0,6,0,0"
            FontSize="13"
            FontAttributes="Bold"
            HorizontalOptions="FillAndExpand"
            Text="Something else"
            Clicked="OpenChat_Clicked"
            VerticalOptions="Center"
            TextColor="White" />
                                </StackLayout>
                            </Border>-->
                        </StackLayout>
                    </StackLayout>
                </control:CustomFrame>



                <Frame
                x:Name="continueBtnFrame"
                IsVisible="false"
            Margin="10,10,10,10"
        Padding="0,10,0,0"
        CornerRadius="4"
        Grid.Row="0"
        HasShadow="False"
        IsClippedToBounds="True" >
                    <StackLayout>
                        <Frame
            Padding="0"
            Margin="10,0,10,8"
            IsClippedToBounds="true"
              x:Name="previewButton"
            CornerRadius="0"
            Style="{StaticResource GradientFrameStyleBlue}"
                    HorizontalOptions="FillAndExpand" 
                    HeightRequest="60">


                            <t:DrMuscleButton x:Name="ChooseworkoutButton"
                              HeightRequest="60" Text="Continue" BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand" Clicked="Close_Tapped" Margin="0,0,0,5" ></t:DrMuscleButton>
                        </Frame>


                    </StackLayout>
                </Frame>

            </StackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>
