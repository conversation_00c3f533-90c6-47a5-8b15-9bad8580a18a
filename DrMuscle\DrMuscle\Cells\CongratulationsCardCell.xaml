﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
          xmlns:controls="clr-namespace:DrMuscle.Controls"
             x:Class="DrMuscle.Cells.CongratulationsCardCell">
  <ViewCell.View>
      <controls:CustomFrame
                            x:Name="WeightProgress2"
                            Margin="10,10,10,10"
                            Padding="0,10,10,10"
                            CornerRadius="12"
                            HasShadow="False">

            <StackLayout Padding="10,15,10,5">
                
                <Grid
                                        Margin="0,0,0,0">
                                        <Grid.RowDefinitions>
                                            <RowDefinition
                                                Height="*" />
                                            
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition
                                                Width="40" />
                                            <ColumnDefinition
                                                Width="*" />
                                        </Grid.ColumnDefinitions>

                                        <Image
                                            Source="starTrophy.png"
                                            Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
                                            Grid.Row="0"
                                            WidthRequest="27"
                                            VerticalOptions="Start"
                                            HeightRequest="27" />
                                        <StackLayout
                                            Grid.Column="1"
                                            Grid.Row="0"
                                            >
                                            <Label
                                                x:Name="LblStrengthUp"
                                                Text="{Binding Part1}"
                                                Margin="0,-9,0,0"
                                                TextColor="Black"
                                                FontAttributes="Bold"
                                                FontSize="19" />
                                            
                                        </StackLayout>
                                    </Grid>
                
            </StackLayout>
        </controls:CustomFrame>
  </ViewCell.View>
</ViewCell>
