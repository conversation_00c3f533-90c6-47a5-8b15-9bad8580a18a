<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.SummaryPage"
              xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:local="clr-namespace:DrMaxMuscle"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:localize="clr-namespace:DrMaxMuscle.Resx"
             xmlns:locali="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:cells="clr-namespace:DrMaxMuscle.Cells"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
             xmlns:heaer="clr-namespace:DrMaxMuscle.Screens.Workouts"
             ios:Page.UseSafeArea="False"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
                xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             Title="Summary page 2">

             <ContentPage.Resources>
        <ResourceDictionary>
            <cells:BotDataTemplateSelector
        x:Key="BotTemplateSelector">
            </cells:BotDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>

        <Grid
            x:Name="mainGrid"
            BackgroundColor="#f4f4f4"
            AbsoluteLayout.LayoutFlags="All"
            AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            RowSpacing="0"
            Margin="0"
            Padding="0">
            <Grid.RowDefinitions>
                <RowDefinition
                    x:Name="StatusBarHeight"
                    Height="0" />
                <RowDefinition
                    Height="Auto" />
                <RowDefinition
                    Height="*" />
                <RowDefinition
                    Height="0" />
                <RowDefinition
                    Height="Auto" />
            </Grid.RowDefinitions>

         
<ScrollView Grid.Row="2" x:Name="TempListScroll">
                <CollectionView
                x:Name="TempList"
                
                BackgroundColor="White"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                VerticalOptions="Start"
                IsVisible="false"/> 
                <!--<ListView
                x:Name="TempList"
                
                BackgroundColor="White"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                HasUnevenRows="True"
                VerticalOptions="Start"
                IsVisible="false"
                SeparatorColor="Transparent"/>-->
            </ScrollView>

             <CollectionView
                Grid.Row="2"
                Margin="0,10,0,15"
                BackgroundColor="#f4f4f4"
                ItemTemplate="{StaticResource BotTemplateSelector}"
                x:Name="lstChats"
                VerticalOptions="FillAndExpand"
                >
                <CollectionView.Footer>
                    <StackLayout
                        x:Name="bottomBtns"
                        Padding="30,0"
                        Spacing="10">
                        <Frame
    HeightRequest="60"
    BackgroundColor="Transparent"
    Margin="0"
    Padding="2"
    BorderColor="#195377"    
    CornerRadius="0"
    HasShadow="False">
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer
                                    NumberOfTapsRequired="1"
                                    Tapped="ButtonShare_Clicked"/>
                            </Frame.GestureRecognizers>
                            <Grid
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

                                <StackLayout
            Grid.Row="0"
            Grid.Column="0"
            HorizontalOptions="Center"
            VerticalOptions="FillAndExpand"
            Orientation="Horizontal"
            Spacing="8"
            Margin="-26,0,0,0"
            BackgroundColor="Transparent">

                                    <Image               
                x:Name="Img"
                Source="ic_share_exercise"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Center"
                Margin="0,20"/>

                                    <Label                
                x:Name="LblText"
                Text="Share"
                FontSize="15"
                HorizontalOptions="Center"
                VerticalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                TextColor="#195377"
                FontAttributes="Bold"
                BackgroundColor="Transparent"/>
                                </StackLayout>
                            </Grid>
                        </Frame>
                        <Border
                            Stroke="Transparent"
                            StrokeShape="RoundRectangle 2,2,2,2"
                            Padding="0"
                            Style="{StaticResource GradientBorderStyleBlue}"

                            Margin="0"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            x:Name="btnMoveToHome"
                            HorizontalOptions="FillAndExpand">

                            <t:DrMuscleButton
                                x:Name="HomeButton"
                                VerticalOptions="Center"
                                HeightRequest="66"
                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                CornerRadius="2"
                                HorizontalOptions="FillAndExpand"
                                Text="Back to Home"
                                Clicked="MoveToHome"
                                IsVisible="true"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White" />
                        </Border>
                    </StackLayout>
                </CollectionView.Footer>
            </CollectionView>
               </Grid>
</ContentPage.Content>
</ContentPage>