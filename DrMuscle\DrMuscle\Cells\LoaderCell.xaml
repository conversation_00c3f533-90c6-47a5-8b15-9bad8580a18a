<?xml version="1.0" encoding="UTF-8"?>
<ViewCell xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="DrMuscle.Cells.LoaderCell" xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms">
    <Frame Margin="10,1,10,1" CornerRadius="6" Padding="5,0,5,0" HorizontalOptions="Start" BorderColor="Transparent" OutlineColor="Transparent" BackgroundColor="#80000000">
        <ffimageloading:CachedImage HorizontalOptions="FillAndExpand" x:Name="ImgLoader" Aspect="AspectFit" Source="resource://DrMuscle.Image.typing_loader.gif" WidthRequest="80" HeightRequest="50" />
    </Frame>
</ViewCell>