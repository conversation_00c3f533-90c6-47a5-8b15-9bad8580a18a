﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.KenkoSingleExercisePage"
              xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:local="clr-namespace:DrMaxMuscle"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:localize="clr-namespace:DrMaxMuscle.Resx"
             xmlns:locali="clr-namespace:DrMaxMuscle.Helpers"
             xmlns:cells="clr-namespace:DrMaxMuscle.Cells"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
             xmlns:heaer="clr-namespace:DrMaxMuscle.Screens.Workouts"
             ios:Page.UseSafeArea="False"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
                xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             Title="KenkoSingleExercisePage">
   <ContentPage.Resources>
       <ResourceDictionary>
            <converter:IdToBodyPartConverter x:Key="IdToBodyConverter" />
            <converter:IdToTransparentBodyPartConverter x:Key="IdToTransBodyConverter" />
        </ResourceDictionary>
   </ContentPage.Resources>
        <Grid x:Name="NavGrid" IgnoreSafeArea="True" BackgroundColor="#D8D8D8" RowSpacing="0" Padding="0,0,0,8" >
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="StatusBarHeight" Height="20" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />

                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Image Source="nav.png" x:Name="navView"  Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" VerticalOptions="Start" Aspect="AspectFill" Grid.RowSpan="3" />

                        <StackLayout Grid.Row="1" Grid.Column="0" BackgroundColor="Transparent" Grid.ColumnSpan="2" Spacing="8" Padding="{OnPlatform Android='15,0,20,0', iOS='8,0,8,0'}" HorizontalOptions="FillAndExpand" VerticalOptions="Start" Orientation="Horizontal">
                            
                            <ImageButton Source="android_back.png" Aspect="AspectFit"  WidthRequest="{OnPlatform Android='45', iOS='60'}" HeightRequest="40" BackgroundColor="Transparent" HorizontalOptions="Start" VerticalOptions="Center" Clicked="Back_Clicked">
                            </ImageButton>
                            <Label x:Name="LblWorkoutName" FontAutoScalingEnabled="True" HorizontalOptions="FillAndExpand" TextColor="White" VerticalOptions="Center" VerticalTextAlignment="Center" LineBreakMode="TailTruncation" Style="{StaticResource BoldLabelStyle}" FontSize="24" />
                            <HorizontalStackLayout Spacing="0" WidthRequest="{OnPlatform Android='90', iOS='100'}" HorizontalOptions="End" >
                            <t:DrMuscleButton Padding="0" Margin="0" ImageSource="plate.png" WidthRequest="{OnPlatform Android='43', iOS='48'}" Clicked="PlateTapped" BackgroundColor="Transparent" HeightRequest="40" HorizontalOptions="End" VerticalOptions="Start">
                                
                            </t:DrMuscleButton>

                <t:DrMuscleButton x:Name="BtnTimer" Text="" ImageSource="stopwatch.png" WidthRequest="{OnPlatform Android='43', iOS='48'}" HeightRequest="40" TextColor="White" BackgroundColor="Transparent" HorizontalOptions="End" VerticalOptions="Start" Clicked="TimerTapped" Padding="0" Margin="0"  FontSize="24">
                                
                            </t:DrMuscleButton>
                                </HorizontalStackLayout>
                        </StackLayout>
                        <StackLayout Grid.Row="2" Grid.Column="0" Padding="{OnPlatform Android='15,-3,20,10', iOS='8,-3,8,10'}" Grid.ColumnSpan="2" VerticalOptions="Start" Orientation="Horizontal">
                            
                            <Label Text="In progress" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" VerticalOptions="Center" FontSize="16" TextColor="#AAFFFFFF">
                            </Label>
                        </StackLayout>

                        <!--<t:DrMuscleListViewCache  x:Name="ExerciseListView" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" HasUnevenRows="True" BackgroundColor="#D8D8D8" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" SeparatorVisibility="None" SeparatorColor="Transparent" IsGroupingEnabled="True" Header="{Binding}" ItemTemplate="{StaticResource SetDataTemplateSelector}" Footer="" GroupHeaderTemplate="{StaticResource kenkoHeaderDataTemplateSelector}" ios:ListView.GroupHeaderStyle="Grouped">
                            <t:DrMuscleListViewCache.GestureRecognizers>
                                <TapGestureRecognizer Tapped="ListTapped" />
                            </t:DrMuscleListViewCache.GestureRecognizers>
                        </t:DrMuscleListViewCache>-->
                        <ScrollView x:Name="ExerciseListScroll" Margin="0,7,0,7" Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" BackgroundColor="#D8D8D8" >
                <StackLayout x:Name="ExerciseListView" BindableLayout.ItemsSource="{Binding exerciseItems}"  Margin="0,0,0,0" Spacing="0">
                    <BindableLayout.ItemTemplate>
                        <DataTemplate>
                            <StackLayout Spacing="0">
                                <Grid IsClippedToBounds="True" BackgroundColor="Transparent" HeightRequest="115" BindingContextChanged="OnBindingContextChanged">
                                    <Grid.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="CellHeaderTapped" />
                                    </Grid.GestureRecognizers>
                                    <Frame
                        Grid.Row="0" 
                        Padding="0"
                        IsClippedToBounds="true"
                        CornerRadius="4"
                        BorderColor="Transparent"
                        Style="{StaticResource GradientFrameStyleBlue}"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand"
                        Margin="4,10,4,0">

                                        <Frame.Triggers>
                                            <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="false">
                                                <Setter Property="IsVisible" Value="true" />
                                            </DataTrigger>
                                            <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="true">
                                                <Setter Property="IsVisible" Value="false" />
                                            </DataTrigger>
                                        </Frame.Triggers>
                                    </Frame>

                                    <ffimageloading:CachedImage Grid.Row="0" Source="{Binding BodyPartId, Converter={StaticResource IdToTransBodyConverter}}" Aspect="Fill" Margin="4,10,4,0"/>
                                    <Frame Margin="4,10,4,0" IsClippedToBounds="True" Grid.Row="0" HasShadow="False" CornerRadius="4" BorderColor="Transparent" HeightRequest="115" Padding="20,16">
                                        <Frame.Triggers>
                                            <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="true">
                                                <Setter Property="Margin" Value="8,10,8,0" />
                                                <Setter Property="BackgroundColor" Value="White" />
                                                <Setter Property="Padding" Value="15,5,5,10" />
                                            </DataTrigger>

                                            <DataTrigger TargetType="Frame" Binding="{Binding IsFrameBackground}" Value="false">
                                                <Setter Property="Margin" Value="4,10,4,0" />
                                                <Setter Property="Padding" Value="8,10,5,10" />
                                                <Setter Property="BackgroundColor" Value="Transparent" />
                                            </DataTrigger>

                                        </Frame.Triggers>
                                        <StackLayout >
                                            <StackLayout Orientation="Horizontal" VerticalOptions="CenterAndExpand">
                                                <StackLayout.Triggers>
                                                    <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="false">
                                                        <Setter Property="IsVisible" Value="true" />
                                                    </DataTrigger>

                                                    <DataTrigger TargetType="StackLayout" Binding="{Binding IsFrameBackground}" Value="true">
                                                        <Setter Property="IsVisible" Value="false" />
                                                    </DataTrigger>
                                                </StackLayout.Triggers>
                                                <Image Source="done2.png" WidthRequest="18" Aspect="AspectFit" HorizontalOptions="Start" VerticalOptions="FillAndExpand" IsVisible="{Binding IsFinished}" Margin="7,0,0,0"/>
                                                <ffimageloading:CachedImage Source="{Binding BodyPartId, Converter={StaticResource IdToBodyConverter}}" HeightRequest="90" WidthRequest="65" Aspect="AspectFit">

                                                </ffimageloading:CachedImage>
                                                <StackLayout Spacing="0" VerticalOptions="Center"  HorizontalOptions="FillAndExpand" Margin="0,0,0,8">
                                                    <controls:AutoSizeLabel HorizontalOptions="FillAndExpand"  VerticalTextAlignment="Center" LineBreakMode="TailTruncation"  TextColor="#FFFFFF" FontSize="19" MaxLines="3"  FontAttributes="Bold" >
                                                        <controls:AutoSizeLabel.FormattedText>
                                                            <FormattedString>
                                                                <Span Text="{Binding CountNo}" />
                                                                <Span Text="&#8211;" />
                                                                <Span Text="{Binding Label}" />
                                                            </FormattedString>
                                                        </controls:AutoSizeLabel.FormattedText>
                                                        <controls:AutoSizeLabel.Triggers>
                                                            <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="false">
                                                                <Setter Property="TextColor" Value="White" />
                                                            </DataTrigger>

                                                            <DataTrigger TargetType="controls:AutoSizeLabel" Binding="{Binding IsNextExercise}" Value="true">
                                                                <Setter Property="TextColor" Value="#97D2F3" />
                                                            </DataTrigger>
                                                        </controls:AutoSizeLabel.Triggers>
                                                    </controls:AutoSizeLabel>

                                                </StackLayout>
                                                
                                                <Image Source="swap.png" WidthRequest="10" Aspect="AspectFit" HorizontalOptions="Start" IsVisible="{Binding IsSwapTarget}" Margin="3,6" VerticalOptions="Start" />
                                                <StackLayout Orientation="Horizontal" VerticalOptions="Center" HorizontalOptions="End" Spacing="0">

                                                    
                                                    <Button Clicked="OnContextMenuClicked" CommandParameter="{Binding .}" ImageSource="menu_blue" WidthRequest="{OnPlatform Android=60, iOS=50}"  HorizontalOptions="End" VerticalOptions="Center" Margin="0,0,-3,0"
                                                                 BackgroundColor="Transparent" />


                                                    
                                                    <controls:ContextMenuButton
                                                            x:Name="MenuButton"
                                                            Margin="0,0,0,0"
                                                            WidthRequest="1"
                                                            HeightRequest="1"
                                                            ItemsContainerHeight="240"
                                                            ItemsContainerWidth="240">
                                                        
                                                        <controls:ContextMenuButton.Items>
                                                            <x:Array Type="{x:Type MenuItem}">

                                                            </x:Array>
                                                        </controls:ContextMenuButton.Items>
                                                    </controls:ContextMenuButton>




                                                </StackLayout>
                                                <StackLayout.GestureRecognizers>

                                                    <TapGestureRecognizer CommandParameter="{Binding .}" Tapped="CellHeaderTapped" />
                                                </StackLayout.GestureRecognizers>
                                            </StackLayout>
                                            
                                        </StackLayout>
                                    </Frame>
                                </Grid>
                                
                                <Border
                        
                        Style="{StaticResource GradientBorderStyleBlue}"
                        VerticalOptions="Start"
                        HorizontalOptions="FillAndExpand"
                        Margin="4,0,4,0">
                                    <StackLayout x:Name="bind" BindableLayout.ItemsSource="{Binding .}" Spacing="0" BackgroundColor="Transparent">

                                        <BindableLayout.ItemTemplateSelector>
                                            <cells:SetBindingTemplateSelector />
                                        </BindableLayout.ItemTemplateSelector>
                                        
                                    </StackLayout>
                               </Border>
                            </StackLayout>
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                </StackLayout> 
            </ScrollView>
                       <t:RightSideMasterPage Grid.RowSpan="4" IsVisible="False" Grid.Row="0" Grid.ColumnSpan="2" Padding="0" Margin="0" x:Name="SlideMenu" HorizontalOptions="EndAndExpand" VerticalOptions="FillAndExpand"/>
 
                    </Grid>
                
        <!--<Image Source="PlusBlack.png" Margin="0,0,20,30" HeightRequest="70" WidthRequest="70" VerticalOptions="Start" HorizontalOptions="Center" Aspect="AspectFit" AbsoluteLayout.LayoutFlags="PositionProportional" AbsoluteLayout.LayoutBounds="1, 1, 90, 100">
            <Image.GestureRecognizers>
                <TapGestureRecognizer Tapped="NewTapped" />
            </Image.GestureRecognizers>
        </Image>-->
    
</ContentPage>
