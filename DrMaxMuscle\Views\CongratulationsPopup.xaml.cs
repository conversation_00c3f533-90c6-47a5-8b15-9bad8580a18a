using Acr.UserDialogs;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.User;
using DrMuscleWebApiSharedModel;
using System.Text.RegularExpressions;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;
using System.Globalization;
using DrMaxMuscle.Constants;

namespace DrMaxMuscle.Views;

public partial class CongratulationsPopup : Popup
{
    TapGestureRecognizer okGuesture;
    string buttonText = "";
    public event EventHandler OkButtonPress;
    public bool _isHide { get; set; }
    public int workoutCount = 0;
    public string lifted = "";
    public CongratulationsPopup(string image, string title, string subtitle, string buttonText)
    {
        InitializeComponent();
        try
        {
           
            okGuesture = new TapGestureRecognizer();
            okGuesture.Tapped += DrMuscleButtonCancel_Clicked;
            OkAction.GestureRecognizers.Add(okGuesture);
            ImgName.Source = image;

            LblHeading.Text = title;
            LblSubHead.Text = $"{subtitle}";
            OkButton.Text = buttonText;
            this.buttonText = buttonText;
            LblTipText.IsVisible = false;
            var displayInfo = DeviceDisplay.MainDisplayInfo;
            var width = displayInfo.Width / displayInfo.Density;
            mainStack.WidthRequest = width * 0.9; ;
        }
        catch (Exception ex)
        {

        }
        CalculateDetails();
        // uncomment code please
        //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
    }

    private async void CalculateDetails()
    {

        
        //Exercise
        int exerciseCount = 0, consecutiveWeek = 0, newRecordCount = 0;
        double  caloriesDouble = 0;
        if (App.TotalExercises != "0")
        {
            exerciseLbl.Text = App.TotalExercises;
        }
        else
        {
            if (LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Year}") != null)
            {
                var exeCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"Exercises{DateTime.Now.Year}").Value);
                exerciseCount = exeCount;
            }
            if (App.TotalExercises == "0")
                App.TotalExercises = exerciseCount.ToString();
            exerciseLbl.Text = App.TotalExercises;
        }
        if (!string.IsNullOrEmpty(App.TotalExercises))
        {
            var exerciseCounts = Convert.ToInt32(App.TotalExercises);
            if (exerciseCounts > 1)
                exerciseLbl1.Text = "Exercises";
            else
                exerciseLbl1.Text = "Exercise";
        }

        // New records

        if (App.TotalNewRecords != "0")
        {
            newRecordsLbl.Text = App.TotalNewRecords;
        }
        else
        {
            var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            var newRecordList = ((App)Application.Current).NewRecordModelContext.NewRecordList.Where(x => x.ExercisePercentageNumber > 0);

            if (LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout") != null || newRecordList.ToList()?.Count > 0)
            {
                var recordCount = int.Parse(LocalDBManager.Instance.GetDBSetting($"RecordFinishWorkout").Value);
                newRecordCount = newRecordList.ToList()?.Count ?? 0;
            }

            if (App.TotalNewRecords == "0")
                App.TotalNewRecords = newRecordCount.ToString();
            newRecordsLbl.Text = App.TotalNewRecords;

        }

        // Calories
        if (App.TotalCaloriesBurned != "0" && App.TotalCaloriesBurned != "N/A")
        {
            caloriesLbl.Text = App.TotalCaloriesBurned;
        }
        else
        {
            try
            {
                if (LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}") != null)
                {
                    var time = LocalDBManager.Instance.GetDBSetting($"Time{DateTime.Now.Year}").Value;
                    if (time != null && time != "0")
                    {
                        var startedTime = new DateTime(long.Parse(time));
                        if ((DateTime.Now - startedTime).Minutes > 0)
                        {
                            TimeSpan span = DateTime.Now - startedTime;
                            decimal weight2 = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                            caloriesDouble = (double)Math.Round(((decimal)Math.Floor((span.TotalMinutes > 120 ? 120 : span.TotalMinutes)) * (decimal)6.0 * (decimal)3.5 * weight2) / 200);
                        }
                    }
                }

            }
            catch (Exception ex)
            {

            }
            if (App.TotalCaloriesBurned == "0")
                App.TotalCaloriesBurned = caloriesDouble.ToString();
            caloriesLbl.Text = App.TotalCaloriesBurned;

        }

        // Week Streaks
        if (App.TotalWeeks != "0")
        {
            weekStreaklbl.Text = App.TotalWeeks;
        }
        else
        {
            weekStreaklbl.Text = "";
            try
            {
                var wl = await DrMuscleRestClient.Instance.GetWeekStreaksWithoughtLoader();
                if (wl != null)
                {
                    if (wl != null && wl.Count > 0)
                    {
                        var lastTime = wl.Last();
                        var year = Convert.ToString(lastTime.MaxWeek).Substring(0, 4);
                        var weekOfYear = Convert.ToString(lastTime.MaxWeek).Substring(4, 2);
                        CultureInfo myCI = new CultureInfo("en-US");
                        System.Globalization.Calendar cal = myCI.Calendar;
                        if (int.Parse(year) == DateTime.Now.Year)
                        {
                            var currentWeekOfYear = cal.GetWeekOfYear(DateTime.Now, CalendarWeekRule.FirstDay, DayOfWeek.Monday);
                            if (int.Parse(weekOfYear) == currentWeekOfYear)
                            {
                                consecutiveWeek = (int)lastTime.ConsecutiveWeeks;
                            }
                            else if (int.Parse(weekOfYear) == currentWeekOfYear - 1)
                            {
                                consecutiveWeek = (int)lastTime.ConsecutiveWeeks;
                            }
                            else
                            {
                                consecutiveWeek = (int)lastTime.ConsecutiveWeeks;
                            }
                        }
                        else if (int.Parse(year) + 1 == DateTime.Now.Year)
                        {
                            consecutiveWeek = (int)lastTime.ConsecutiveWeeks;
                        }
                    }
                    else
                    {
                        if (CurrentLog.Instance.IsWorkoutedOut)
                            consecutiveWeek = 1;
                    }
                }
            }
            catch (Exception ex)
            {
            }
            if (App.TotalWeeks == "0")
                App.TotalWeeks = consecutiveWeek.ToString();
            weekStreaklbl.Text = App.TotalWeeks;
        }
        
    }
    //protected override async void OnAppearing()
    //{
    //    base.OnAppearing();
    //    MyParticleCanvas.IsActive = true;
    //    MyParticleCanvas.IsRunning = true;
    //    //await Task.Delay(Device.RuntimePlatform.Equals(Device.Android) ? 9000 : 5000);
    //    //MyParticleCanvas.IsActive = false;
    //}

    //protected override void OnDisappearing()
    //{
    //    base.OnDisappearing();
    //    _isHide = true;
    //    MessagingCenter.Unsubscribe<Message.WorkoutLoadedMessage>(this, "WorkoutLoadedMessage");
    //}
    private async Task SetLoadingSummary(TapGestureRecognizer okGuesture)
    {
        //await Task.Delay(250);

        //OkButton.Text = "Loading.";

        //await Task.Delay(700);
        //OkButton.Text = "Loading..";

        //await Task.Delay(700);

        //OkButton.Text = "Loading...";
        //await Task.Delay(700);
        OkButton.Text = this.buttonText;
        OkAction.GestureRecognizers.Add(okGuesture);

    }

    private async void SetLoading(string title)
    {
        //LblHeading.FontAttributes = LblSubHead.FontAttributes;
        //LblHeading.FontSize = LblSubHead.FontSize;
        //LblHeading.TextColor = LblSubHead.TextColor;

        if (Device.RuntimePlatform.Equals(Device.Android))
        {
            LblTipText.Text = "";
            LblTipText.IsVisible = true;
            await Task.Factory.StartNew(async () =>
            {

                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading sets...";
                });


                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading reps...";
                });
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {

                    LblTipText.Text = "Loading weights...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Loading a big pump...";
                });
                await Task.Delay(500);
                if (LblTipText.Text == " ")
                    return;
                MainThread.BeginInvokeOnMainThread(() => {
                    LblTipText.Text = "Let's go!";
                });
            });
        }
        else
        {
            LblTipText.IsVisible = true;

            //ImgLoader.IsVisible = true;
            Device.BeginInvokeOnMainThread(async () =>
            {

                LblTipText.Text = "Loading sets...";
                await Task.Delay(700);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading reps...";
                await Task.Delay(750);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading weights...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Loading a big pump...";
                await Task.Delay(800);
                if (LblTipText.Text == " ")
                    return;
                LblTipText.Text = "Let's go!";

            });
        }
    }

    async void OkButton_Clicked(System.Object sender, System.EventArgs e)
    {
        //await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();

        await this.CloseAsync();

        if (OkButtonPress != null)
            OkButtonPress.Invoke(sender, EventArgs.Empty);


    }

    async void DrMuscleButton_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (await CheckTrialUserAsync())
            return;
            //await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAsync();
            LearnPage page = new LearnPage();
            await Application.Current.MainPage.Navigation.PushAsync(page);
        }
        catch (Exception ex)
        {
        }
    }


    private async Task<bool> CheckTrialUserAsync()
    {
        try
        {
            if (App.IsFreePlan)
            {
                var ShowWelcomePopUp2 = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock custom coaching tips based on your goals and progression.",
                "Upgrade", "Maybe later");
                ShowWelcomePopUp2.ActionSelected += async (sender, action) =>
                {
                    try
                    {
                        if (action == PopupAction.OK)
                        {
                            SubscriptionPage page = new SubscriptionPage();
                            page.OnBeforeShow();
                            await Application.Current.MainPage.Navigation.PushAsync(page);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                };

                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock custom coaching tips based on your goals and progression.",
                //     Title = "You discovered a premium feature!",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             await Application.Current.MainPage.Navigation.PushAsync(page);
                //             //PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //         else
                //         {

                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
        }
        catch (Exception ex)
        {

        }
        return App.IsFreePlan;
    }

    async void DrMuscleButtonCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        await this.CloseAsync();
        //await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
        //    PopupNavigation.Instance.PopAsync();
    }

    async void DrMuscleButtonShareTrial_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            var ImageStream = await mainFrame.CaptureAsync();
            HelperClass.ShareImage(await ImageStream.OpenReadAsync(), "milestone", "popup_share_free_trial");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in DrMuscleButtonShareTrial_Clicked: {ex.Message}");
        }
    }

    async void DrMuscleButtonFeedback_Clicked(System.Object sender, System.EventArgs e)
    {
        //await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0)
        //   await PopupNavigation.Instance?.PopAsync();
        var page = new FeedbackView();
        await PopupNavigation.Instance.PushAsync(page);

    }

    private async void SetupLastWorkoutWas(string workoutwas)
    {
        await DrMuscleRestClient.Instance.SetUserLastWorkoutWas(new UserInfosModel()
        {
            LastWorkoutWas = workoutwas
        });
    }
}