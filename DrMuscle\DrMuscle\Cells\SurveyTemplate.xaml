﻿<?xml version="1.0" encoding="UTF-8" ?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:t="clr-namespace:DrMuscle.Layout"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    xmlns:app="clr-namespace:DrMuscle.Constants"
    x:Class="DrMuscle.Cells.SurveyTemplate">
    <ViewCell.View>
        <controls:CustomFrame
            x:Name="SurveyCardFrame"
            Margin="10,10,10,10"
            Padding="0,10,10,10"
            CornerRadius="12"
            HasShadow="False">
            <controls:CustomFrame.Triggers>
                <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="True" TargetType="controls:CustomFrame">
                    <Setter Property="Margin" Value="10,10,10,0" />
                </DataTrigger>
                <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="False" TargetType="controls:CustomFrame">
                    <Setter Property="Margin" Value="10,0,10,10" />
                </DataTrigger>
            </controls:CustomFrame.Triggers>
            <Grid                
                Padding="10,15,10,15"
                Margin="0,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="40" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <!--Icon for survey-->
                <Image
                    Source="{Binding StrengthImage}"
                    Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
                    Grid.Row="0"
                    Grid.Column="0"
                    WidthRequest="27"
                    VerticalOptions="Start"
                    HeightRequest="27" />

                <!--Label for message-->
                <Label                    
                    Grid.Row="0"
                    Grid.Column="1"
                    Text="{Binding Question}"
                    Margin="0,-8,0,9"
                    TextColor="Black"
                    FontAttributes="Bold"
                    FontSize="19" />

                <!--grid to show emojis for review-->
                <Grid
                    x:Name="EmojiGrid"
                    Grid.Row="1"
                    Grid.Column="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <pancakeView:PancakeView
                        x:Name="PancakeSadReview"
                        Grid.Column="0"
                        CornerRadius="24"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        BackgroundColor="Transparent">
                        <pancakeView:PancakeView.Triggers>
                            <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding SelectedSurveyOption}" Value="Sad">
                                <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                            </DataTrigger>
                        </pancakeView:PancakeView.Triggers>
                        <ImageButton
                            x:Name="SadReviewButton"
                            Source="ic_sad.png"
                            WidthRequest="48"
                            HeightRequest="48"
                            BackgroundColor="Transparent"
                            Clicked="SadReviewButton_Clicked">
                        </ImageButton>
                    </pancakeView:PancakeView>
                    <pancakeView:PancakeView
                        x:Name="PancakeNeutralReview"
                        Grid.Column="1"
                        CornerRadius="24"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        BackgroundColor="Transparent">
                        <pancakeView:PancakeView.Triggers>
                            <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding SelectedSurveyOption}" Value="Neutral">
                                <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                            </DataTrigger>
                        </pancakeView:PancakeView.Triggers>
                        <ImageButton
                            x:Name="NeutralReviewButton"
                            Source="ic_neutral.png"
                            WidthRequest="48"
                            HeightRequest="48"
                            BackgroundColor="Transparent"
                            Clicked="NeutralReviewButton_Clicked"/>
                    </pancakeView:PancakeView>
                    <pancakeView:PancakeView
                        x:Name="PancakeHappyReview"
                        Grid.Column="2"
                        CornerRadius="24"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        BackgroundColor="Transparent">
                        <pancakeView:PancakeView.Triggers>
                            <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                            </DataTrigger>
                        </pancakeView:PancakeView.Triggers>
                        <ImageButton
                            x:Name="HappyReviewButton"
                            Source="ic_happy.png"
                            WidthRequest="48"
                            HeightRequest="48"
                            BackgroundColor="Transparent"
                            Clicked="HappyReviewButton_Clicked"/>
                    </pancakeView:PancakeView>
                </Grid>

                <!--Stacks to show as per button clicked in grid-->
                <!--Stack for response (row 2)-->
                <StackLayout
                    x:Name="ResponseStack"
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    IsVisible="{Binding SelectedSurveyOption, Converter={StaticResource SurveySelectionEnumToVisibilityConverter}}"
                    Margin="1,20,0,0">
                    <!--Label for response message-->
                    <!-- We're always trying to improve. Please email us feedback. We reply in 1 day. -->
                    <!-- Tell us what you thought about Dr. Muscle  -->
                    <Label
                        x:Name="LabelMessage"
                        Margin="45,0,0,0"
                        TextColor="#AA000000"
                        Text="So sorry. Email feedback? 1-day reply."
                        Style="{StaticResource LabelStyle}"
                        VerticalOptions="Center"
                        VerticalTextAlignment="Center"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                        IsVisible="true">
                        <Label.Triggers>
                            <DataTrigger TargetType="Label" Binding="{Binding SelectedSurveyOption}" Value="Neutral">
                                <Setter Property="Text" Value="Oops! Email feedback? 1-day reply." />
                            </DataTrigger>
                            <DataTrigger TargetType="Label" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="Text" Value="Thank you for your feedback!" />
                            </DataTrigger>
                        </Label.Triggers>
                    </Label>

                    <Grid
                        Margin="1,20,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--Pancake Rate 5 Star-->
                        <t:DrMuscleButton
                            x:Name="BtnRate5Stars"
                            Grid.Row="0"
                            Grid.Column="0"
                            Text="RATE 5 STARS"
                            FontSize="13"
                            HeightRequest="45"
                            FontAttributes="Bold"
                            VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                            IsVisible="False"
                            Style="{StaticResource buttonLinkStyle}"
                            TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                            Clicked="BtnRate5Stars_Clicked">
                            <t:DrMuscleButton.Triggers>
                                <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                    <Setter Property="IsVisible" Value="True" />
                                </DataTrigger>
                            </t:DrMuscleButton.Triggers>
                        </t:DrMuscleButton>

                        <!--Chat with us button-->
                        <t:DrMuscleButton
                            x:Name="BtnChatWithUs"
                            Grid.Row="0"
                            Grid.Column="0"
                            Text="CHAT WITH US"
                            FontSize="13"
                            HeightRequest="45"
                            FontAttributes="Bold"
                            VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                            IsVisible="True"
                            Style="{StaticResource buttonLinkStyle}"
                            TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                            Clicked="BtnChatWithUs_Clicked">
                            <t:DrMuscleButton.Triggers>
                                <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                    <Setter Property="IsVisible" Value="False" />
                                </DataTrigger>
                            </t:DrMuscleButton.Triggers>
                        </t:DrMuscleButton>

                        <!--Pancake Share Free Trial-->
                        <pancakeView:PancakeView
                            x:Name="PanecakeBtnShareFreeTrial"
                            Grid.Row="0"
                            Grid.Column="1"
                            Padding="0,0,0,0"
                            Margin="10,0,0,0"
                            IsClippedToBounds="true"
                            OffsetAngle="90"
                            CornerRadius="6"
                            VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                            HeightRequest="45"
                            Style="{StaticResource PancakeViewStyleBlue}"
                            IsVisible="False">
                            <pancakeView:PancakeView.Triggers>
                                <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                    <Setter Property="IsVisible" Value="True" />
                                </DataTrigger>
                            </pancakeView:PancakeView.Triggers>
                            <t:DrMuscleButton
                                x:Name="BtnShareFreeTrial"
                                VerticalOptions="Center"
                                HeightRequest="45"
                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                CornerRadius="6"
                                HorizontalOptions="FillAndExpand"
                                Text="SHARE FREE TRIAL"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White"
                                Clicked="BtnShareFreeTrial_Clicked"/>
                        </pancakeView:PancakeView>

                        <!--Pancake Email Us-->
                        <pancakeView:PancakeView
                            x:Name="PanecakeBtnEmailUs"
                            Grid.Row="0"
                            Grid.Column="1"
                            Padding="0,0,0,0"
                            Margin="10,0,0,0"
                            IsClippedToBounds="true"
                            OffsetAngle="90"
                            CornerRadius="6"
                            VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                            Style="{StaticResource PancakeViewStyleBlue}"
                            HeightRequest="45">
                            <pancakeView:PancakeView.Triggers>
                                <DataTrigger TargetType="pancakeView:PancakeView" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                    <Setter Property="IsVisible" Value="False" />
                                </DataTrigger>
                            </pancakeView:PancakeView.Triggers>
                            <t:DrMuscleButton
                                x:Name="BtnEmailUs"
                                VerticalOptions="Center"
                                HeightRequest="45"
                                FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                                CornerRadius="6"
                                HorizontalOptions="FillAndExpand"
                                Text="EMAIL FEEDBACK"
                                Style="{StaticResource highEmphasisButtonStyle}"
                                BackgroundColor="Transparent"
                                BorderColor="Transparent"
                                TextColor="White"
                                Clicked="BtnEmailUs_Clicked"/>
                        </pancakeView:PancakeView>
                    </Grid>
                </StackLayout>
            </Grid>
        </controls:CustomFrame>
    </ViewCell.View>
</ViewCell>