using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using System.Text.RegularExpressions;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Views;

public partial class WeightGoalPopup : PopupPage
{
	public WeightGoalPopup()
	{
		InitializeComponent();
        LbGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        if (LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg")
        {
            BtnKgClicked(BtnKg, EventArgs.Empty);
        }
        else
            BtnLbsClicked(BtnLbs, EventArgs.Empty);

    }
    protected override void OnAppearing()
    {
        base.OnAppearing();
        Device.BeginInvokeOnMainThread(() =>
        {
            EntryBodyWeight.Focus();
        });
    }
    public async void BtnDoneClicked(object sender, EventArgs args)
    {
        if (string.IsNullOrEmpty(EntryBodyWeight.Text) || string.IsNullOrWhiteSpace(EntryBodyWeight.Text))
            return;
        try
        {
            var weight = int.Parse(EntryBodyWeight.Text);
            if (weight < 1)
            {
                await HelperClass.DisplayCustomPopupForResult("Error",
                        "Please enter valid goal weight","Ok","");

                // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = "Please enter valid goal weight",
                //     Title = "Error"
                // });
                return;
            }
        }
        catch (Exception ex)
        {

        }

        var weightGoal = EntryBodyWeight.Text;
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack.Count() > 0) 
        //    await PopupNavigation.Instance.PopAsync();

        MessagingCenter.Send<GoalWeightMessage>(new GoalWeightMessage() { WeightGoal = weightGoal }, "GoalWeightMessage");
    }

    public async void BtnLbsClicked(object sender, EventArgs args)
    {
        //BtnLbs.BackgroundColor = Color.FromHex("#5CD196");
        BtnKg.BackgroundColor = Colors.Transparent;
        LocalDBManager.Instance.SetDBSetting("massunit", "lb");
        KgGradient.BackgroundColor = Colors.Transparent;
        LbGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        BtnKg.TextColor = Color.FromHex("#0C2432");
        BtnLbs.TextColor = Colors.White;
    }

    public async void BtnKgClicked(object sender, EventArgs args)
    {
        BtnLbs.BackgroundColor = Colors.Transparent;
        //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        LocalDBManager.Instance.SetDBSetting("massunit", "kg");
        BtnKg.TextColor = Colors.White;
        BtnLbs.TextColor = Color.FromHex("#0C2432");
        KgGradient.BackgroundColor = Constants.AppThemeConstants.BlueColor;
        LbGradient.BackgroundColor = Colors.Transparent;
    }

    protected void BodyweightPopup_OnTextChanged(object obj, TextChangedEventArgs args)
    {
        try
        {

            Entry entry = (Entry)obj;
            const string textRegex = @"^\d+(?:[\.,]\d{0,5})?$";
            var text = entry.Text.Replace(",", ".");
            bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            if (IsValid == false && !string.IsNullOrEmpty(entry.Text))
            {
                double result;
                entry.Text = entry.Text.Substring(0, entry.Text.Length - 1);
                double.TryParse(entry.Text, out result);
                entry.Text = result.ToString();
            }

        }
        catch (Exception ex)
        {

        }
    }
}
