﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.User.WelcomePage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:local="clr-namespace:DrMaxMuscle.Behaviors"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             Title="WelcomePage">
    <StackLayout>
        <Grid
            Margin="0"
            HorizontalOptions="FillAndExpand"
            RowSpacing="0"
            VerticalOptions="FillAndExpand">
            <Image
            Aspect="AspectFill"
            HorizontalOptions="FillAndExpand"
            Source="page2"
            VerticalOptions="FillAndExpand" />
            <ScrollView>
                <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" >

                    <StackLayout VerticalOptions="FillAndExpand">
                        <StackLayout Orientation="Vertical" VerticalOptions="StartAndExpand" HorizontalOptions="CenterAndExpand">
                            <Label x:Name="WorkoutInfo1" IsVisible="false" Text="See C# (bottom)" HorizontalOptions="CenterAndExpand" TextColor="LightGray" FontSize="16"></Label>
                            <StackLayout
                           HorizontalOptions="FillAndExpand"
                           Orientation="Vertical"
                           Spacing="0">
                                <Image
                               x:Name="AppLogoImage"
                               Aspect="AspectFit"
                                HeightRequest="135"
                                WidthRequest="145"
                               HorizontalOptions="Center"
                               Source="logo1">
                                    <Image.Margin>
                                        <OnPlatform x:TypeArguments="Thickness">
                                            <On Platform="iOS" Value="0,40,0,0"/>
                                            <On Platform="Android" Value="0,20,0,0"/>
                                        </OnPlatform>
                                    </Image.Margin>
                                </Image>
                                <Label
                               x:Name="LblHeader1"
                               Style="{StaticResource OnBoardingLabelStyle}"
                               FontSize="21" 
                               FontAttributes="Bold"
                               HorizontalTextAlignment="Center"
                               Text="Log in"
                               TextColor="White" />
                                <Label
                               x:Name="LblHeader2"
                               Style="{StaticResource OnBoardingLabelStyle}" 
                               FontSize="17"
                               HorizontalOptions="CenterAndExpand"
                               HorizontalTextAlignment="Center"
                               Text="Save your workouts,"
                               TextColor="LightGray" />
                                <Label
                               x:Name="LblHeader3"
                               Style="{StaticResource OnBoardingLabelStyle}" 
                               FontSize="17"
                               HorizontalOptions="CenterAndExpand"
                               HorizontalTextAlignment="Center"
                               Text="progress, and stats"
                               TextColor="LightGray" />
                            </StackLayout>
                            <Label x:Name="WorkoutInfo2" IsVisible="false" Text="See C# (bottom)" FontAttributes="Bold" HorizontalOptions="CenterAndExpand" TextColor="White" Style="{StaticResource OnBoardingLabelStyleBig}"></Label>
                            <t:DrMuscleButton CornerRadius="0" IsEnabled="False" x:Name="MadeAMistakeButton" Text="" HeightRequest="40" HorizontalOptions="FillAndExpand" VerticalOptions="Start" Style="{StaticResource buttonLinkStyle}"></t:DrMuscleButton>

                            <StackLayout x:Name="CreateAccountStack" IsVisible="false">
                                <Label FontSize="19" x:Name="LblCreateAccount" Text="Create account" HorizontalOptions="CenterAndExpand" TextColor="LightGray" Style="{StaticResource LabelStyle}"></Label>
                                <Label x:Name="LblBackUpAutomatically" HorizontalOptions="CenterAndExpand" TextColor="LightGray" Style="{StaticResource LabelStyle}"></Label>
                            </StackLayout>
                        </StackLayout>
                        <StackLayout x:Name="CreateAccount" VerticalOptions="CenterAndExpand" Orientation="Vertical" Padding="{OnPlatform Android= '0,20,0,10',iOS='0,10,0,10'}" Spacing="10">

                            <!--<Label x:Name="LblLoginText" Margin="0,2,0,0" Text="Log in with email" HorizontalOptions="CenterAndExpand" TextColor="LightGray" FontSize="16"></Label>-->
                            <Frame x:Name="EmailFrame" Margin="0,20,0,0" CornerRadius="{OnPlatform Android='10', iOS='10'}" Padding="{OnPlatform Android='15,5',iOS= '15'}"
                                    HasShadow="False" BackgroundColor="#4C000000" BorderColor="LightGray">
                                <t:DrMuscleEntry x:Name="EmailEntry" MaxLength="45" FontSize="16"
                                       BackgroundColor="Transparent" PlaceholderColor="LightGray" Placeholder="Enter email" 
                                       Keyboard="Email" TextColor="White">
                                </t:DrMuscleEntry>
                            </Frame>
                            <Frame  x:Name="PasswordFrame" Margin="0,2,0,0" CornerRadius="{OnPlatform Android='10', iOS='10'}"
                               Padding="{OnPlatform Android='15,5,5,5',iOS= '13,4'}" HasShadow="False" BackgroundColor="#4C000000" BorderColor="LightGray">
                                <Grid>
                                    <t:DrMuscleEntry MaxLength="40" FontSize="16" x:Name="PasswordEntry" Placeholder="Enter password" 
                                           IsPassword="{Binding Source={x:Reference ShowPasswordActualTrigger}, Path=HidePassword}" 
                                           BackgroundColor="Transparent" HorizontalOptions="FillAndExpand" TextColor="White" PlaceholderColor="LightGray">
                                    </t:DrMuscleEntry>
                                    <ImageButton VerticalOptions="Center" HeightRequest="20" WidthRequest="30" Margin="0,0,6,0" BackgroundColor="Transparent"
                                        HorizontalOptions="End" Source="close_eye">
                                        <ImageButton.Triggers>
                                            <EventTrigger Event="Clicked">
                                                <local:ShowPasswordTriggerAction ShowIcon="open_eye"
                                                     HideIcon="close_eye"
                                                     x:Name="ShowPasswordActualTrigger"/>
                                            </EventTrigger>
                                        </ImageButton.Triggers>
                                    </ImageButton>

                                </Grid>

                            </Frame>
                            <Label Text="At least 6 characters" Margin="6,-4,0,0" TextColor="White" FontSize="12" x:Name="LblPasswordText"/>
                            <Frame x:Name="EmailBtnFrame" Padding="15" BackgroundColor="White"
                                CornerRadius="{OnPlatform Android='10', iOS='10'}" HorizontalOptions="FillAndExpand">
                                <StackLayout Padding="0" Orientation="Horizontal" Spacing="10">
                                    <Image HeightRequest="20" Source="mail_icon" />
                                    <Label FontSize="16" Margin="-30,0,0,0" x:Name="LoginButton" FontAttributes="Bold"
                                       HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" Text="Log in" TextColor="Black"
                                       VerticalTextAlignment="Center" />
                                </StackLayout>
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer  Tapped="LoginButton_Clicked"/>
                                </Frame.GestureRecognizers>
                            </Frame>
                            <t:DrMuscleButton x:Name="ResetPasswordButton" BackgroundColor="Transparent" Margin="0,0,0,0" Padding="0" Text="Forgot password?" HorizontalOptions="FillAndExpand" VerticalOptions="StartAndExpand" TextColor="LightGray" ></t:DrMuscleButton>
                            <StackLayout Margin="20,0" HorizontalOptions="FillAndExpand"
                               Orientation="Horizontal">
                                <BoxView HeightRequest="0.6" Margin="0,3,0,0" HorizontalOptions="FillAndExpand" VerticalOptions="CenterAndExpand"
                                   Color="White" />
                                <Label x:Name="LblOr" FontSize="15" Text="OR" TextColor="White" VerticalTextAlignment="Center" />
                                <BoxView HeightRequest="0.6" Margin="0,3,0,0" HorizontalOptions="FillAndExpand" VerticalOptions="CenterAndExpand"
                                   Color="White" />
                            </StackLayout>
                            <StackLayout
                               HorizontalOptions="FillAndExpand" Orientation="Vertical" Spacing="10" VerticalOptions="EndAndExpand">
                                <Frame x:Name="GoogleBtnFrame" Padding="15" BackgroundColor="#db3737" BorderColor="#db3737"
                                    CornerRadius="{OnPlatform Android='10', iOS='10'}" HorizontalOptions="FillAndExpand">
                                    <StackLayout Padding="0" Orientation="Horizontal" Spacing="10">
                                        <Image HeightRequest="20" Source="google_icon" />
                                        <Label x:Name="LoginWithGoogleButton" FontSize="16" FontAttributes="Bold" HorizontalOptions="FillAndExpand"
                                           HorizontalTextAlignment="Center" Text="Log in with Google" TextColor="White" VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="LoginWithGoogleAsync"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <Frame x:Name="FacebookBtnFrame" Padding="15" BackgroundColor="#4768AD" BorderColor="#4768AD" IsVisible="{OnPlatform Android='false' , iOS='false'}"
                                    CornerRadius="{OnPlatform Android='10', iOS='10'}" HorizontalOptions="FillAndExpand">
                                    <StackLayout Padding="0" Orientation="Horizontal" Spacing="10">
                                        <Image HeightRequest="20" Source="facebook_icon" />
                                        <Label x:Name="LoginWithFBButton" FontSize="16" FontAttributes="Bold"
                                           HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center" Text="Log in with Facebook"
                                           TextColor="White" VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="LoginWithFBButton_Clicked"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                                <Frame x:Name="BtnAppleSignIn" IsVisible="{OnPlatform Android='false' , iOS='true'}"
                                   Padding="15" BackgroundColor="Black" BorderColor="Black" HorizontalOptions="FillAndExpand">
                                    <StackLayout Padding="0" Orientation="Horizontal" Spacing="10">
                                        <Image HeightRequest="20" Source="apple" />
                                        <Label x:Name="AppleBtnText" FontSize="16" FontAttributes="Bold" HorizontalOptions="FillAndExpand"
                                           HorizontalTextAlignment="Center" Text="Join free with Apple" TextColor="White" VerticalTextAlignment="Center" />
                                    </StackLayout>
                                    <Frame.CornerRadius>
                                        <OnPlatform x:TypeArguments="x:Single">
                                            <On Platform="iOS" Value="10" />
                                            <On Platform="Android" Value="10" />
                                        </OnPlatform>
                                    </Frame.CornerRadius>
                                    <Frame.GestureRecognizers>
                                        <TapGestureRecognizer  Tapped="LoginWithAppleAsync"/>
                                    </Frame.GestureRecognizers>
                                </Frame>
                            </StackLayout>
                            <Label 
                            x:Name="CreateNewAccountButton"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            VerticalTextAlignment="Center"
                            FontSize="Small"
                            Padding="0"
                            HeightRequest="40"
                            Margin="0,-5,0,0"
                            TextColor="LightGray" 
                            Text="Create new account">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="CreateNewAccountButton_Clicked"/>
                                </Label.GestureRecognizers>
                            </Label>
                            <Image Margin="0,24,0,0" Source="stars_5.png" HeightRequest="{OnPlatform iOS='45'}" WidthRequest="90" VerticalOptions="Center" HorizontalOptions="CenterAndExpand" />
                            <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                                <Label TextColor="{x:Static constnats:AppThemeConstants.LightGrayColor}" FontAttributes="Italic" Text="&quot;Stronger at 62 than I was at 31&quot;" HorizontalOptions="CenterAndExpand"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="14" />
                                <Label TextColor="{x:Static constnats:AppThemeConstants.LightGrayColor}" Text="-Rodney Spence" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="13" />
                            </StackLayout>

                        </StackLayout>


                        <StackLayout Spacing="2" Margin="0,20,0,0" HorizontalOptions="CenterAndExpand" VerticalOptions="EndAndExpand" Orientation="Vertical" >
                            <Label x:Name="ByContinueAgree" HorizontalOptions="CenterAndExpand" Text="By continuing, you agree to our " FontSize="13" TextColor="White" Style="{StaticResource LabelStyle}"/>
                            <StackLayout x:Name="TermsPrivacyPolicy" HorizontalOptions="CenterAndExpand" VerticalOptions="End" Orientation="Horizontal">
                                <Label x:Name="TermsOfUse" FontSize="13" Text="terms of use " TextColor="{x:Static constnats:AppThemeConstants.SharpBlueColor}" />
                                <Label x:Name="LblAnd" Text="and" Padding="5,0" FontSize="13" TextColor="White" Style="{StaticResource LabelStyle}"/>
                                <Label x:Name="PrivacyPolicy" FontSize="13" Text=" privacy policy." TextColor="{x:Static constnats:AppThemeConstants.SharpBlueColor}"  Margin="0,0,0,20"/>
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>

                </StackLayout>

            </ScrollView>
        </Grid>
    </StackLayout>
</ContentPage>