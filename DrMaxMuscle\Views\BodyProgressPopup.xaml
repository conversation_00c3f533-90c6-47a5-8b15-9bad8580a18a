<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
               xmlns:control="clr-namespace:DrMaxMuscle.Controls"
               CanBeDismissedByTappingOutsideOfPopup="True"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               Color="Transparent"
               xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="DrMaxMuscle.Views.BodyProgressPopup">
    <Grid
        x:Name="MainFrame"
        BackgroundColor="Transparent"
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
    Margin="0"
    Padding="0"
    >
            <Frame
        x:Name="AndroidFrame"
        IsClippedToBounds="true"
        CornerRadius="8"
        VerticalOptions="FillAndExpand"
        HorizontalOptions="FillAndExpand"
        Margin="0"
                BorderColor="Transparent"
        Padding="20"
        BackgroundColor="White">


                <StackLayout
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand"
        Spacing="10"
        >
                    <!--<Label
                Padding="0"
                Margin="0"
                VerticalOptions="StartAndExpand"
                x:Name="currentDateLbl"
                Text=""
                FontSize="Medium"
                FontAttributes="Bold"
                VerticalTextAlignment="Center"
                TextColor="White"
                HorizontalTextAlignment="Center"
                HorizontalOptions="CenterAndExpand"
                />-->
    
                    <Border x:Name="datepickerFrame" Margin="0,0,0,0"  Padding="0"
                 HorizontalOptions="FillAndExpand" BackgroundColor="White" Stroke="Gray">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="10"/>
                        </Border.StrokeShape>
                        
                        <StackLayout
                    x:Name="datpickerStack"
                    Orientation="Horizontal"
                    HorizontalOptions="FillAndExpand"
                    Spacing="5">
                            <Grid
                                Padding="0"
                                Margin="0"
                                HorizontalOptions="FillAndExpand">
                                <DatePicker
FontSize="Medium"
Visual="Default"
BackgroundColor="Transparent"
FontAttributes="Bold"
                                    Margin="0"
DateSelected="datePicker_DateSelected"
TextColor="Transparent"
IsVisible="True"
HorizontalOptions="FillAndExpand"
x:Name="datePicker"  />
                                <StackLayout
                                    Orientation="Horizontal"
                                    HorizontalOptions="FillAndExpand"
                                    VerticalOptions="CenterAndExpand"
                                    
                                    Spacing="5"
                                    Padding="0"
                                    Margin="0">
                                    <Label
    Padding="0"
    Margin="0"
    VerticalOptions="StartAndExpand"
    x:Name="currentDateLbl"
    Text=""
    FontSize="15"
                                        HeightRequest="20"
    VerticalTextAlignment="Center"
    TextColor="Black"
    HorizontalTextAlignment="Center"
    HorizontalOptions="FillAndExpand"
    />
                                    <Image
                                        Margin="0,0,10,0"
Source="black_down_arrow.png"
HeightRequest="15"
>

                                    </Image>

                                </StackLayout>
                                
                            </Grid>
                                                       
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Tapped="Picker_Tapped"/>
                            </StackLayout.GestureRecognizers>

                        </StackLayout>
                    </Border>

                    <!--<StackLayout
                Orientation="Horizontal"
                HorizontalOptions="FillAndExpand">
                <Frame x:Name="weightFrame" Margin="0,0,0,0" CornerRadius="{OnPlatform Android='10', iOS='10'}" Padding="{OnPlatform Android='6',iOS= '10'}"
                    HasShadow="False" HorizontalOptions="FillAndExpand" BackgroundColor="White" BorderColor="Gray">
                    <t:DrMuscleEntry x:Name="weightEntry" MaxLength="7" FontSize="16" 
                           BackgroundColor="Transparent" PlaceholderColor="Gray" 
                           Keyboard="Numeric" TextColor="Black">
                    </t:DrMuscleEntry>
                </Frame>
                <Frame x:Name="fatFrame" Margin="0,0,0,0" CornerRadius="{OnPlatform Android='10', iOS='10'}" Padding="{OnPlatform Android='6',iOS= '10'}"
                    HasShadow="False" HorizontalOptions="End" BackgroundColor="White" BorderColor="Gray">
                    <t:DrMuscleEntry x:Name="fatEntry" MaxLength="4" FontSize="16" 
                                     TextChanged="fatEntry_TextChanged"
                       BackgroundColor="Transparent" PlaceholderColor="Gray" Placeholder="Body fat %" 
                       Keyboard="Numeric" TextColor="Black">
                    </t:DrMuscleEntry>
                </Frame>
            </StackLayout>-->
                    <StackLayout
                Orientation="Horizontal"
                HorizontalOptions="FillAndExpand">
                        <Grid
                            ColumnSpacing="10"
                    HorizontalOptions="FillAndExpand">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width=".7*"/>
                                <ColumnDefinition Width=".3*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="weightFrame" Grid.Column="0" Margin="0,0,0,0"  Padding="{OnPlatform Android='5',iOS= '10'}"
                     HorizontalOptions="FillAndExpand" BackgroundColor="White" Stroke="Gray">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="10"/>
                                </Border.StrokeShape>
                                <t:DrMuscleEntry x:Name="weightEntry" MaxLength="7" FontSize="15" 
                           BackgroundColor="Transparent" PlaceholderColor="Gray" 
                                               Margin="8"
                           Keyboard="Numeric" TextColor="Black">
                                </t:DrMuscleEntry>
                            </Border>
                            <Border x:Name="fatFrame" Grid.Column="1" Margin="0,0,0,0"  Padding="{OnPlatform Android='5',iOS= '10'}"
                     HorizontalOptions="FillAndExpand" BackgroundColor="White" Stroke="Gray">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="10"/>
                                </Border.StrokeShape>
                                <t:DrMuscleEntry x:Name="fatEntry" MaxLength="4" FontSize="15" 
                                               Margin="8"
                                     TextChanged="fatEntry_TextChanged"
                       BackgroundColor="Transparent" PlaceholderColor="Gray" Placeholder="Fat %" 
                       Keyboard="Numeric" TextColor="Black">
                                </t:DrMuscleEntry>
                            </Border>
                        </Grid>

                    </StackLayout>

                    <Border
                Padding="0"
                Margin="0"
                VerticalOptions="End"
                HorizontalOptions="FillAndExpand"
                HeightRequest="50">
                        <Border.Background>
                            <LinearGradientBrush  EndPoint="1,0">
                                <GradientStop Color="#0C2432" Offset="0.0"/>
                                <GradientStop Color="#195276" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="6"/>
                        </Border.StrokeShape>
                        <t:DrMuscleButton
                    CornerRadius="6"
                    VerticalOptions="Fill"
                    HeightRequest="66"
                    FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                    HorizontalOptions="FillAndExpand"
                    Text="CHECK IN"
                    Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White"
                    Clicked="OK_Btn_Clicked"/>
                    </Border>
                </StackLayout>
            </Frame>
        </Grid>

</toolkit:Popup>
