﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Screens.Workouts;
using DrMuscleWebApiSharedModel;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Views;

public partial class ProgramListPopup : PopupPage
{
    public ObservableCollection<WorkoutTemplateGroupModel> workoutOrderItems = new ObservableCollection<WorkoutTemplateGroupModel>();

    public object _chooseYourCustomWorkout;
    public string ProgramName { get; set; }
    public ProgramListPopup()
    {
        InitializeComponent();
        ProgramListView.ItemsSource = workoutOrderItems;

        //ProgramListView.ItemTapped += WorkoutListView_ItemTapped;
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        DependencyService.Get<IFirebase>().SetScreenName("program_list_popup");

    }
    public void setDataSource()
    {

        WorkoutTemplateGroupModel addWorkoutOrderItem = new WorkoutTemplateGroupModel();
        addWorkoutOrderItem.Id = -1;
        addWorkoutOrderItem.IsSystemExercise = false;
        addWorkoutOrderItem.Label = "None";
        if (workoutOrderItems.Where(x => x.Id == -1).FirstOrDefault() == null)
            workoutOrderItems.Insert(0, addWorkoutOrderItem);

        ProgramListView.ItemsSource = workoutOrderItems;
        ProgramListView.HeightRequest = workoutOrderItems.Count * 50;
    }

    private async void WorkoutListView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        if (e.Item == null)
            return;
        await MauiProgram.SafeDismissTopPopup();
        //if (PopupNavigation.Instance.PopupStack?.Count > 0)
        //    await PopupNavigation.Instance.PopAsync();

        if (this._chooseYourCustomWorkout is ChooseYourCustomWorkoutPage)
            ((ChooseYourCustomWorkoutPage)this._chooseYourCustomWorkout).TakeOrderLevel((int)((WorkoutTemplateGroupModel)e.Item).Id, ProgramName);
        //TODO: MAUI
        else if (this._chooseYourCustomWorkout is ChooseWorkoutOrder)
            ((ChooseWorkoutOrder)this._chooseYourCustomWorkout).TakeOrderLevel((int)((WorkoutTemplateGroupModel)e.Item).Id, ProgramName);

    }

    private async void OnItemTapped(object sender, TappedEventArgs e)
    {
        var grid = sender as Grid;
        App.IsCustomPopup = false;
        if (grid?.BindingContext is WorkoutTemplateGroupModel selectedItem)
        {
            await MauiProgram.SafeDismissTopPopup();

            if (_chooseYourCustomWorkout is ChooseYourCustomWorkoutPage customWorkoutPage)
                customWorkoutPage.TakeOrderLevel((int)selectedItem.Id, ProgramName);
            else if (_chooseYourCustomWorkout is ChooseWorkoutOrder workoutOrderPage)
                workoutOrderPage.TakeOrderLevel((int)selectedItem.Id, ProgramName);
        }
    }
}
