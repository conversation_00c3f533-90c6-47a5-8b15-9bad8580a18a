using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Screens.User;

public partial class FAQPage : ContentPage
{
    string supportUrl = "";
    public FAQPage()
    {
        InitializeComponent();
        Title = "Help";
        //SendBirdClient.Init("91658003-270F-446B-BD61-0043FAA8D641");
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        var _firebase = (IFirebase)MauiProgram.ServiceProvider.GetService(typeof(IFirebase));
        _firebase.SetScreenName("faq_page");
    }
    private async void MealPlan_Tapped(object sender, System.EventArgs e)
    {
        await HelperClass.SendMail("I would like a meal plan");
    }

    private async void OneonOnePlan_Tapped(object sender, System.EventArgs e)
    {
        try
        {
            CurrentLog.Instance.ChannelUrl = supportUrl;
            CurrentLog.Instance.RoomId = 0;
            SupportPage page = new SupportPage();
            page.OnBeforeShow();
            await Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<SupportPage>();
        }
        catch (Exception ex)
        {

        }
    }


    private async void SupportMail_Tapped(object sender, System.EventArgs e)
    {
        await HelperClass.SendMail("");
    }


}
