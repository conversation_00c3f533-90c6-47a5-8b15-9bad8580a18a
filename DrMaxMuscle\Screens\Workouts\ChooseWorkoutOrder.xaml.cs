﻿using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Layout;
using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using DrMaxMuscle.Resx;
using DrMaxMuscle.Views;
using System.Globalization;
using RGPopup.Maui.Services;
using DrMaxMuscle.Dependencies;
using System.Text.RegularExpressions;
using DrMaxMuscle.Utility;
using CommunityToolkit.Maui.Views;

namespace DrMaxMuscle.Screens.Workouts;

public partial class ChooseWorkoutOrder : ContentPage
{
    public ObservableListCollection<WorkoutTemplateModel> WorkoutItems = new ObservableListCollection<WorkoutTemplateModel>();

    public ChooseWorkoutOrder()
    {
        InitializeComponent();

        ExerciseListView.ItemsSource = WorkoutItems;

        SaveWorkoutButton.Clicked += NextButton_Clicked;
        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        OnBeforeShow();
    }

    private void RefreshLocalized()
    {
        Title = "Choose order";
        SaveWorkoutButton.Text = AppResources.SaveProgram;
    }
    public async void OnBeforeShow()
    {
        DependencyService.Get<IFirebase>().SetScreenName("choose_workout_order");

        await UpdateExerciseList();
    }

    public async void TakeOrderLevel(int programid, string programName)
    {

        CurrentLog.Instance.CurrentWorkoutTemplateGroup.NextProgramId = programid;

        if (programid == -1)
        {
            try
            {

                SaveWorkout();
                return;
            }
            catch (Exception e)
            {
            }
        }
        else
        {
            CustomPromptConfig minRepsPopup = new CustomPromptConfig("","Enter level here",AppResources.Ok,AppResources.Cancel,
            $"How many workouts before switching to that program automatically?",Keyboard.Numeric,"");

            minRepsPopup.ActionSelected += (sender,action) => {
                if(action == PopupAction.OK){
                    if (string.IsNullOrWhiteSpace(minRepsPopup.text) || Convert.ToDecimal(minRepsPopup.text, CultureInfo.InvariantCulture) < 1)
                        {
                            TakeOrderLevel(programid, programName);
                            return;
                        }
                        int levels = Convert.ToInt32(minRepsPopup.text, CultureInfo.InvariantCulture);
                        if (levels >= 0)
                        {
                            CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp = levels;
                            SaveWorkout();
                        }
                }
            };

            await Application.Current.MainPage.ShowPopupAsync(minRepsPopup);
            // PromptConfig minRepsPopup = new PromptConfig()
            // {
            //     InputType = InputType.Number,
            //     IsCancellable = true,
            //     Title = "",
            //     Message = $"How many workouts before switching to that program automatically?",
            //     Placeholder = "Enter level here",
            //     OkText = AppResources.Ok,
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OnAction = async (ageResponse) =>
            //     {
            //         if (ageResponse.Ok)
            //         {
            //             if (string.IsNullOrWhiteSpace(ageResponse.Value) || Convert.ToDecimal(ageResponse.Value, CultureInfo.InvariantCulture) < 1)
            //             {
            //                 TakeOrderLevel(programid, programName);
            //                 return;
            //             }
            //             int levels = Convert.ToInt32(ageResponse.Value, CultureInfo.InvariantCulture);
            //             if (levels >= 0)
            //             {
            //                 CurrentLog.Instance.CurrentWorkoutTemplateGroup.RequiredWorkoutToLevelUp = levels;
            //                 SaveWorkout();
            //             }
            //         }
            //     }
            // };
            // minRepsPopup.OnTextChanged += ExerciseRepsPopup_OnTextChanged;
            // UserDialogs.Instance.Prompt(minRepsPopup);
        }
    }
    protected void ExerciseRepsPopup_OnTextChanged(PromptTextChangedArgs obj)
    {
        const string textRegex = @"^\d+(?:)?$";
        bool IsValid = Regex.IsMatch(obj.Value, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(obj.Value))
        {
            double result;
            obj.Value = obj.Value.Substring(0, obj.Value.Length - 1);
            double.TryParse(obj.Value, out result);
            obj.Value = result.ToString();
        }
    }

    private async void NextButton_Clicked(object sender, EventArgs e)
    {
        try
        {

            if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id != -1)
            {
                //Edit
                if (CurrentLog.Instance.workoutOrderItems.Count == 0)
                    SaveWorkout();
                else
                {
                    var popup = new ProgramListPopup();
                    if (CurrentLog.Instance.workoutOrderItems != null)
                    {
                        var currentProg = CurrentLog.Instance.workoutOrderItems.Where(x => x.Id == CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id).FirstOrDefault();
                        if (currentProg != null)
                            CurrentLog.Instance.workoutOrderItems.Remove(currentProg);
                    }
                    if(popup != null)
                    {
                        popup.workoutOrderItems = new ObservableCollection<WorkoutTemplateGroupModel>(CurrentLog.Instance.workoutOrderItems);
                        popup._chooseYourCustomWorkout = this;
                        popup.ProgramName = "";
                        popup.setDataSource();
                        await PopupNavigation.Instance.PushAsync(popup);
                    }
                    
                }
            }
            else
                SaveWorkout();

        }
        catch (Exception ex)
        {

        }
    }

    private async void SaveWorkout()
    {
        CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Clear();
        foreach (WorkoutTemplateModel m in WorkoutItems)
        {
            CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Add(m);
        }

        BooleanModel result = await DrMuscleRestClient.Instance.CreateNewWorkoutTemplateOrder(CurrentLog.Instance.CurrentWorkoutTemplateGroup);
        if (result.Result)
        {
            //if (CurrentLog.Instance.CurrentWorkoutTemplateGroup.Id == -1)
            //{

            //}
            //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workouts = new List<WorkoutTemplateModel>();
            //PagesFactory.GetPage<ChooseYourCustomWorkoutPage>().workoutGroups = new List<WorkoutTemplateGroupModel>();
            //if (Device.RuntimePlatform == Device.Android)
            //    await PagesFactory.PushAsyncWithoutBefore<ChooseYourCustomWorkoutPage>();
            //else
            //    await PagesFactory.PushAsync<ChooseYourCustomWorkoutPage>();
            Utility.HelperClass.PopToPage<ChooseYourCustomWorkoutPage>(this.Navigation);
        }
        else
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
        }
    }

    private void OnBindingContextChanged(object sender, EventArgs e)
    {
        base.OnBindingContextChanged();

        if (((BindableObject)sender).BindingContext == null)
            return;

        // WorkoutTemplateModel m = (WorkoutTemplateModel)((BindableObject)sender).BindingContext;

        //Button up = (Button)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[0];
        //Button down = (Button)((StackLayout)((StackLayout)((ViewCell)sender).View).Children[1]).Children[1];

        //int itemIndex = WorkoutItems.IndexOf(m);

        //up.Clicked += (object s, EventArgs ea) =>
        //{
        //    MoveWorkoutTemplate(m, "up", (Button)s);
        //};

        //down.Clicked += (object s, EventArgs ea) =>
        //{
        //    MoveWorkoutTemplate(m, "down", (Button)s);
        //};
    }

    private void MoveWorkoutTemplate(WorkoutTemplateModel m, string UpDown, Button button)
    {
        int itemIndex = WorkoutItems.IndexOf(m);
        if (UpDown == "up")
        {
            if (itemIndex - 1 >= 0)
                WorkoutItems.Move(itemIndex, itemIndex - 1);
        }
        else
        {
            if (itemIndex + 1 < CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates.Count)
                WorkoutItems.Move(itemIndex, itemIndex + 1);
        }
    }

    private async Task UpdateExerciseList()
    {
        WorkoutItems.Clear();
        try
        {
            foreach (WorkoutTemplateModel e in CurrentLog.Instance.CurrentWorkoutTemplateGroup.WorkoutTemplates)
            {
                WorkoutItems.Add(e);
            }
        }
        catch (Exception e)
        {
            // await UserDialogs.Instance.AlertAsync(new AlertConfig()
            // {
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     Message = AppResources.PleaseCheckInternetConnection,
            //     Title = AppResources.ConnectionError
            // });

            await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                        AppResources.PleaseCheckInternetConnection,"Try again","");
            // await UserDialogs.Instance.AlertAsync(AppResources.PleaseCheckInternetConnection, AppResources.Error);
        }
    }
}

