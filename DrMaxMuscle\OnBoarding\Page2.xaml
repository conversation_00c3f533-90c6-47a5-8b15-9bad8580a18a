﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Shell.NavBarIsVisible="false"
             NavigationPage.HasNavigationBar="False"
             x:Class="DrMaxMuscle.OnBoarding.Page2">
    <Grid>
        <Image Source="page2" Aspect="AspectFill" />
        <StackLayout HorizontalOptions="Center"  x:Name="mainView" Margin="{OnPlatform Android= '0,100,0,50',iOS='0,150,0,50'}">
            <Image x:Name="ImgLogo" HeightRequest="130" WidthRequest="150" Source="logo1.png" />

            <StackLayout HorizontalOptions="CenterAndExpand" Margin="0,10,0,0">

                <Label TextColor="White" Margin="25,0" x:Name="LblTitle" Text="The only workout app&#10;with 23+ smart features" HorizontalOptions="CenterAndExpand" HorizontalTextAlignment="Center"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="21" FontAttributes="Bold" >
                   
                </Label>
                <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                    <Label TextColor="LightGray" x:Name="LblLine1" Text="• Automates everything"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    <Label TextColor="LightGray" x:Name="LblLine2" Text="• Workouts unique to you"  Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                    <Label TextColor="LightGray" x:Name="LblLine3" Text="• Speed up your progress" Style="{StaticResource OnBoardingLabelStyle}" FontSize="17" />
                </StackLayout>
                <StackLayout x:Name="ReviewStack" Padding="0,25,0,0">
                    <Image Margin="0,20,0,0" x:Name="ImgStar1" Source="stars_5.png" HeightRequest="{OnPlatform iOS='30'}" WidthRequest="90" VerticalOptions="Center" HorizontalOptions="CenterAndExpand" />
                    <StackLayout Orientation="Vertical" HorizontalOptions="CenterAndExpand">
                        <Label TextColor="White" FontAttributes="Italic" Text="&quot;Like a trainer in your pocket&quot;" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="14" x:Name="LblReview1" />
                        <Label TextColor="LightGray" Text="-Jon Benson" HorizontalOptions="CenterAndExpand" Style="{StaticResource OnBoardingLabelStyle}" FontSize="13"  x:Name="LblAuthor1" />
                    </StackLayout>
                </StackLayout>
            </StackLayout>
        </StackLayout>
    </Grid>
</ContentView>