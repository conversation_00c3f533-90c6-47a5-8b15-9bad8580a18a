﻿using CommunityToolkit.Maui.Views;
using 
    RGPopup.Maui.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Controls
{
    public class ContextMenuButton : ImageButton
    {
        private ContextMenuPage _contextMenuPage;

        #region ItemsContainerWidth

        public static readonly BindableProperty ItemsContainerWidthProperty = BindableProperty.Create(
            nameof(ItemsContainerWidth),
            typeof(double),
            typeof(ContextMenuButton),
            propertyChanged: OnItemsContainerWidthChanged);

        private static void OnItemsContainerWidthChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is ContextMenuButton contextMenuButton && contextMenuButton.Handler != null && contextMenuButton.Handler.PlatformView != null && newValue is double newValueDouble &&
                contextMenuButton._contextMenuPage != null)
            {
                contextMenuButton._contextMenuPage.ItemsContainerWidth = newValueDouble;
            }
        }

        public double ItemsContainerWidth
        {
            get { return (double)GetValue(ItemsContainerWidthProperty); }
            set { SetValue(ItemsContainerWidthProperty, value); }
        }

        #endregion

        #region ItemsContainerHeight

        public static readonly BindableProperty ItemsContainerHeightProperty = BindableProperty.Create(
            nameof(ItemsContainerHeight),
            typeof(double),
            typeof(ContextMenuButton),
            propertyChanged: OnItemsContainerHeightChanged);

        private static void OnItemsContainerHeightChanged(BindableObject bindable, object oldValue, object newValue)
        {
            if (bindable is ContextMenuButton contextMenuButton && contextMenuButton.Handler != null && contextMenuButton.Handler.PlatformView != null && newValue is double newValueDouble &&
                contextMenuButton._contextMenuPage != null)
            {
                contextMenuButton._contextMenuPage.ItemsContainerHeight = newValueDouble;
            }
        }

        public double ItemsContainerHeight
        {
            get { return (double)GetValue(ItemsContainerHeightProperty); }
            set { SetValue(ItemsContainerHeightProperty, value); }
        }

        #endregion

        #region Items

        private IEnumerable<MenuItem> _items;
        public IEnumerable<MenuItem> Items
        {
            get => _items;
            set
            {
                _items = value;
                OnPropertyChanged();
            }
        }

        #endregion

        public ContextMenuButton()
            : base()
        {
            Clicked += ContextMenuButton_Clicked;
        }

        public void ContextMenuButton_Clicked(object sender, EventArgs e)
        {
            try
            {

                _contextMenuPage = new ContextMenuPage(Items);
                _contextMenuPage.ItemsContainerHeight = ItemsContainerHeight;
                _contextMenuPage.ItemsContainerWidth = ItemsContainerWidth;
                SetContextMenuPosition();
                Navigation.PushPopupAsync(_contextMenuPage);
            }
            catch (Exception ex)
            {

            }
        }

        private void SetContextMenuPosition()
        {
            try
            {
                var coordinates = GetCoordinates.Invoke();
                var leftOffset = (int)(Width / 2);
                var leftCenter = coordinates.x + leftOffset;
                _contextMenuPage.SetPosition(leftCenter, leftOffset);
            }
            catch (Exception ex)
            {

            }
        }

        public Func<(int x, int y)> GetCoordinates = null;

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            if (Items == null)
                return;
            foreach (var item in Items)
            {
                item.BindingContext = this.BindingContext;
            }
        }
    }
}

