﻿using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.Android.Dependencies;
using AndroidEnv = Android.OS.Environment;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//[assembly: Dependency(typeof(DroidShareService))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class DroidShareService : Activity, IShareService
    {
        public async void Share(string subject, string message, Stream stream)
        {
            try
            {
                var intent = new Intent(Intent.ActionSend);
                //intent.PutExtra(Intent.ExtraSubject, subject);
                intent.SetFlags(ActivityFlags.NewTask);
                intent.PutExtra(Intent.ExtraText, message);
                intent.SetType("image/png");

                Bitmap bitmap = BitmapFactory.DecodeStream(stream);
                MemoryStream ms = new MemoryStream();

                PermissionStatus status = await Permissions.CheckStatusAsync<Permissions.StorageWrite>();
                if (Build.VERSION.SdkInt < BuildVersionCodes.R)
                {
                    if (status != PermissionStatus.Granted)
                    {
                        status = await Permissions.RequestAsync<Permissions.StorageWrite>();

                        if (status != PermissionStatus.Granted && !Permissions.ShouldShowRationale<Permissions.StorageWrite>())
                        {
                            bool userChoice = await App.Current.MainPage.DisplayAlert("Permission Needed", "Please allow storage permission from settings.", "Open Settings", "Cancel");
                            if (userChoice)
                                AppInfo.ShowSettingsUI();

                            return;
                        }
                    }
                }

                if (status == PermissionStatus.Granted || Build.VERSION.SdkInt >= BuildVersionCodes.R)
                {

                    try
                    {


                        //Environment.DirectoryDownloads
                        var path = AndroidEnv.GetExternalStoragePublicDirectory(AndroidEnv.DirectoryPictures
                            + Java.IO.File.Separator + $"DrMuscle_stats_{DateTime.Now:yyyyMMdd_hhmmss}.png");
                        if (bitmap != null)
                        {
                            using (var os = new System.IO.FileStream(path.AbsolutePath, System.IO.FileMode.Create))
                            {
                                bitmap.Compress(Bitmap.CompressFormat.Png, 100, os);
                            }

                            var imageUri = FileProvider.GetUriForFile(MainActivity._currentActivity, $"{AppInfo.PackageName}.fileprovider", path);
                            intent.PutExtra(Intent.ExtraStream, imageUri);
                        }
                        MainActivity._currentActivity.StartActivity(Intent.CreateChooser(intent, "Share Image"));
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
    }
}