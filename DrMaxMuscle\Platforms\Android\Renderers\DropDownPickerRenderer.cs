﻿using Android.App;
using Android.Widget;
using DrMaxMuscle.Controls;
using Android.Views;
using Android.Content;
using Microsoft.Maui.Handlers;
using View = Android.Views.View;
using Microsoft.Maui.Controls.Compatibility;
using DrMaxMuscle.Platforms.Android.Renderers;
using Color = Android.Graphics.Color;
using Button = Android.Widget.Button;
using Microsoft.Maui.Platform;
using Microsoft.Maui.Graphics;
using Paint = Android.Graphics.Paint;
using Android.Graphics.Drawables;
using Microsoft.Maui.Controls.Platform;
using Android.Graphics;
using Android.Util;
using Android.Hardware.Lights;
using static Android.Icu.Text.ListFormatter;
using AndroidX.Core.Content;
using DrMaxMuscle.Resx;

[assembly: ExportRenderer(typeof(DropDownPicker), typeof(DropDownPickerRenderer))]
namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class DropDownPickerRenderer : ViewHandler<DropDownPicker, EditText>
    {
        public static IPropertyMapper<DropDownPicker, DropDownPickerRenderer> Mapper = new PropertyMapper<DropDownPicker, DropDownPickerRenderer>(ViewMapper)
        {
            // Define property mappings if needed (e.g., "Title" or "SelectedIndex")
            [nameof(DropDownPicker.Image)] = MapImage,
            [nameof(DropDownPicker)] = MapRemoveUnderline,
        };

        public DropDownPickerRenderer() : base(Mapper) // ✅ Pass Mapper here
        {
        }

        public DropDownPickerRenderer(IPropertyMapper mapper) : base(mapper)
        {
        }

        public static void MapImage(DropDownPickerRenderer handler, DropDownPicker picker)
        {
            try
            {
                if (!string.IsNullOrEmpty(picker.Image) && handler != null && handler.PlatformView != null)
                {
                    var drawable = GetDrawable(handler, picker.Image);
                    handler.PlatformView.SetCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
                    picker.TextColor = Colors.White;
                }
            }
            catch (Exception ex)
            {
            }
        }
        public static void MapRemoveUnderline(DropDownPickerRenderer handler, DropDownPicker picker)
        {
            try
            {
                if (handler != null && handler.PlatformView != null)
                {
                    // Remove the default underline (bottom border)
                    handler.PlatformView.Background = null;
                }
            }
            catch (Exception ex)
            {
            }
        }
        private static Drawable GetDrawable(DropDownPickerRenderer handler, string imagePath)
        {
            imagePath = imagePath.Replace(".png", "");
            int resID = handler.MauiContext.Context.Resources.GetIdentifier(imagePath, "drawable", handler.MauiContext.Context.PackageName);
            return ContextCompat.GetDrawable(handler.MauiContext.Context, resID);
        }
        protected override EditText CreatePlatformView()
        {
            var edit = new EditText(MainActivity.Instance);

            
            //var edit = new EditText(Context ?? MauiApplication.Current.ApplicationContext);

            // Set the placeholder
            edit.Hint = VirtualView?.Title ?? VirtualView?.Items?.FirstOrDefault() ?? "Select"; // Avoid null reference

            // Disable selection, cursor, etc
            edit.SetCursorVisible(false);
            edit.Focusable = false;
            edit.FocusableInTouchMode = false;
            edit.SetTextColor(Color.White);
            edit.SetBackgroundColor(Color.Transparent);
            edit.SetHintTextColor(Color.White);
            edit.TextSize = 15;
            edit.Click += (s, e) =>
            {
                var p = this.VirtualView as DropDownPicker;
                if (p == null || p.Items == null || p.Items.Count == 0 || (p.Items.Count == 1 && p.Items.FirstOrDefault() == AppResources.AverageOfAllRecentExercises)) return;
                var items = p.Items;

                edit.Hint = p.Items.FirstOrDefault();

                 var n = new CustomNumberPicker(MainActivity.Instance);
                n.MinValue = 0;
                n.MaxValue = items.Count - 1;

                // Set this to true if looping is required
                n.WrapSelectorWheel = false;

                // If there is a value already selected, select it
                var lastSelectedIndex = p.SelectedIndex != -1 ? p.SelectedIndex : 0;
                n.Value = lastSelectedIndex;

                n.SetBackgroundColor(Color.Transparent);
                n.SetTextColor(Color.White);

                n.SetDisplayedValues(items.ToArray<string>());

                AlertDialog.Builder alertDialog = new AlertDialog.Builder(MainActivity.Instance, Resource.Style.AlertDialogCustomGray);
                var dlg = alertDialog
                    .SetTitle(p.Title)
                    .SetView(n)
                    .SetPositiveButton("OK", (s, e) =>
                    {
                        p.SelectedIndex = n.Value;
        
                        edit.Text = p.Items[n.Value].ToString(); // Assign the selected value to EditText
                        edit.SetTextColor(Color.White);
                        edit.SetBackgroundColor(Color.Transparent);
                    })
                    .SetNegativeButton("CANCEL", (s, e) => { }) // Optional Cancel button
                    .Create();

                // ✅ Set custom gradient background
                dlg.SetOnShowListener(new IDialogInterfaceOnShowListenerWrapper(() =>
                {
                    if (dlg.Window != null)
                    {
                        dlg.Window.SetBackgroundDrawableResource(Resource.Drawable.gradient_background); // Apply gradient
                    }

                    // ✅ Style buttons
                    Button positiveButton = dlg.GetButton((int)DialogButtonType.Positive);
                    if (positiveButton != null)
                    {
                        positiveButton.SetTextColor(Color.White);
                    }

                    Button negativeButton = dlg.GetButton((int)DialogButtonType.Negative);
                    if (negativeButton != null)
                    {
                        negativeButton.SetTextColor(Color.LightGray);
                    }
                }));


                dlg.Show();
            };
            return edit;
        }
    }


    public class IDialogInterfaceOnShowListenerWrapper : Java.Lang.Object, IDialogInterfaceOnShowListener
    {
        private readonly Action _onShowAction;

        public IDialogInterfaceOnShowListenerWrapper(Action onShowAction)
        {
            _onShowAction = onShowAction;
        }

        public void OnShow(IDialogInterface dialog)
        {
            _onShowAction?.Invoke();
        }
    }

    public class CustomNumberPicker : NumberPicker
    {
        private Paint _paint;

        public CustomNumberPicker(Context context) : base(context)
        {
            Initialize();
        }

        public CustomNumberPicker(Context context, IAttributeSet attrs) : base(context, attrs)
        {
            Initialize();
        }

        private void Initialize()
        {
            _paint = new Paint
            {
                Color = Color.White, // Set the divider color to white
                StrokeWidth = 5, // Divider thickness
                AntiAlias = true
            };
        }

        protected override void OnDraw(Canvas canvas)
        {
            base.OnDraw(canvas);

            // Get the height and width of the view
            int height = Height;
            int width = Width;

            // Get the position of the existing dividers by considering the height and min/max values of the picker
            int dividerHeight = (height+40) / 3;  // Adjust the height of the divider to fit your needs

            // Calculate dynamic Y position for top and bottom dividers
            int topDividerY = dividerHeight;
            int bottomDividerY = height - dividerHeight;

            // Draw the custom white divider
            canvas.DrawLine(0, topDividerY, width, topDividerY, _paint); // Top divider
            canvas.DrawLine(0, bottomDividerY, width, bottomDividerY, _paint); // Bottom divider
        }
    }

    //public class DropDownPickerRenderer : PickerHandler
    //{
    //    public static IPropertyMapper<DropDownPicker, DropDownPickerRenderer> Mapper = new PropertyMapper<DropDownPicker, DropDownPickerRenderer>(PickerHandler.Mapper)
    //    {
    //        [nameof(DropDownPicker.Image)] = MapImage,
    //        [nameof(DropDownPicker)] = MapRemoveUnderline,
    //    };

    //    public DropDownPickerRenderer() : base(Mapper)
    //    {
    //    }

    //    public DropDownPickerRenderer(IPropertyMapper mapper) : base(mapper)
    //    {
    //    }

    //    public static void MapImage(DropDownPickerRenderer handler, DropDownPicker picker)
    //    {
    //        try
    //        {
    //            if (!string.IsNullOrEmpty(picker.Image) && handler != null && handler.PlatformView != null)
    //            {
    //                var drawable = GetDrawable(handler, picker.Image);
    //                handler.PlatformView.SetCompoundDrawablesWithIntrinsicBounds(null, null, drawable, null);
    //            }
    //        }
    //        catch (Exception ex)
    //        {
    //        }
    //    }
    //    public static void MapRemoveUnderline(DropDownPickerRenderer handler, DropDownPicker picker)
    //    {
    //        try
    //        {
    //            if (handler != null && handler.PlatformView != null)
    //            {
    //                // Remove the default underline (bottom border)
    //                handler.PlatformView.Background = null;
    //            }
    //        }
    //        catch (Exception ex)
    //        {
    //        }
    //    }
    //    private static Drawable GetDrawable(DropDownPickerRenderer handler, string imagePath)
    //    {
    //        imagePath = imagePath.Replace(".png", "");
    //        int resID = handler.MauiContext.Context.Resources.GetIdentifier(imagePath, "drawable", handler.MauiContext.Context.PackageName);
    //        return ContextCompat.GetDrawable(handler.MauiContext.Context, resID);
    //    }
    //}
    //public class DropDownPickerRenderer : PickerRenderer
    //{
    //    DropDownPicker element;

    //    public DropDownPickerRenderer(Context context) : base(context)
    //    {

    //    }

    //    protected override void OnElementChanged(ElementChangedEventArgs<Picker> e)
    //    {
    //        base.OnElementChanged(e);

    //        if (e.NewElement is DropDownPicker pickerElement)
    //        {
    //            element = pickerElement;
    //        }
    //        try
    //        {

    //            this.Element.TextColor = Colors.White;
    //            this.Element.BackgroundColor = Constants.AppThemeConstants.BlueColor;

    //            if (Control != null)
    //            {
    //                Control?.SetPadding(20, 4, 4, 8);
    //                Control.TextSize = 15;
    //                Control.SetBackgroundColor(Constants.AppThemeConstants.BlueColor.ToAndroid());

    //            }

    //        }
    //        catch (Exception ex)
    //        {

    //        }
    //        if (Control != null && this.Element != null && !string.IsNullOrEmpty(element.Image))
    //        {
    //            Control.Background = AddPickerStyles(element.Image);
    //            Control.SetPadding(20, 10, 80, 10);

    //        }
    //    }

    //    public LayerDrawable AddPickerStyles(string imagePath)
    //    {
    //        var border = new ShapeDrawable();
    //        border.Paint.Color = Color.Gray;
    //        border.SetPadding(10, 10, 10, 10);
    //        border.Paint.SetStyle(Paint.Style.Stroke);

    //        Drawable[] layers = { border, GetDrawable(imagePath) };
    //        LayerDrawable layerDrawable = new LayerDrawable(layers);
    //        layerDrawable.SetLayerInset(0, 0, 0, 0, 0);


    //        return layerDrawable;
    //    }

    //    private BitmapDrawable GetDrawable(string imagePath)
    //    {
    //        imagePath = imagePath.Replace(".png", "");
    //        int resID = Resources.GetIdentifier(imagePath, "drawable", this.Context.PackageName);
    //        var drawable = ContextCompat.GetDrawable(this.Context, resID);
    //        var bitmap = ((BitmapDrawable)drawable).Bitmap;

    //        var result = new BitmapDrawable(Resources, Bitmap.CreateScaledBitmap(bitmap, 70, 37, true));
    //        result.Gravity = GravityFlags.Right;

    //        return result;
    //    }
    //}
}