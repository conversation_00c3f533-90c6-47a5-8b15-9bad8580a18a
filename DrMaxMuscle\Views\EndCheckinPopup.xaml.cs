using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Utility;
using DrMuscleWebApiSharedModel;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Legends;
using OxyPlot.Series;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Globalization;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class EndCheckinPopup : PopupPage
{
    string strFacebook = "";
    bool isEstimated = false;
    long exerciseId = 0;
    private bool isPresented = false;
    private decimal _userBodyWeight = 0;
    private bool isHistoryLoaded = false;
    public PlotModel plotModel = null;
    private Dictionary<double, string> IndexToDateLabel = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel2 = new Dictionary<double, string>();
    private Dictionary<double, string> IndexToDateLabel3 = new Dictionary<double, string>();

    public EndCheckinPopup(UserWeight weight0, UserWeight weight1, UserWeight weight2)
    {
        InitializeComponent();
        Device.BeginInvokeOnMainThread(() =>
        {

            try
            {

                RefreshLocalized();
                //MyParticleCanvas.ParticleColors = AppThemeConstants.CalculateConfettieColors();
                NextExerciseButton.Clicked += NextExerciseButton_Clicked;
                MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
                {
                    RefreshLocalized();
                });

                MessagingCenter.Subscribe<Message.ReceivedWatchMessage>(this, "ReceivedWatchMessage", (obj) =>
                {
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        if (obj.PhoneToWatchModel.WatchMessageType == WatchMessageType.NextExercise)
                            NextExerciseButton_Clicked(NextExerciseButton, EventArgs.Empty);
                    });

                });
                if (plotView != null)
                {
                    plotView.Controller = new PlotController();
                    plotView.Controller?.UnbindMouseDown(OxyMouseButton.Left); // Disable default behavior for left click (single tap)
                    plotView.Controller?.UnbindTouchDown(); // Disable touch down events
                    if (plotModel == null)
                    {
                        plotModel = InitializePlotModel();
                        plotView.Model = plotModel;
                    }
                }
                LoadBeforeServerCall(weight0, weight1, weight2);
            }
            catch (Exception ex)
            {

            }
        });

    }

    public async void LoadBeforeServerCall(UserWeight weight0, UserWeight weight1, UserWeight weight2)
    {
        try
        {
            //Uncomment code please
            //DependencyService.Get<IFirebase>().SetScreenName("end_checkin_page");
            lblResult1.IsVisible = true;

            lblPercentage.Text = "";
            lblResult1.Text = "";
            lblLastTimeWeightTrend.Text = "";
            lblTodayWeightTrend.Text = "";
            string massunit = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg" ? "kg" : "lbs";

            NextExerciseButton.Text = AppResources.Continue;
            var days = 0;
            var dayStr = "";
            string weightForPrompt = "";
            var isKg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            if (!isKg)
            {
                if (weight0 != null)
                {
                    weight0.Weight = Math.Round(new MultiUnityWeight(weight0.Weight, "kg").Lb, 1);
                    weight0.TrendWeight = Math.Round(new MultiUnityWeight(weight0.TrendWeight ?? 0, "kg").Lb, 1);
                }
                if (weight1 != null)
                {
                    weight1.Weight = Math.Round(new MultiUnityWeight(weight1.Weight, "kg").Lb, 1);
                    weight1.TrendWeight = Math.Round(new MultiUnityWeight(weight1.TrendWeight ?? 0, "kg").Lb, 1);
                }
                if (weight2 != null)
                {
                    weight2.Weight = Math.Round(new MultiUnityWeight(weight2.Weight, "kg").Lb, 1);
                    weight2.TrendWeight = Math.Round(new MultiUnityWeight(weight2.TrendWeight ?? 0, "kg").Lb, 1);
                }
            }
            if (weight0 != null && weight1 != null)
            {
                days = (int)((DateTime)weight0.CreatedDate.Date - (DateTime)weight1.CreatedDate.Date).TotalDays;
                if (days == 0)
                    days = 1;
                dayStr = days > 1 ? "days" : "day";

                if (Math.Round(weight0.Weight, 1) == Math.Round(weight1.Weight, 1))
                {
                    //WeightArrowText.Text = $"Body weight is stable in the last {days} {dayStr}.";
                    weightForPrompt = $"Stable in {days} {dayStr}";
                }
                else if (Math.Round(weight0.Weight, 1) >= Math.Round(weight1.Weight, 1))
                {
                    var progress = (weight0.Weight - weight1.Weight) * 100 / weight1.Weight;
                    var weightChange = Math.Round(weight0.Weight, 1) - Math.Round(weight1.Weight, 1);
                    //WeightArrowText.Text = "Body weight +" + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;
                    weightForPrompt = "+" + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;
                    //WeightArrowText.Text = $"{WeightArrowText.Text} in the last {days} {dayStr}.";
                    weightForPrompt = $"{weightForPrompt} in {days} {dayStr}";

                }
                else
                {
                    var progress = (weight0.Weight - weight1.Weight) * 100 / weight1.Weight;
                    //LblWeightGoal2.Text = "Weight -" + String.Format("{0:0.##}%", Math.Round(progress, 2)).ReplaceWithDot().Replace("-", ""); ;
                    var weightChange = Math.Round(weight0.Weight, 1) - Math.Round(weight1.Weight, 1);
                    //WeightArrowText.Text = "Body weight " + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%)", Math.Round(progress, 1)).ReplaceWithDot().Replace("-", ""); ;
                    //WeightArrowText.Text = "Body weight " + String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;
                    weightForPrompt = String.Format("{0:0.#}", Math.Round(weightChange, 1)) + " " + massunit;
                    //WeightArrowText.Text = $"{WeightArrowText.Text} in the last {days} {dayStr}.";
                    weightForPrompt = $"{weightForPrompt} in {days} {dayStr}";
                }
                if (Math.Round(weight0.TrendWeight ?? 0, 1) == Math.Round(weight1.TrendWeight ?? 0, 1))
                {
                    decimal goalWeight = 0;
                    decimal currentWeight = Math.Round(weight0.TrendWeight ?? 0, 1);

                    // Retrieve the goal weight from the local database
                    if (LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value != null)
                    {
                        goalWeight = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.ReplaceWithDot(), CultureInfo.InvariantCulture);
                    }

                    // Determine whether the user is trying to gain or lose weight
                    if (goalWeight > currentWeight) // User's goal is to gain weight
                    {
                        lblPercentage.Text = $"+0 {(isKg ? "kg" : "lbs")}";
                    }
                    else // User's goal is to lose weight
                    {
                        lblPercentage.Text = $"-0 {(isKg ? "kg" : "lbs")}";
                    }
                    lblResult1.Text = "Trend is stable";
                    lblLastTimeWeightTrend.Text = $"{Math.Round(weight1.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                    lblTodayWeightTrend.Text = $"{Math.Round(weight0.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                }
                else if (Math.Round(weight0.TrendWeight ?? 0, 1) >= Math.Round(weight1.TrendWeight ?? 0, 1))
                {
                    var progress = (weight0.TrendWeight - weight1.TrendWeight) * 100 / weight1.TrendWeight;
                    var weightTrendChange = Math.Round(weight0.TrendWeight ?? 0, 1) - Math.Round(weight1.TrendWeight ?? 0, 1);

                    weightForPrompt = $"{weightForPrompt} (trend suggests +" + String.Format("{0:0.#}", Math.Round(weightTrendChange, 1)) + " " + massunit + ")";
                    lblPercentage.Text = string.Format("{0}{1:0.#} {2}", weightTrendChange > 0 ? "+" : "", weightTrendChange, massunit).ReplaceWithDot();
                    lblResult1.Text = "Trending up";

                    lblLastTimeWeightTrend.Text = $"{Math.Round(weight1.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                    lblTodayWeightTrend.Text = $"{Math.Round(weight0.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                }
                else
                {
                    var progress = (weight0.TrendWeight - weight1.TrendWeight) * 100 / weight1.TrendWeight;
                    //LblWeightGoal2.Text = LblWeightGoal2.Text + "\u2014Trend -" + String.Format("{0:0.##}%", Math.Round(progress ?? 0, 2)).ReplaceWithDot().Replace("-", ""); ;
                    var weightTrendChange = Math.Round(weight0.TrendWeight ?? 0, 1) - Math.Round(weight1.TrendWeight ?? 0, 1);
                    //WeightArrowText.Text = $"{WeightArrowText.Text} Trend " + String.Format("{0:0.#}", Math.Round(weightTrendChange ?? 0, 1)) + " " + massunit + " (-" + String.Format("{0:0.#}%).", Math.Round(progress ?? 0, 2)).ReplaceWithDot().Replace("-", ""); ;
                    //WeightArrowText.Text = $"{WeightArrowText.Text} Trend " + String.Format("{0:0.#}", Math.Round(weightTrendChange ?? 0, 1)) + " " + massunit;
                    weightForPrompt = $"{weightForPrompt} (trend suggests " + String.Format("{0:0.#}", Math.Round(weightTrendChange, 1)) + " " + massunit + ")";
                    lblPercentage.Text = string.Format("{0}{1:0.#} {2}", weightTrendChange > 0 ? "+" : "", weightTrendChange, massunit).ReplaceWithDot();
                    lblResult1.Text = "Trending down";
                    lblLastTimeWeightTrend.Text = $"{Math.Round(weight1.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                    lblTodayWeightTrend.Text = $"{Math.Round(weight0.TrendWeight ?? 0, 1)} {(isKg ? "kg" : "lbs")}";
                }
            }
            else
            {
                //WeightArrowText.Text = "In the last 1 day.";
                //LblWeightGoal2.Text = "Weight is stable"+ "\u2014Trend is stable";
                //WeightArrowText.Text = "Body weight and trend is stable.";
                // weightForPrompt = WeightArrowText.Text;
                lblPercentage.Text = "0";
                lblResult1.Text = "Trend is stable";
            }

            //Graph
            List<DataPoint> weightData = new List<DataPoint>();
            List<DataPoint> weightTrendData = new List<DataPoint>();
            var last3points = new List<UserWeight>();
            if (weight2 != null)
                last3points.Add(weight2);
            last3points.Add(weight1);
            last3points.Add(weight0);



            int index = 1;
            int index2 = 1;
            int index3 = 1;
            var minFatVal = 0;
            var maxFatVal = 0;
            var minFatTrendVal = 0;
            var maxFatTrendVal = 0;

            var minFFMVal = 0;
            var maxFFMVal = 0;
            var minFFMTrendVal = 0;
            var maxFFMTrendVal = 0;

            IndexToDateLabel.Clear();
            IndexToDateLabel2.Clear();
            IndexToDateLabel3.Clear();
            foreach (var weight in last3points)
            {
                decimal val = 0;
                decimal trendval = 0;
                val = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight.Weight, 2)));
                trendval = Decimal.Parse(string.Format("{0:0.##}", Math.Round(weight?.TrendWeight ?? 0, 2)));
                decimal fatfreemass = 0;
                decimal fatfreemassTrend = 0;
                weightData.Add(new DataPoint(index2, (double)val));
                weightTrendData.Add(new DataPoint(index2, (double)trendval));
                IndexToDateLabel2[index2] = weight.CreatedDate.ToString("MMM dd", CultureInfo.InvariantCulture);
                index2++;
            }

            var weightMin = (double)last3points.Min(a => a.Weight);
            var weightMax = (double)last3points.Max(a => a.Weight);
            var trendMin = (double)last3points.Min(a => a.TrendWeight);
            var trendMax = (double)last3points.Max(a => a.TrendWeight);
            var minVal = (weightMin > trendMin) ? trendMin : weightMin;
            var maxVal = (weightMax > trendMax) ? weightMax : trendMax;
            if (minVal == maxVal)
            {
                minVal = minVal - 10;
                maxVal = maxVal + 10;
            }
            var weightDateMin = last3points.Min(a => a.CreatedDate);
            var weightDateMax = last3points.Max(a => a.CreatedDate);
            var min = minVal - (maxVal - minVal) * 0.3;
            var max = maxVal + (maxVal - minVal) * 0.2;

            var minVal2 = (minFatVal > minFatTrendVal) ? minFatTrendVal : minFatVal;
            var maxVal2 = (maxFatVal > maxFatTrendVal) ? maxFatVal : maxFatTrendVal;

            var min2 = minVal2 - (maxVal2 - minVal2) * 0.3;
            var max2 = maxVal2 + (maxVal2 - minVal2) * 0.2;

            ////////FFM

            var minVal3 = (minFFMVal > minFFMTrendVal) ? minFFMTrendVal : minFFMVal;
            var maxVal3 = (maxFFMVal > maxFFMTrendVal) ? maxFFMVal : maxFFMTrendVal;

            var min3 = minVal3 - (maxVal3 - minVal3) * 0.3;
            var max3 = maxVal3 + (maxVal3 - minVal3) * 0.2;

            // Add the series to the plot model
            (plotModel.Series[0] as LineSeries).Points.Clear();
            foreach (var point in weightData)
            {
                (plotModel.Series[0] as LineSeries).Points.Add(point);
            }

            (plotModel.Series[1] as LineSeries).Points.Clear();
            foreach (var point in weightTrendData)
            {
                (plotModel.Series[1] as LineSeries).Points.Add(point);
            }
            await UpdateAxisLimits(plotModel, min, max, "weight");
            plotView.Model.InvalidatePlot(true);
        }
        catch (Exception ex)
        {

        }
    }


    protected async Task<bool> ConnectionErrorConfirmPopup()
    {
        if (isPresented)
            return true;
        isPresented = true;

        try
        {
        var results = await HelperClass.DisplayCustomPopupForResult("Loading error","Slow or no connection. Please check and try again.","Retry loading","Cancel");



        // ConfirmConfig ShowAlertPopUp = new ConfirmConfig()
        // {
        //     Title = "Loading error",
        //     Message = "Slow or no connection. Please check and try again.",
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray) ,
        //     OkText = "Retry loading",
        //     CancelText = "Cancel",

        // };
        // var results = await UserDialogs.Instance.ConfirmAsync(ShowAlertPopUp);
        isPresented = false;
        if (results == PopupAction.OK)
        {
            return true;
        }
        }
        catch (Exception ex)
        {

        }
        return false;
    }
    public async void OnBeforeShow()
    {


    }


    private void RefreshLocalized()
    {
        NextExerciseButton.Text = Resx.AppResources.NextExercise;
    }

    private async void NextExerciseButton_Clicked(object sender, EventArgs e)
    {
        try
        {
            await MauiProgram.SafeDismissTopPopup();
            //if (PopupNavigation.Instance.PopupStack?.Count > 0)
            //    await PopupNavigation.Instance.PopAsync();
        }
        catch (Exception ex)
        {

        }
    }



    private string _formatter(double d)
    {
        return IndexToDateLabel.ContainsKey(d) ? IndexToDateLabel[d] : "";
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {

            //MyParticleCanvas.IsActive = false;
            //MyParticleCanvas.IsRunning = false;
            //MyParticleCanvas = null;

        }
        catch (Exception ex)
        {

        }
    }

    void TapGestureRecognizer_Tapped(System.Object sender, System.EventArgs e)
    {
        Browser.OpenAsync("http://dr-muscle.com/deload/", BrowserLaunchMode.SystemPreferred);
        //Device.OpenUri(new Uri("http://dr-muscle.com/deload/"));
    }

    async void ShareExerciseButton_Clicked(System.Object sender, System.EventArgs e)
    {
        //get the image
        var ImageStream = await ScrollContentToShare.CaptureAsync();
        //set path of file
        //var fileName = Path.Combine(FileSystem.CacheDirectory, $"workout_stat_{DateTime.Now:yyyyMMdd_hhmmss}.jpg");

        HelperClass.ShareImage(await ImageStream.OpenReadAsync(), "weight", firebaseEventName: "checkin_stats_share");
    }

    //Graph
    private PlotModel InitializePlotModel()
    {
        var series = new LineSeries
        {
            MarkerType = MarkerType.Circle,
            MarkerSize = 5,
            Color = OxyColor.Parse("#195377"),
            TextColor = OxyColor.Parse("#195377"),
            LabelFormatString = "{1:#.#;#}",
            Title = "Weight",
            MarkerStroke = OxyColor.Parse("#195377"),
            MarkerFill = OxyColor.Parse("#195377"),
            MarkerStrokeThickness = 1,
            FontSize = 10
        };
        var trendColor = OxyColor.Parse("#000000");
        var series1 = new LineSeries
        {
            MarkerType = MarkerType.Circle,
            MarkerSize = 5,
            MarkerStrokeThickness = 1,
            LabelFormatString = "{1:#.#;#}",
            Color = trendColor,
            TextColor = trendColor,
            MarkerStroke = trendColor,
            MarkerFill = trendColor,
            FontSize = 10,
            LabelMargin = 5,
            Title = "Trend     "
        };
        //Create the plot model
        var plotModelWeight = new PlotModel
        {
            Background = OxyColors.Transparent,
            PlotAreaBackground = OxyColors.Transparent,
            PlotAreaBorderColor = OxyColor.Parse("#23253A"),
            
            IsLegendVisible = true
        };
        var legend = new Legend
        {
            LegendPosition = LegendPosition.BottomCenter, // Position of the legend
            LegendPlacement = LegendPlacement.Outside, // Place legend outside the plot area
            LegendOrientation = LegendOrientation.Horizontal, // Arrange legend items vertically
            LegendFontSize = 12, // Font size of legend items
            LegendTextColor = OxyColors.Black, // Color of the legend text
            LegendSymbolLength = 24, // Length of the legend symbol (e.g., line or marker)
            LegendSymbolMargin = 8, // Margin between legend symbol and text
        };

        plotModelWeight.Legends.Add(legend);
        plotModelWeight.Series.Add(series);
        plotModelWeight.Series.Add(series1);

        plotModelWeight.Axes.Add(new DateTimeAxis
        {
            Position = AxisPosition.Bottom,
            StringFormat = "MMM dd",
            LabelFormatter = _formatter2,
            MinimumPadding = 0.05,
            MaximumPadding = 0.05,
            ExtraGridlineColor = OxyColors.Transparent,
            MajorGridlineColor = OxyColors.Transparent,
            MinorGridlineColor = OxyColors.Transparent
        });
        plotModelWeight.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Minimum = 30, Maximum = 70, });
        plotModelWeight.TitleHorizontalAlignment = TitleHorizontalAlignment.CenteredWithinView;


        return plotModelWeight;
    }

    private async Task UpdateAxisLimits(PlotModel plotModel, double minimum, double maximum, string plot = "")
    {
        var yAxis = plotModel.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left) as LinearAxis;
        if (yAxis != null)
        {
            if (maximum - minimum > 2)
            {
                // Optionally add some padding or use specific logic to determine min and max
                yAxis.Minimum = minimum;
                yAxis.Maximum = maximum;
            }
            else
            {
                maximum = maximum + 2;
                yAxis.Minimum = minimum;
                yAxis.Maximum = maximum;
            }

        }
        if (plot == "weight")
        {
            int digitCount = Math.Round(maximum).ToString().Length;
            var newValue = 4 * digitCount;
            var rightMarginValue = 20 + newValue;
            WeightArrowText.Margin = new Thickness(rightMarginValue, 0, 0, 0);
        }

    }

    private string _formatter2(double d)
    {
        if (IndexToDateLabel2.TryGetValue(d, out var label))
        {
            return label; // Return the label if found
        }

        return "";
    }

    private void InfoIcon_Tapped2(object sender, EventArgs e)
    {
        MessagingCenter.Send<GeneralMessage>(new GeneralMessage() { PopupEnum = Enums.GeneralPopupEnum.TrendInfo }, "GeneralMessage");
    }
}