﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
            xmlns:local="clr-namespace:DrMaxMuscle.Cells"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
            xmlns:app="clr-namespace:DrMaxMuscle.Constants"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Screens.User.ChatView">
    <ContentView.Resources>
        <ResourceDictionary>
            <local:ChatDataTemplateSelector
            x:Key="MessageTemplateSelector">
            </local:ChatDataTemplateSelector>
        </ResourceDictionary>
    </ContentView.Resources>
    <ContentView.Content>
        <AbsoluteLayout>
            <Grid
            x:Name="ChatMainView"
            BackgroundColor="#f4f4f4"
            AbsoluteLayout.LayoutFlags="All"
            AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            RowSpacing="0"
            Padding="2,2,2,0">
                <Grid.RowDefinitions>
                    <RowDefinition
                    Height="*" />
                    <RowDefinition
                    Height="1" />
                    <RowDefinition
                    Height="auto" />
                </Grid.RowDefinitions>

                <StackLayout
                Grid.Row="0"
                Spacing="2">
                    <CollectionView
                        Rotation="180"
                        BackgroundColor="Transparent"
                        ItemTemplate="{StaticResource MessageTemplateSelector}"
                        ItemsSource="{Binding messageList}"
                        Margin="0"
                        SelectionChanged="lstChats_SelectionChanged"
                        FlowDirection="RightToLeft"
                        x:Name="lstChats"
                        VerticalOptions="StartAndExpand"
                        SelectionMode="None"
                        >
                        
                    </CollectionView>
                    <!--<controls:ExtendedListView
                    Rotation="180"
                    BackgroundColor="Transparent"
                    ItemTemplate="{StaticResource MessageTemplateSelector}"
                    ItemsSource="{Binding messageList}"
                    Margin="0"
                    ItemTapped="OnListTapped"
                    FlowDirection="RightToLeft"
                    HasUnevenRows="True"
                    x:Name="lstChats"
                    VerticalOptions="StartAndExpand"
                    SeparatorColor="Transparent"
                    ItemAppearing="Handle_ItemAppearing"
                    ItemDisappearing="Handle_ItemDisappearing">
                    </controls:ExtendedListView>-->

                </StackLayout>
                <!--            </ScrollView>-->
                <BoxView
                HorizontalOptions="FillAndExpand"
                HeightRequest="1"
                BackgroundColor="LightGray"
                Grid.Row="1" />
                <controls:ChatInputBarView
                Grid.Row="2"
                Margin="0,0,0,0"
                Tapped="BtnSendTapGestureRecognizer_Tapped"
                x:Name="chatInput" />
                <t:DrMuscleButton
                Grid.Row="2"
                x:Name="NotPurchased"
                BackgroundColor="Transparent"
                IsVisible="false"
                Clicked="NotPurchased_Clicked" />
                <BoxView
                x:Name="BxTooltip"
                Grid.Row="0"
                HorizontalOptions="End"
                Margin="0,-2,45,0"
                WidthRequest="2"
                VerticalOptions="Start"
                HeightRequest="2"
                BackgroundColor="Transparent" />
            </Grid>
            <Grid
            BackgroundColor="#f4f4f4"
            x:Name="SupportMainView"
            AbsoluteLayout.LayoutFlags="All"
            AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            RowSpacing="0"
            Padding="2,2,2,0">
                <Grid.RowDefinitions>
                    <RowDefinition
                    Height="*" />
                    <RowDefinition
                    Height="1" />
                    <RowDefinition
                    Height="auto" />
                </Grid.RowDefinitions>
                <StackLayout
                HorizontalOptions="FillAndExpand"
                Orientation="Vertical"
                Grid.Row="0">
                    <StackLayout
                    x:Name="StackInfo"
                    IsVisible="true"
                    Padding="8,5,0,2"
                    Spacing="2">
                        <!--<Label x:Name="LblEmail" FontAttributes="Bold" HorizontalOptions="FillAndExpand" TextColor="White" FontSize="Default" />
            <BoxView x:Name="BoxBorder" IsVisible="false" HeightRequest="0.5" BackgroundColor="LightGray" />-->
                    </StackLayout>
                    <CollectionView
                        Rotation="180"
                    BackgroundColor="Transparent"
                    ItemTemplate="{StaticResource MessageTemplateSelector}"
                    ItemsSource="{Binding messageList}"
                    Margin="7,0,7,0"
                    SelectionChanged="lstChats_SelectionChanged"
                    FlowDirection="RightToLeft"
                    x:Name="lstSupportChats"
                    VerticalOptions="FillAndExpand"
                    SelectionMode="None"
                        >

                    </CollectionView>
                    <!--<controls:ExtendedListView
                    Rotation="180"
                    BackgroundColor="Transparent"
                    ItemTemplate="{StaticResource MessageTemplateSelector}"
                    ItemsSource="{Binding messageList}"
                    Margin="7,0,7,0"
                    ItemTapped="OnListTapped"
                    FlowDirection="RightToLeft"
                    HasUnevenRows="True"
                    x:Name="lstSupportChats"
                    VerticalOptions="FillAndExpand"
                    SeparatorColor="Transparent"
                    ItemAppearing="Handle_ItemAppearing"
                    ItemDisappearing="Handle_ItemDisappearing">
                    </controls:ExtendedListView>-->
                    <t:DrMuscleButton
                    x:Name="AiButton"
                    IsVisible="false"
                        CornerRadius="2"
                    Text="How do I build muscle?"
                    Clicked="AiButton_Clicked"
                    HorizontalOptions="StartAndExpand"
                    Padding="20,16"
                    Margin="8,10"
                    VerticalOptions="End"
                    BackgroundColor="Gray"
                    TextColor="White" />
                    <Grid
                    x:Name="DrMuscleTyping"
                    FlowDirection="LeftToRight"
                    ColumnSpacing="5"
                    IsVisible="false"
                    RowSpacing="0"
                    Padding="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition
                            Width="40">
                            </ColumnDefinition>
                            <ColumnDefinition
                            Width="*">
                            </ColumnDefinition>

                            <ColumnDefinition
                            Width="40">
                            </ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition
                            Height="auto">
                            </RowDefinition>
                            <RowDefinition
                            Height="*">
                            </RowDefinition>
                        </Grid.RowDefinitions>
                        <ffimageloading:CachedImage
                            ErrorPlaceholder="backgroundblack.png"
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.RowSpan="2"
                        x:Name="imgInProfilePic"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        WidthRequest="35"
                        HeightRequest="35"
                        Source="icon_1">
                        </ffimageloading:CachedImage>
                        <StackLayout
                        Grid.Row="1"
                        Grid.Column="1"
                        VerticalOptions="End"
                        Orientation="Horizontal"
                        IsClippedToBounds="true"
                        HorizontalOptions="Start">

                            <Frame
                            Margin="0,10,15,5"
                            CornerRadius="12"
                            Padding="0,0,0,0"
                            Grid.Row="1"
                            Grid.Column="1"
                            VerticalOptions="End"
                            IsClippedToBounds="true"
                            HorizontalOptions="Start"
                            BorderColor="Transparent"
                            HasShadow="False"
                            BackgroundColor="Transparent">
                                <ffimageloading:CachedImage
                                    ErrorPlaceholder="backgroundblack.png"
                                Margin="-30,0,0,0"
                                HorizontalOptions="Start"
                                x:Name="ImgLoader"
                                Aspect="AspectFit"
                                Source="typing_loader.gif"
                                WidthRequest="{OnPlatform iOS='120', Android='120'}"
                                HeightRequest="{OnPlatform Android='60', iOS='70'}" />
                            </Frame>
                        </StackLayout>
                        <StackLayout
                        Grid.Row="0"
                        Grid.Column="1"
                        VerticalOptions="Start"
                        Orientation="Horizontal"
                        HorizontalOptions="Start">
                            <Label
                            FontAttributes="Bold"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            x:Name="nameLabel"
                            Text="Dr. Muscle AI"
                            TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">

                            </Label>
                            <Label
                            FontSize="Micro"
                            HorizontalOptions="Start"
                            HorizontalTextAlignment="Start"
                            VerticalOptions="End"
                            VerticalTextAlignment="End"
                            TextColor="{x:Static app:AppThemeConstants.BlueColor}">
                            </Label>
                        </StackLayout>
                    </Grid>
                </StackLayout>
                <BoxView
                IsVisible="false"
                HorizontalOptions="FillAndExpand"
                HeightRequest="1"
                BackgroundColor="LightGray"
                Grid.Row="1" />
                <controls:ChatInputBarView
                Grid.Row="2"
                Margin="{OnPlatform Android='5,9,5,20',iOS='0,11,0,10'}"
                Tapped="BtnSendSupportTapGestureRecognizer_Tapped"
                x:Name="chatSupportInput" />
            </Grid>
        </AbsoluteLayout>
    </ContentView.Content>
</ContentView>
