﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.MealPlanCell">
    <Frame
        Margin="10,10,10,10"
        CornerRadius="12"
        x:Name="FrmContainer"
        Padding="15,0, 20, -7"
        HorizontalOptions="FillAndExpand"
        BorderColor="#ffffff"
        HasShadow="False"
        Opacity="0"
        BackgroundColor="#ffffff">
        <!--<StackLayout
        Orientation="Horizontal"
        Spacing="2"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">-->
        <!--7-->
        <StackLayout
            HorizontalOptions="FillAndExpand"
            Spacing="0"
            Padding="0"
            Margin="0">
            <!--ItemsContainerHeight="{OnPlatform Android='120',iOS='100'}"-->
            <controls:ContextMenuButton
                x:Name="MenuButton"
                Margin="{OnPlatform Android='0,0,0,0',iOS='0,0,0,0'}"
                HeightRequest="0"
                ItemsContainerHeight="{OnPlatform Android='110',iOS='100'}"
                ItemsContainerWidth="200"
                WidthRequest="0"
                HorizontalOptions="EndAndExpand">

                <!--// uncomment code please-->
                <controls:ContextMenuButton.Items>
                    <x:Array Type="{x:Type MenuItem}" />
                </controls:ContextMenuButton.Items>
            </controls:ContextMenuButton>

            <!--6-->
            <StackLayout
                Spacing="0"
                Padding="0"
                Margin="0,0,0,0"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                >
                <!--5-->
                <Grid HorizontalOptions="FillAndExpand" Padding="0" Margin="0" ColumnSpacing="5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition x:Name="column1" Width=".5*" />
                        <!-- Column for the header -->
                        <ColumnDefinition x:Name="column2" Width=".5*" />
                        <!-- Column for adjustingLabel and images -->
                    </Grid.ColumnDefinitions>

                    <Label
                        Grid.Column="0"
                        TextColor="#26262B"
                        x:Name="LblHeader"
                        IsVisible="false"
                        FontAttributes="Bold"
                        FontSize="17"
                        LineHeight="{OnPlatform Android='1.1',iOS='1'}"
                        Margin="4,10,0,0" />
                    <!--4-->
                    <StackLayout
                        Grid.Column="1"
                        Orientation="Horizontal"
                        HorizontalOptions="FillAndExpand"
                        Spacing="0"
                        Margin="0,0,0,0"
                        Padding="0">

                        <Grid HorizontalOptions="FillAndExpand" x:Name="menuGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width=".5*"/>
                                <ColumnDefinition Width=".5*"/>
                            </Grid.ColumnDefinitions>
                            <Label
                                Grid.Column="0"
                                Text="(Adjusting...)"
                                IsVisible="false"
                                Margin="0,0,0,0"
                                Padding="0"
                                x:Name="adjustingLabel"
                                VerticalOptions="CenterAndExpand"
                                HorizontalOptions="CenterAndExpand"
                                HorizontalTextAlignment="Start"
                                TextColor="Black"
                                FontSize="14"
                                MaxLines="1"/>
                            <StackLayout Grid.Column="1" HorizontalOptions="FillAndExpand" Orientation="Horizontal" Spacing="1">


                                <!--Arrow icon-->
                                <Frame 
                                    BorderColor="White"
                                    HasShadow="False"
                                    x:Name="arrowImg"
                                    Padding="0"
                                    HeightRequest="40"
                                    WidthRequest="40"
                                    VerticalOptions="CenterAndExpand"
                                    HorizontalOptions="End"
                                    Margin="0,0,-4,0">
                                    <Image 
                                        Margin="0"
                                        x:Name="AdjustingImage"
                                        Aspect="AspectFit"
                                        Source="rotating_arrow.gif"
                                        HeightRequest="18"
                                        WidthRequest="18"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"/>
                                </Frame>


                                <!--Three Dot-->
                                <Grid Margin="0,0,0,0" HorizontalOptions="End">

                                    <Frame 
                                        BorderColor="Transparent"
                                        HasShadow="False"
                                        x:Name="editBubbleImg"
                                        VerticalOptions="CenterAndExpand"
                                        IsVisible="True"
                                        Padding="0"
                                        HeightRequest="40"
                                        WidthRequest="40"
                                        >
                                        <Image 
                                            Margin="0"
                                            Aspect="AspectFit"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="18"
                                            WidthRequest="18"
                                            Source="menu_blueicon"/>
                                        <Frame.GestureRecognizers>
                                            <TapGestureRecognizer Tapped="menu_tapped1" NumberOfTapsRequired="1"/>
                                        </Frame.GestureRecognizers>
                                    </Frame>

                                </Grid>

                            </StackLayout>


                        </Grid>






                        <Frame 
                            BorderColor="Transparent"
                     HasShadow="False"
                     HorizontalOptions="End"
                     x:Name="infoFrame"
                     VerticalOptions="Center"
                     HeightRequest="30"
                     Padding="15,7,16,7"
                     IsVisible="false"
                     Margin="0,0,0,0">
                            <Image
                             Source="infoicon.png"
                             Aspect="AspectFit"
                             Margin="0,0,0,0"
                             HeightRequest="20"
                             VerticalOptions="CenterAndExpand"
                             HorizontalOptions="CenterAndExpand"
                             WidthRequest="20"

                             >

                            </Image>
                            <Frame.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TipsEvent"/>
                            </Frame.GestureRecognizers>
                        </Frame>
                    </StackLayout>
                </Grid>
            </StackLayout>
            <!--<StackLayout
                Orientation="Horizontal"
                HorizontalOptions="FillAndExpand"
                Spacing="0"
                Margin="0"
                Padding="0">
                <Label
                    TextColor="#26262B"
                    x:Name="LblHeader"
                    IsVisible="false"
                    FontAttributes="Bold"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.1',iOS='1'}"
                    HorizontalOptions="FillAndExpand"
                    Margin="4,0,0,0" />
                <Label Text="(Adjusting...)"
                       IsVisible="true"
                   Margin="0,1,2,0"
                   Padding="0"
                   WidthRequest="145"
                   x:Name="adjustingLabel" VerticalOptions="Start" HorizontalOptions="EndAndExpand" HorizontalTextAlignment="End"  TextColor="Black"/>
                <Frame 
                    HasShadow="False"
                    x:Name="arrowImg"
                    Padding="5"
                HorizontalOptions="EndAndExpand"
                    VerticalOptions="Start"
                    HeightRequest="25"
                    WidthRequest="25"
                Margin="0,-7,0,0">
                    <Image Margin="0"
                       x:Name="AdjustingImage"
                        Aspect="AspectFill"
                        Source="rotating_arrow.gif"
                        HeightRequest="18"
                        WidthRequest="18"
                           
                        VerticalOptions="Center"
                           HorizontalOptions="Center"
                           />           
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Tapped="Adjusting"/>
                    </Frame.GestureRecognizers>
                </Frame>
                <Frame 
                     HasShadow="False"
                    HorizontalOptions="EndAndExpand"
                     x:Name="editBubbleImg"
                    VerticalOptions="Start"
                     IsVisible="false"
                     Padding="5,7"
                     HeightRequest="25"
                     WidthRequest="35"
                    Margin="0,-10,-5,0">
                    <Image Margin="0"
                        Aspect="AspectFit"
                        VerticalOptions="Center"
                           HorizontalOptions="Center"
                        HeightRequest="15"
                        WidthRequest="15"
                        Source="edit_solid"/>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Tapped="ChangeBubble"/>
                    </Frame.GestureRecognizers>
                </Frame>
            </StackLayout>-->

            <Label
                TextColor="#26262B"
                x:Name="LblAnswer"
                FontSize="17"
                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                HorizontalOptions="Start"
                Margin="4,0,20,0" />

            <!--<StackLayout x:Name="myStack" IsVisible="False">

            </StackLayout>-->
            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="menu_tapped"/>
            </StackLayout.GestureRecognizers>
        </StackLayout>
        <!--<AbsoluteLayout HorizontalOptions="FillAndExpand"
                        Margin="0"
                        Padding="0"
                VerticalOptions="FillAndExpand">-->

        <!--<StackLayout
                Padding="0"
                Margin="0"
                Spacing="0"
                VerticalOptions="Start"
                HorizontalOptions="FillAndExpand"
                         Orientation="Horizontal">
                -->
        <!-- Your existing StackLayout content here -->
        <!--

                
            </StackLayout>-->


        <!--</AbsoluteLayout>-->

        <!--</StackLayout>-->
        <!--<Frame.GestureRecognizers>
            <TapGestureRecognizer Tapped="menu_tapped"/>
        </Frame.GestureRecognizers>-->
    </Frame>
</ContentView>
