using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Eve;
using DrMaxMuscle.Screens.History;
using DrMaxMuscle.Screens.Me;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Screens.User;
using DrMaxMuscle.Screens.User.OnBoarding;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Controls;
using RGPopup.Maui.Services;
using System;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Layout;

public partial class RightSideMasterPage : ContentView
{
    private bool isMenuOpen = false;
    public List<ReviewsModel> reviewList = new List<ReviewsModel>();
    private bool _isTimerTooltip = false;
    private bool _isTimerSettingsUpdating = false;

    public RightSideMasterPage()
    {
        InitializeComponent();
        // uncomment code please
        //this.IsFullScreen = true;
        //this.MenuOrientations = MenuOrientation.RightToLeft;
        //this.BackgroundViewColor = Color.FromHex("#99000000");
        SlideMenuGrid.TranslationX = 250;
        SlideMenuGrid.WidthRequest = 280;
        //this.WidthRequest = 260;// DeviceDisplay.MainDisplayInfo.Density > 1 ? DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density : DeviceDisplay.MainDisplayInfo.Width;

        // You must set BackgroundColor,
        // and you cannot put another layout with background color cover the whole View
        // otherwise, it cannot be dragged on Android
        //this.BackgroundColor = Colors.White;
        //this.Opacity = 1.0;
        // This is shadow view color, you can set a transparent color

        try
        {
            var _IDrMuscleSubcription = (IDrMuscleSubcription)MauiProgram.ServiceProvider.GetService(typeof(IDrMuscleSubcription));
            if (_IDrMuscleSubcription != null && VersionInfoLabel != null) // CHANGE: Added null check for VersionInfoLabel
            {
                VersionInfoLabel.Text = _IDrMuscleSubcription.GetBuildVersion().Replace("Version", AppResources.Version).Replace("Build", AppResources.Build);
            }
            else
            {
                Console.WriteLine("_IDrMuscleSubcription or VersionInfoLabel is null"); // CHANGE: Log null case
            }
            reviewList = GetReviews();

            if (ShortOnTimeButton != null)
            {
                ShortOnTimeButton.Clicked += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    MessagingCenter.Send<HomeOptionsMessage>(new HomeOptionsMessage() { Options = ShortOnTimeButton.Text }, "HomeOptionsMessage");
                };
            }
            if (TiredTodayButton != null)
            {
                TiredTodayButton.Clicked += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    MessagingCenter.Send<HomeOptionsMessage>(new HomeOptionsMessage() { Options = TiredTodayButton.Text }, "HomeOptionsMessage");
                };
            }
            if (MoreStatsButton != null)
            {
                MoreStatsButton.Clicked += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        MeCombinePage meCombinePage = new MeCombinePage();
                        meCombinePage.OnBeforeShow();
                        await Navigation.PushAsync(meCombinePage);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (SharefreeMonthButton != null)
            {
                SharefreeMonthButton.Clicked += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    /*
                    var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
                    if (Device.RuntimePlatform.Equals(Device.Android))
                    {

                        await Share.RequestAsync(new ShareTextRequest
                        {
                            Uri = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=home&utm_content={firstname}",
                            Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                        });
                    }
                    else
                        await Xamarin.Essentials.Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=home&utm_content={firstname}");
                    */
                    try
                    {
                        await HelperClass.ShareApp(firebaseEventName: "shared_home_page");
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (MeGesture != null)
            {
                MeGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        SettingsPage page = new SettingsPage();
                        await Navigation.PushAsync(page);
                        //await PagesFactory.PushAsync<SettingsPage>();
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (ChartsGesture != null)
            {
                ChartsGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        MeCombinePage meCombinePage = new MeCombinePage();
                        meCombinePage.OnBeforeShow();
                        await Navigation.PushAsync(meCombinePage);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (SettingGesture != null)
            {
                SettingGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        SettingsPage page = new SettingsPage();
                        await Navigation.PushAsync(page);
                        //await PagesFactory.PushAsync<SettingsPage>();
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }

            if (HistoryGesture != null)
            {
                HistoryGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        CurrentLog.Instance.PastWorkoutDate = null;
                        HistoryPage historyPage = new HistoryPage();
                        historyPage.OnBeforeShow();
                        await Navigation.PushAsync(historyPage);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }

            if (SubscriptionGesture != null)
            {
                SubscriptionGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        SubscriptionPage page = new SubscriptionPage();
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);
                        // await PagesFactory.PushAsync<SubscriptionPage>();
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }

            if (WebGestures != null)
            {
                WebGestures.Tapped += (sender, e) =>
                {
                    Browser.OpenAsync("https://my.dr-muscle.com", BrowserLaunchMode.SystemPreferred);
                };
            }

            if (TellAFriendGesture != null)
            {
                TellAFriendGesture.Tapped += async (sender, e) =>
                {
                    /*
                    var firstname = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
                    if (Device.RuntimePlatform.Equals(Device.Android))
                    {

                        Xamarin.Essentials.Share.RequestAsync(new Xamarin.Essentials.ShareTextRequest
                        {
                            Uri = $"https://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=sidebar&utm_content={firstname}",
                            Subject = $"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence"
                        });
                    }
                    else
                        Xamarin.Essentials.Share.RequestAsync($"{firstname} is inviting you to try Dr.Muscle, the app that gets you in shape faster using artificial intelligence \nhttps://dr-muscle.com/discount/?utm_source=app&utm_medium=share&utm_campaign=sidebar&utm_content={firstname}");
                    */
                    try
                    {
                        await HelperClass.ShareApp(firebaseEventName: "told_a_friend");
                    }
                    catch (Exception ex)
                    {

                    }
                    //if (Device.RuntimePlatform.Equals(Device.Android))
                    //Xamarin.Essentials.Share.RequestAsync("Check out this new app! For your fitness. \n\n\"Dr.Muscle gets you in shape fast like a personal trainer\" \nhttps://play.google.com/store/apps/details?id=com.drmaxmuscle.dr_max_muscle&hl=en");
                    //else
                    //    Xamarin.Essentials.Share.RequestAsync("Check out this new app! For your fitness. \n\n\"Dr.Muscle gets you in shape fast like a personal trainer\" \nhttps://itunes.apple.com/app/dr-muscle/id1073943857?mt=8");
                };
            }
            //EmailUsButton.Clicked += (object sender, EventArgs e) =>
            //{
            //    HideWithoutAnimations();
            //    await HelperClass.SendMail("");
            //};

            if (LogoutGesture != null)
            {
                LogoutGesture.Tapped += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    RemoveToken();
                    CancelNotification();
                    LocalDBManager.Instance.Reset();
                    CurrentLog.Instance.Reset();
                    App.IsV1User = false;
                    App.IsV1UserTrial = false;
                    App.IsFreePlan = false;
                    App.IsCongratulated = false;
                    App.IsSupersetPopup = false;
                    ((App)Application.Current).UserWorkoutContexts.workouts = new GetUserWorkoutLogAverageResponse();
                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                    ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
                    ((App)Application.Current).WorkoutHistoryContextList.SaveContexts();
                    ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
                    ((App)Application.Current).WorkoutLogContext.SaveContexts();
                    try
                    {
                        if (((global::DrMaxMuscle.MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage.Navigation.NavigationStack[0] is LearnPage)
                            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).SelectedItem = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[0];
                    }
                    catch (Exception ex)
                    {

                    }
                    //await Navigation.PopToRootAsync();
                    ((App)Application.Current).displayCreateNewAccount = true;
                    WelcomePage welcome = new WelcomePage();
                    welcome.OnBeforeShow();
                    await Application.Current.MainPage.Navigation.PushAsync(welcome);
                    //Application.Current.MainPage = new NavigationPage(welcome);

                };
            }

            if (SignInButton != null)
            {
                SignInButton.Clicked += async (object sender, EventArgs e) =>
                {
                    try
                    {
                        ((App)Application.Current).displayCreateNewAccount = true;
                        MainOnboardingPage.IsMovedToLogin = true;
                        HideWithoutAnimations();
                        LocalDBManager.Instance.Reset();
                        WelcomePage page = new WelcomePage();
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }

            if (SkipDemoButton != null)
            {
                SkipDemoButton.Clicked += async (object sender, EventArgs e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        CurrentLog.Instance.EndExerciseActivityPage = GetType();

                        var modalPage1 = new Views.WelcomeAIOverlay();
                        Navigation.PushAsync(modalPage1);
                        modalPage1.SetDetails("", CurrentLog.Instance.AiDescription);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            //OldHomeButton.Clicked += async (object sender, EventArgs e) =>
            //{
            //    HideWithoutAnimations();
            //    await PagesFactory.PushAsync<MainPage>();
            //};
            if (FAQGesture != null)
            {
                FAQGesture.Tapped += async (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        await Navigation.PushAsync(new FAQPage());
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (CancelButton != null)
            {
                CancelButton.Clicked += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    if (Navigation?.NavigationStack?.Count > 1)
                    {
                        try
                        {
                            await Navigation?.PopAsync();
                        }
                        catch (NotSupportedException ex)
                        {
                        }
                    }
                    //await PagesFactory.PopAsync(true);
                };
            }
            if (LanguageButton != null)
            {
                LanguageButton.Clicked += (sender, e) =>
                {
                    HideWithoutAnimations();
                    Device.BeginInvokeOnMainThread(() =>
                    {
                        // uncomment code please
                        //var p = new LanguagesPage();
                        //p.OnBeforeShow();
                        //Navigation.PushModalAsync(p);
                    });
                };
            }
            if (RestartSetupButton != null)
            {
                RestartSetupButton.Clicked += (sender, e) =>
                {
                    try
                    {
                        HideWithoutAnimations();
                        App.IsDemoProgress = false;
                        CurrentLog.Instance.IsDemoRunningStep2 = false;
                        //Navigation.PopToRootAsync();
                        MainOnboardingPage mainOnboardingPage = new MainOnboardingPage();
                        mainOnboardingPage.OnBeforeShow();
                        Application.Current.MainPage.Navigation.PushAsync(mainOnboardingPage);
                        //Application.Current.MainPage = new NavigationPage(mainOnboardingPage);
                        //PagesFactory.PopToPage<MainOnboardingPage>(true);
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (RestartDemoButton != null)
            {
                RestartDemoButton.Clicked += async (sender, e) =>
                {
                    HideWithoutAnimations();
                    App.IsDemoProgress = false;
                    App.IsDemo1Progress = false;
                    CurrentLog.Instance.IsDemoRunningStep2 = false;
                    CurrentLog.Instance.IsDemoRunningStep1 = true;
                    CurrentLog.Instance.IsDemoPopingOut = true;
                    CurrentLog.Instance.IsRestarted = true;
                    CurrentLog.Instance.CurrentExercise = new ExerciceModel()
                    {
                        BodyPartId = 7,
                        VideoUrl = "https://youtu.be/Plh1CyiPE_Y",
                        IsBodyweight = true,
                        IsEasy = false,
                        IsFinished = false,
                        IsMedium = false,
                        IsNextExercise = false,
                        IsNormalSets = false,
                        IsSwapTarget = false,
                        IsSystemExercise = true,
                        IsTimeBased = false,
                        IsUnilateral = false,
                        Label = "Crunch",
                        RepsMaxValue = null,
                        RepsMinValue = null,
                        Timer = null,
                        Id = 864
                    };
                    App.IsDemoProgress = true;
                    LocalDBManager.Instance.SetDBSetting("DemoProgress", "true");
                    CurrentLog.Instance.Exercise1RM.Clear();
                    // uncomment code please
                    //await PagesFactory.PopToPage<NewDemoPage>();
                    CurrentLog.Instance.IsDemoPopingOut = false;
                };
            }
            if (LocalDBManager.Instance.GetDBSetting("timer_vibrate") == null)
                LocalDBManager.Instance.SetDBSetting("timer_vibrate", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_sound") == null)
                LocalDBManager.Instance.SetDBSetting("timer_sound", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_reps_sound") == null)
            {
                LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "true");
                LocalDBManager.Instance.SetDBSetting("timer_sound", "false");
            }

            if (LocalDBManager.Instance.GetDBSetting("timer_123_sound") == null)
                LocalDBManager.Instance.SetDBSetting("timer_123_sound", "true");



            if (LocalDBManager.Instance.GetDBSetting("timer_autostart") == null)
                LocalDBManager.Instance.SetDBSetting("timer_autostart", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_autoset") == null)
                LocalDBManager.Instance.SetDBSetting("timer_autoset", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
                LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_betweenexercises") == null)
                LocalDBManager.Instance.SetDBSetting("timer_betweenexercises", "true");

            if (LocalDBManager.Instance.GetDBSetting("timer_remaining") == null)
                LocalDBManager.Instance.SetDBSetting("timer_remaining", "60");

            if (LocalDBManager.Instance.GetDBSetting("reprange") == null)
                LocalDBManager.Instance.SetDBSetting("reprange", "BuildMuscle");


            TimerEntry.Text = LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value;
            _isTimerSettingsUpdating = true;
            VibrateSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_vibrate")?.Value);
            SoundSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_sound")?.Value);
            RepsSoundSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_reps_sound")?.Value);
            Timer123Switch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_123_sound")?.Value);
            if (SoundSwitch.IsToggled && RepsSoundSwitch.IsToggled)
            {
                SoundSwitch.IsToggled = false;
                LocalDBManager.Instance.SetDBSetting("timer_sound", "false");
            }
            AutostartSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_autostart")?.Value);
            AutosetSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_autoset")?.Value);
            FullscreenSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_fullscreen")?.Value);
            BetweenExercisesSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_betweenexercises")?.Value);
            _isTimerSettingsUpdating = false;
            if (TimerEntry != null)
                TimerEntry.Text = LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value;
            if (LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value != null)
            {
                try
                {
                    App.globalTime = int.Parse(LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value);

                }
                catch (Exception ex)
                {
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", "60");
                    App.globalTime = 60;
                    if (TimerEntry != null)
                        TimerEntry.Text = "60";
                }
            }

            if (TimerStartButton != null)
            {
                TimerStartButton.Clicked += async (sender, e) =>
                {
                    if (TimerEntry != null)
                        if (TimerEntry.Text.Length == 0)
                            return;
                    // Debug.WriteLine(Timer.Instance.Remaining.ToString());

                    //
                    try
                    {
                        string value = LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value;
                        if (!string.IsNullOrEmpty(value))
                        {
                            Timer.Instance.Remaining = int.Parse(value, NumberStyles.Integer, CultureInfo.InvariantCulture);
                        }
                        //Timer.Instance.Remaining = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value);
                        if (Timer.Instance.Remaining == 0)
                            return;

                        // uncomment code please
                        //if (((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage is DrMusclePage)
                        //    ((DrMusclePage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).HideTimerIcon();
                        //else
                        //{
                        //    var navigation = (((MainTabbedPage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).CurrentPage.Navigation);
                        //    ((DrMusclePage)navigation.NavigationStack[navigation.NavigationStack.Count - 1]).HideTimerIcon();
                        //    //((DrMusclePage)((MainTabbedPage)((NoAnimationNavigationPage)Application.Current.MainPage).CurrentPage).CurrentPage.Navigation.NavigationStack[0]).HideTimerIcon();
                        //}

                    }
                    catch (Exception ex)
                    {

                }
                if (Timer.Instance.State != "RUNNING")
                {
                    Timer.Instance.StartTimer();
                    HideWithoutAnimations();
                    Timer.Instance.IsWorkTimer = false;
                    TimerStartButton.Text = "STOP";
                    // uncomment code please
                    //if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen").Value == "true")
                    //    await PagesFactory.PushAsync<TimerOverlay>(true);

                    }
                    else
                    {
                        HideWithoutAnimations();
                        TimerStartButton.Text = "START";
                        await Timer.Instance.StopTimer();
                    }

                };

            }
            if (TimerLess != null)
            {
                TimerLess.Clicked += (sender, e) =>
                {
                    try
                    {
                        if (TimerEntry.Text.Length == 0)
                            return;
                        TimerLess.Unfocus();
                        string value = TimerEntry.Text;
                        int current = int.Parse(value, NumberStyles.Integer, CultureInfo.InvariantCulture);
                        //int current = Convert.ToInt32(TimerEntry.Text);
                        if (current >= 5)
                            current = current - 5;
                        TimerEntry.Text = current.ToString();
                        Timer.Instance.Remaining = current;
                        UpdateTimeCountWithoutLoader(current);
                        App.globalTime = current;

                        if (_isTimerTooltip)
                        {
                            _isTimerTooltip = false;
                            // uncomment code please
                            //TooltipEffect.SetPosition(TimerStartButton, TooltipPosition.Bottom);
                            //TooltipEffect.SetBackgroundColor(TimerStartButton, AppThemeConstants.BlueColor);
                            //TooltipEffect.SetTextColor(TimerStartButton, Color.White);
                            //TooltipEffect.SetText(TimerStartButton, $"Start timer");
                            //TooltipEffect.SetHasTooltip(TimerStartButton, true);
                            //TooltipEffect.SetHasShowTooltip(TimerStartButton, true);
                        }

                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (TimerMore != null)
            {
                TimerMore.Clicked += (sender, e) =>
                {
                    try
                    {
                        if (TimerEntry.Text.Length == 0)
                            return;
                        TimerLess.Unfocus();
                        string value = TimerEntry.Text;
                        int current = int.Parse(value, NumberStyles.Integer, CultureInfo.InvariantCulture);
                        //int current = Convert.ToInt32(TimerEntry.Text);
                        current = current + 5;
                        TimerEntry.Text = current.ToString();
                        UpdateTimeCountWithoutLoader(current);
                        Timer.Instance.Remaining = current;
                        App.globalTime = current;

                        if (_isTimerTooltip)
                        {
                            // uncomment code please
                            //TooltipEffect.SetPosition(TimerLess, TooltipPosition.Bottom);
                            //TooltipEffect.SetBackgroundColor(TimerLess, AppThemeConstants.BlueColor);
                            //TooltipEffect.SetTextColor(TimerLess, Color.White);
                            //TooltipEffect.SetText(TimerLess, $"Remove seconds");
                            //TooltipEffect.SetHasTooltip(TimerLess, true);
                            //TooltipEffect.SetHasShowTooltip(TimerLess, true);
                        }
                    }
                    catch (Exception ex)
                    {

                    }
                };
            }
            if (TimerEntry != null)
            {
                TimerEntry.TextChanged += (sender, e) =>
                {
                    try
                    {
                        if (TimerEntry.Text.Length == 0)
                        {
                            return;
                        }
                        const string textRegex = @"^\d+(?:)?$";
                        bool IsValid = Regex.IsMatch(TimerEntry.Text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
                        if (IsValid == false)
                            TimerEntry.Text = e.OldTextValue;
                        if (TimerEntry.Text.Length == 0)
                        {
                            return;
                        }

                        if (",.".Contains(TimerEntry.Text[TimerEntry.Text.Length - 1].ToString()))
                            TimerEntry.Text = TimerEntry.Text.Substring(0, TimerEntry.Text.Length - 1);
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", TimerEntry.Text);
                        UpdateTimeCountWithoutLoader(int.Parse(TimerEntry.Text));
                        string value = TimerEntry.Text;
                        App.globalTime = int.Parse(value, NumberStyles.Integer, CultureInfo.InvariantCulture);
                        //App.globalTime = Convert.ToInt32(TimerEntry.Text);

                    }
                    catch (Exception ex)
                    {
                        LocalDBManager.Instance.SetDBSetting("timer_remaining", TimerEntry.Text);
                    }
                };
            }
            if (VibrateSwitch != null)
            {
                VibrateSwitch.Toggled += (sender, e) =>
                {
                    if (VibrateSwitch.IsToggled)
                        LocalDBManager.Instance.SetDBSetting("timer_vibrate", "true");
                    else
                        LocalDBManager.Instance.SetDBSetting("timer_vibrate", "false");
                    UpdateTimerSettings();

                };
            }
            if (SoundSwitch != null)
            {
                SoundSwitch.Toggled += (sender, e) =>
                {
                    if (!_isTimerSettingsUpdating)
                    {
                        if (SoundSwitch.IsToggled)
                        {
                            RepsSoundSwitch.IsToggled = false;
                            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "false");
                            LocalDBManager.Instance.SetDBSetting("timer_sound", "true");
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_sound", "false");
                        }
                        UpdateTimerSettings();
                    }

                };
            }
            if (RepsSoundSwitch != null)
            {
                RepsSoundSwitch.Toggled += (sender, e) =>
                {
                    if (!_isTimerSettingsUpdating)
                    {
                        if (RepsSoundSwitch.IsToggled)
                        {
                            SoundSwitch.IsToggled = false;
                            LocalDBManager.Instance.SetDBSetting("timer_sound", "false");
                            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "true");
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "false");
                        }
                        UpdateTimerSettings();
                    }
                };
            }
            //
            if (Timer123Switch != null)
            {
                Timer123Switch.Toggled += (sender, e) =>
                {
                    if (!_isTimerSettingsUpdating)
                    {
                        if (Timer123Switch.IsToggled)
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_123_sound", "true");
                        }
                        else
                        {
                            LocalDBManager.Instance.SetDBSetting("timer_123_sound", "false");
                        }
                        UpdateTimerSettings();
                    }
                };
            }
            if (AutostartSwitch != null)
            {
                AutostartSwitch.Toggled += (sender, e) =>
                {
                    if (AutostartSwitch.IsToggled)
                        LocalDBManager.Instance.SetDBSetting("timer_autostart", "true");
                    else
                        LocalDBManager.Instance.SetDBSetting("timer_autostart", "false");
                    UpdateTimerSettings();

                };
            }
            if (AutosetSwitch != null)
            {
                AutosetSwitch.Toggled += (sender, e) =>
                {
                    if (AutosetSwitch.IsToggled)
                        LocalDBManager.Instance.SetDBSetting("timer_autoset", "true");
                    else
                        LocalDBManager.Instance.SetDBSetting("timer_autoset", "false");
                    UpdateTimerSettings();
                    if (_isTimerTooltip)
                    {
                        // uncomment code please
                        //TooltipEffect.SetPosition(TimerMore, TooltipPosition.Bottom);
                        //TooltipEffect.SetBackgroundColor(TimerMore, AppThemeConstants.BlueColor);
                        //TooltipEffect.SetTextColor(TimerMore, Color.White);
                        //TooltipEffect.SetText(TimerMore, $"Add seconds");
                        //TooltipEffect.SetHasTooltip(TimerMore, true);
                        //TooltipEffect.SetHasShowTooltip(TimerMore, true);
                    }
                };
            }
            if (BetweenExercisesSwitch != null)
            {
                BetweenExercisesSwitch.Toggled += (sender, e) =>
                {
                    if (!_isTimerSettingsUpdating) // Only update if not already updating from elsewhere
                    {
                        // Save the toggle state to the local database
                        string valueToSave = BetweenExercisesSwitch.IsToggled ? "true" : "false";
                        LocalDBManager.Instance.SetDBSetting("timer_betweenexercises", valueToSave);

                        // Update the timer settings on the server
                        UpdateTimerSettings();
                    }
                };
            }

            if (FullscreenSwitch != null)
            {
                FullscreenSwitch.Toggled += (sender, e) =>
                {
                    if (FullscreenSwitch.IsToggled)
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");
                    else
                        LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "false");
                    UpdateTimerSettings();
                };
            }
            Timer.Instance.OnTimerDone += () => { TimerStartButton.Text = "START"; };
            Timer.Instance.OnTimerStop += () => { TimerStartButton.Text = "START"; };

            var tapLinkGestureRecognizer = new TapGestureRecognizer();
            tapLinkGestureRecognizer.Tapped += (s, e) =>
            {
                Browser.OpenAsync("http://drmuscleapp.com/news/between-sets/", BrowserLaunchMode.SystemPreferred);
            };

            if(LearnMoreLink != null)
                LearnMoreLink.GestureRecognizers.Add(tapLinkGestureRecognizer);
            RefreshLocalized();
            MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
            {
                RefreshLocalized();
            });

        }
        catch (Exception ex)
        {

        }
    }

    private void CancelNotification()
    {
        try
        {
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1251);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1351);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1451);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1551);
            DependencyService.Get<IAlarmAndNotificationService>().CancelNotification(1651);
        }
        catch (Exception ex)
        {

        }
    }

    private void RefreshLocalized()
    {
        //MeButton.Text = "More";
        try
        {
            ChartsButton.Text = "Charts";
            HistoryButton.Text = AppResources.History;
            SubscriptionInfosButton.Text = "Subscription"; //AppResources.SubscriptionInfo;
                                                           //SettingsButton.Text = AppResources.Settings;
                                                           //EmailUsButton.Text = AppResources.EmailSupport;
            LogOutButton.Text = AppResources.LogOut;
            LblVibrate.Text = AppResources.VIBRATE;
            //LblSound.Text = AppResources.SOUND;
            LblAutoStart.Text = AppResources.AUTOSTART;
            LblAutomatchReps.Text = AppResources.AUTOMATCHREPS;
            TimerStartButton.Text = AppResources.START;
            LearnMoreLink.Text = AppResources.LearnMore;
            //ManageExercise.Text = AppResources.ManageExercises;
            LblAutomaticallyChangeTimer.Text = AppResources.AutomaticallyChangeTimerDurationToMatchRecommendedRepsAndOptimizeMuscleHypertrophy;

            var data = (IDrMuscleSubcription)MauiProgram.ServiceProvider.GetService(typeof(IDrMuscleSubcription));
            VersionInfoLabel.Text = data.GetBuildVersion().Replace("Version", AppResources.Version).Replace("Build", AppResources.Build);
            //VersionInfoLabel.Text = DependencyService.Get<IDrMuscleSubcription>().GetBuildVersion().Replace("Version", AppResources.Version).Replace("Build", AppResources.Build);
            VersionInfoLabel1.Text = VersionInfoLabel.Text;
            VersionInfoLabel2.Text = VersionInfoLabel.Text;
            LblFullScreen.Text = AppResources.FullscreenUppercase;
            //WebButton.Text = AppResources.WebApp;
            //OldHomeButton.Text = "Old Home";
            FAQButton.Text = "Help";
        }
        catch (Exception ex)
        {

        }
    }

    private List<ReviewsModel> GetReviews()
    {
        List<ReviewsModel> reviews = new List<ReviewsModel>();
        reviews.Add(new ReviewsModel()
        {
            Review = "For basic strength training this app out performs the many methods/apps I have tried in my 30+ years of body/strength training. What I like the most is that it take the brain work out of weights, reps, and sets (if you follow a structured workout). What I like even more is the exceptional customer engagement.",
            ReviewerName = "TijFamily916"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Let me just say, I was thinking of being an online personal trainer but after using and seeing the power of this app, I sincerely can't charge people the rates I had in mind when this app does it at a fraction of the cost. The man behind it, Dr. Juneau is the real deal too.",
            ReviewerName = "Rajib Ghosh"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "love seeing my progress on my 1 RM while varying my weight and rep count. Also feel like I am getting more results in a shorter time utilizing the rest pause method. Loving the workouts and the feedback from the app",
            ReviewerName = "Randall Duke"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Maximizing the time in the gym takes preparation. This app eliminates that and does a better job then I did with hours of preparation. I've seen amazing gains with less work.",
            ReviewerName = "Raymond Backers"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Great alternative to an actual human personal trainer if your schedule is always dynamic. The charts and graphs and many various options are outstanding.",
            ReviewerName = "Daniel Quick"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "Dr Carl has used science and experience to create an app that will continually push you to the limits. I've been using this app for about a month now, and am moving weight that I didn't think was possible in this short amount of time. I've been lifting for years, but this app would be just as affective for a beginner. One of the best things about it, is Dr Carl listens to the users and their feedback, and is constantly making improvements.",
            ReviewerName = "DeeBee78"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This app is absolutely amazing. I have been in and out of the gym for a few years with some light progress every time and modest gains, however, the implementation of this app helped me gain 10 lbs and become significantly more defined in the first 6 weeks. Very easy to use, and the customer service is incredible. This app is really great for anyone from beginners to experts.",
            ReviewerName = "Potero2122"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "When I first trialed the app, I wasn't sure I'd like it, but after having stuck with it for a couple of months, I'm sold. The AI is great and makes it very easy for me to know how many reps to do and how much weight to lift. No more guessing. He brings all the science of lifting to this app, and I'd been lifting regularly for two years. This really is something different than any other app out there.",
            ReviewerName = "MKJ&MKJ"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "This is a very good app to invest in. It's already a good design and has great workouts that will help you continually build muscle and break through plateaus, but they are constantly working to improve it based on customer feedback. The most important thing about this app is the customer service. Christelle and Carl are always available to assist you in anyway they can in a very timely manner, most of the time within an hour of submitting your question or issue. I would recommend this app to everyone serious about building muscle.",
            ReviewerName = "David Fechter"
        });
        reviews.Add(new ReviewsModel()
        {
            Review = "I have been using Dr. Muscle for two years now and this app gives me confidence and provides structure to my workouts. I love that the app adapts to you and is quite \"forgiving\" when you do fail while encouraging you to push harder each time. It has really demystified all the elements of training for hypertrophy so I can get straight to lifting after a hard day at work without having to think about everything! I look forward to the analysis of my \"performance\" after every exercise and love to see those green check marks indicating progress. I have recently subscribed to \"Eve\" the dietary equivalent to this app and while it's in its early stages of development I'm looking forward to similarly great things.",
            ReviewerName = "Remone Mundle"
        });

        return reviews;
    }




    private async void UpdateTimeCountWithoutLoader(int timeCount)
    {
        //SetUserTimeCount
        try
        {
            LocalDBManager.Instance.SetDBSetting("timer_count", timeCount.ToString());
            await DrMuscleRestClient.Instance.SetUserTimeCountWithoutLoader(new UserInfosModel()
            {
                TimeCount = timeCount
            });
        }
        catch (Exception ex)
        {

        }
    }
    private async void UpdateTimeCount(int timeCount)
    {
        //SetUserTimeCount
        LocalDBManager.Instance.SetDBSetting("timer_count", timeCount.ToString());
        await DrMuscleRestClient.Instance.SetUserTimeCount(new UserInfosModel()
        {
            TimeCount = timeCount
        });
    }
    private async void UpdateMassUnit(string unit)
    {
        await DrMuscleRestClient.Instance.SetUserMassUnit(new UserInfosModel()
        {
            MassUnit = unit
        });
    }

    private async void UpdateTimerSettings()
    {
        try
        {
            await DrMuscleRestClient.Instance.SetUserTimerOptionsV3(new UserInfosModel()
            {
                IsVibrate = VibrateSwitch.IsToggled,
                IsAutomatchReps = AutosetSwitch.IsToggled,
                IsSound = SoundSwitch.IsToggled,
                IsFullscreen = FullscreenSwitch.IsToggled,
                IsBetweenExercises = BetweenExercisesSwitch?.IsToggled, // Added null check with ?. operator
                IsAutoStart = AutostartSwitch.IsToggled,
                IsRepsSound = RepsSoundSwitch.IsToggled,
                IsTimer321 = Timer123Switch.IsToggled
            });
        }
        catch (Exception ex)
        {

        }
    }


    public void ResignedField()
    {
        Device.BeginInvokeOnMainThread(() =>
        {
            if (TimerEntry != null)
                TimerEntry.Unfocus();
        });
    }

    public async void ShowGeneral()
    {
        //this.WidthRequest = 250;
        var rndm = new Random();
        var review = reviewList.ElementAt(rndm.Next(0, 9));
        LblReview.Text = review.Review;
        LblReviewerName.Text = review.ReviewerName;
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();

        Device.BeginInvokeOnMainThread(async () =>
        {
            GeneralStack.IsVisible = true;
            TimerStack.IsVisible = false;
            BotStack.IsVisible = false;
            HomeStack.IsVisible = false;

        });
        try
        {

            if (!string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("ProfilePic")?.Value))
                ImgProfile.Source = LocalDBManager.Instance.GetDBSetting("ProfilePic")?.Value;
            else
                ImgProfile.Source = "me_tab.png";
        }
        catch (Exception ex)
        {

        }
        try
        {
            LblDoneWorkout.Text = "";
            LblNmae.Text = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            var workouts = ((App)Application.Current).UserWorkoutContexts.workouts;
            if (workouts != null)
            {
                if (workouts.Sets != null)
                {
                    if (workouts.Averages.Count > 1)
                    {
                        OneRMAverage last = workouts.Averages.OrderBy(a => a.Date).ToList()[workouts.Averages.Count - 1];
                        OneRMAverage before = workouts.Averages.OrderBy(a => a.Date).ToList()[workouts.Averages.Count - 2];
                        decimal progresskg = (last.Average.Kg - before.Average.Kg) * 100 / last.Average.Kg;
                        bool inKg = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg";
                        var exerciseModel = workouts.HistoryExerciseModel;
                        if (exerciseModel != null)
                        {
                            var unit = inKg ? AppResources.Kg.ToLower() : AppResources.Lbs.ToLower();
                            var weightLifted = inKg ? exerciseModel.TotalWeight.Kg : exerciseModel.TotalWeight.Lb;
                            LblDoneWorkout.Text = exerciseModel.TotalWorkoutCompleted <= 1 ? $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutDone}" : $"{exerciseModel.TotalWorkoutCompleted} {AppResources.WorkoutsDone}";

                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {

        }
        try
        {
            if (CurrentLog.Instance.IsMonthlyUser == null)
            {
                var result = await DrMuscleRestClient.Instance.IsMonthlyUser(true);
                if (result != null)
                    CurrentLog.Instance.IsMonthlyUser = result.Result;
            }
        }
        catch (Exception ex)
        {

        }
    }

    public void ShowFeaturedMenu()
    {
        GeneralStack.IsVisible = BotStack.IsVisible = HomeStack.IsVisible = TimerStack.IsVisible = false;
        FeaturedStack.IsVisible = true;
    }
    public void ShowMealPlanMenu()
    {
        GeneralStack.IsVisible = BotStack.IsVisible = HomeStack.IsVisible = TimerStack.IsVisible = false;
        TimerStack.IsVisible = false;
        FeaturedStack.IsVisible = false;
        MealPlanStack.IsVisible = true;
    }

    public void ShowHomeMenu()
    {
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        GeneralStack.IsVisible = false;
        BotStack.IsVisible = false;
        HomeStack.IsVisible = true;
        TimerStack.IsVisible = false;
        HomeMainStack.IsVisible = true;
        SummaryMainStack.IsVisible = false;
    }

    public void ShowHomeSummaryMenu()
    {
        this.WidthRequest = 250;
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        GeneralStack.IsVisible = false;
        BotStack.IsVisible = false;
        HomeStack.IsVisible = true;
        TimerStack.IsVisible = false;
        HomeMainStack.IsVisible = false;
        SummaryMainStack.IsVisible = true;


    }

    public void ShowAutoBotMenu()
    {
        this.WidthRequest = 250;
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        GeneralStack.IsVisible = false;
        RestartSetupButton.IsVisible = true;
        SignInButton.IsVisible = true;
        RestartDemoButton.IsVisible = false;
        BoxDemoBorder.IsVisible = false;
        SkipDemoButton.IsVisible = false;
        BoxSetupBorder.IsVisible = true;
        HomeStack.IsVisible = false;
        BotStack.IsVisible = true;
        TimerStack.IsVisible = false;

        LanguageButton.IsVisible = true;
        BoxLanguageBorder.IsVisible = true;
        CancelButton.IsVisible = false;

        string val = LocalDBManager.Instance.GetDBSetting("BetaVersion")?.Value;
        if (val == "Beta")
        {
            ModeLbl.Text = "Beta experience - load normal";
        }
        else
        {
            ModeLbl.Text = "Normal experience - load beta";
        }
    }

    public void ShowAutoBotReconfigureMenu()
    {
        this.WidthRequest = 250;
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        GeneralStack.IsVisible = false;
        RestartSetupButton.IsVisible = false;
        SignInButton.IsVisible = false;
        RestartDemoButton.IsVisible = false;
        BoxDemoBorder.IsVisible = false;
        SkipDemoButton.IsVisible = false;
        BoxSetupBorder.IsVisible = false;
        HomeStack.IsVisible = false;
        BotStack.IsVisible = true;
        TimerStack.IsVisible = false;

        LanguageButton.IsVisible = false;
        BoxLanguageBorder.IsVisible = false;
        CancelButton.IsVisible = true;
    }

    public void ShowAutoBotDemoMenu()
    {
        this.WidthRequest = 250;
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        GeneralStack.IsVisible = false;
        SkipDemoButton.IsVisible = true;
        SignInButton.IsVisible = false;
        RestartSetupButton.IsVisible = false;
        BoxSetupBorder.IsVisible = false;
        RestartDemoButton.IsVisible = true;
        BoxDemoBorder.IsVisible = true;
        BotStack.IsVisible = true;
        HomeStack.IsVisible = false;
        BotStack.VerticalOptions = LayoutOptions.FillAndExpand;
        TimerStack.IsVisible = false;
        LanguageButton.IsVisible = true;
        BoxLanguageBorder.IsVisible = true;
        CancelButton.IsVisible = false;
    }

    public void SetFeatuedTimer()
    {
        if (TimerEntry.IsFocused)
            TimerEntry.Unfocus();
        TimerEntry.Text = LocalDBManager.Instance.GetDBSetting("timer_remaining")?.Value;
        AutosetSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_autoset")?.Value);
    }
    public async void ShowTimer()
    {
        this.WidthRequest = 250;
        Device.BeginInvokeOnMainThread(async () =>
        {
            IsVisible = true;
            GeneralStack.IsVisible = false;
            BotStack.IsVisible = false;
            HomeStack.IsVisible = false;
            SetTimerSettings();
            TimerStack.IsVisible = true;
            if (CurrentLog.Instance.ShowTimerOptions)
            {
                await Task.Delay(500);

                CurrentLog.Instance.ShowTimerOptions = false;
                if (Device.RuntimePlatform == Device.Android)
                {
                    _isTimerTooltip = true;
                    // uncomment code please
                    //TooltipEffect.SetPosition(StackAutoMatch, TooltipPosition.Bottom);
                    //TooltipEffect.SetBackgroundColor(StackAutoMatch, AppThemeConstants.BlueColor);
                    //TooltipEffect.SetTextColor(StackAutoMatch, Color.White);
                    //TooltipEffect.SetText(StackAutoMatch, $"Toggle off to customize");
                    //TooltipEffect.SetHasTooltip(StackAutoMatch, true);
                    //TooltipEffect.SetHasShowTooltip(StackAutoMatch, true);
                }
                else
                {
                    // UserDialogs.Instance.Alert("Toggle off to customize");
                }
            }
            if (Timer.Instance.State == "RUNNING")
            {
                TimerStartButton.Text = "STOP";
            }
        });
    }

    void SetTimerSettings()
    {

        if (LocalDBManager.Instance.GetDBSetting("timer_vibrate") == null)
            LocalDBManager.Instance.SetDBSetting("timer_vibrate", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_sound") == null)
            LocalDBManager.Instance.SetDBSetting("timer_sound", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_reps_sound") == null)
            LocalDBManager.Instance.SetDBSetting("timer_reps_sound", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_autostart") == null)
            LocalDBManager.Instance.SetDBSetting("timer_autostart", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_autoset") == null)
            LocalDBManager.Instance.SetDBSetting("timer_autoset", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_fullscreen") == null)
            LocalDBManager.Instance.SetDBSetting("timer_fullscreen", "true");

        if (LocalDBManager.Instance.GetDBSetting("timer_betweenexercises") == null)
            LocalDBManager.Instance.SetDBSetting("timer_betweenexercises", "true");


        try
        {
            _isTimerSettingsUpdating = true;
            VibrateSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_vibrate")?.Value);
            SoundSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_sound")?.Value);
            Timer123Switch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_123_sound")?.Value);
            RepsSoundSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_reps_sound")?.Value);
            if (RepsSoundSwitch.IsToggled && RepsSoundSwitch.IsToggled)
            {
                SoundSwitch.IsToggled = false;
                LocalDBManager.Instance.SetDBSetting("timer_sound", "false");
            }
            AutostartSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_autostart")?.Value);
            AutosetSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_autoset")?.Value);
            FullscreenSwitch.IsToggled = Convert.ToBoolean(LocalDBManager.Instance.GetDBSetting("timer_fullscreen")?.Value);
            if (BetweenExercisesSwitch != null)
            {
                var betweenExercisesSetting = LocalDBManager.Instance.GetDBSetting("timer_betweenexercises");
                if (betweenExercisesSetting != null && !string.IsNullOrEmpty(betweenExercisesSetting.Value))
                {
                    BetweenExercisesSwitch.IsToggled = Convert.ToBoolean(betweenExercisesSetting.Value);
                }
            }
            TimerEntry.Text = LocalDBManager.Instance.GetDBSetting("timer_count")?.Value;
            LocalDBManager.Instance.SetDBSetting("timer_remaining", TimerEntry.Text);
            _isTimerSettingsUpdating = false;
        }
        catch (Exception ex)
        {
            _isTimerSettingsUpdating = false;
        }
    }
    private void SetProduction()
    {
        LocalDBManager.Instance.SetDBSetting("Environment", "Production");
        DrMuscleRestClient.Instance.ResetBaseUrl();
    }
    private void SetStaging()
    {
        LocalDBManager.Instance.SetDBSetting("Environment", "Staging");
        DrMuscleRestClient.Instance.ResetBaseUrl();
    }
    private async void LogOut()
    {
        try
        {

            HideWithoutAnimations();
            RemoveToken();
            CancelNotification();
            LocalDBManager.Instance.Reset();
            CurrentLog.Instance.Reset();
            App.IsV1User = false;
            App.IsV1UserTrial = false;
            App.IsCongratulated = false;
            App.IsSupersetPopup = false;
            App.IsFreePlan = false;
            ((App)Application.Current).UserWorkoutContexts.workouts = new GetUserWorkoutLogAverageResponse();
            ((App)Application.Current).UserWorkoutContexts.SaveContexts();
            ((App)Application.Current).WorkoutHistoryContextList.Histories = new List<HistoryModel>();
            ((App)Application.Current).WorkoutHistoryContextList.SaveContexts();
            ((App)Application.Current).WorkoutLogContext.WorkoutLogSeriesByExerciseRef = new Dictionary<long, ObservableCollection<WorkoutLogSerieModelRef>>();
            ((App)Application.Current).WorkoutLogContext.SaveContexts();
            await Navigation.PopToRootAsync();
            ((App)Application.Current).displayCreateNewAccount = true;
            WelcomePage welcomePage = new WelcomePage();
            welcomePage.OnBeforeShow();
            await Application.Current.MainPage.Navigation.PushAsync(welcomePage);
            //Application.Current.MainPage = new NavigationPage(welcomePage);
        }
        catch (Exception ex)
        {

        }

    }

    private async void RemoveToken()
    {
        try
        {
            //string email = LocalDBManager.Instance.GetDBSetting("email").Value;
            //await Task.Delay(1000);
            ////Live
            //SendBirdClient.Init("********-270F-446B-BD61-0043FAA8D641");
            ////Test
            ////SendBirdClient.Init("05F82C36-1159-4179-8C49-5910C7F51D7D");
            //if (!email.ToLower().Equals("<EMAIL>"))
            //    SendBirdClient.Connect(email, Connect_Handler);
            //else
            //    SendBirdClient.Connect(email, "****************************************", Connect_Handler);
            var res = await DrMuscleRestClient.Instance.RemoveDeviceToken(new DeviceModel() { DeviceToken = Config.RegisteredDeviceToken });

        }
        catch (Exception ex)
        {

        }
    }

    protected override void OnSizeAllocated(double width, double height)
    {
        base.OnSizeAllocated(width, height);
        try
        {

            if (GeneralStack != null)
            {
                if (Device.RuntimePlatform.Equals(Device.iOS))
                {
                    //GeneralStack.Margin = new Thickness(0, 0, 0, 0);
                    //BotStack.Margin = new Thickness(0, App.StatusBarHeight, 0, 0);
                    //HomeStack.Margin = new Thickness(0, App.StatusBarHeight, 0, 0);
                    //TimerStack.Margin = new Thickness(0, App.StatusBarHeight, 0, 0);
                    //FeaturedStack.Margin = new Thickness(0, App.StatusBarHeight, 0, 0);
                    //MealPlanStack.Margin = new Thickness(0, App.StatusBarHeight, 0, 0);
                    //PancakeContainer.Margin = new Thickness(0);
                    //PancakeContainer.Padding = new Thickness(0, App.StatusBarHeight + 10, 0, 0);
                }
                else
                {
                    if (TimerStack != null)
                        TimerStack.Margin = new Thickness(0, 20, 0, 0);
                }
            }
        }
        catch (Exception ex)
        {

        }
    }
    public async Task<bool> ToggleMenu()
    {
        try
        {
            if (isMenuOpen)
            {
                this.WidthRequest = 0;
                // Close the menu
                await SlideMenuGrid.TranslateTo(250, 0, 250, Easing.SinInOut);
                SlideMenuGrid.IsVisible = false;
                IsVisible = false;
                App.IsMealPlanMenuOpened = false;
            }
            else
            {
                this.WidthRequest = 250;
                // Open the menu
                SlideMenuGrid.IsVisible = true;
                await SlideMenuGrid.TranslateTo(0, 0, 250, Easing.SinInOut);
                App.IsMealPlanMenuOpened = true;

            }
        }
        catch (Exception ex)
        {

        }
        isMenuOpen = !isMenuOpen;
        return isMenuOpen;
    }

    public async void ToggleMenu1()
    {
        try
        {
            if (isMenuOpen)
            {
                this.WidthRequest = 0;
                // Close the menu
                await SlideMenuGrid.TranslateTo(250, 0, 250, Easing.SinInOut);
                SlideMenuGrid.IsVisible = false;
                IsVisible = false;
            }
            else
            {
                this.WidthRequest = App.ScreenWidth;
                // Open the menu
                SlideMenuGrid.IsVisible = true;
                await SlideMenuGrid.TranslateTo(0, 0, (uint)this.WidthRequest, Easing.SinInOut);

            }
        }
        catch (Exception ex)
        {

        }
        isMenuOpen = !isMenuOpen;
    }
    private async void BtnFeatureWorkout_Clicked(object sender, EventArgs e)
    {
        HideWithoutAnimations();
        try
        {
            if (App.IsFreePlan)
            {

              var alert = await HelperClass.DisplayCustomPopup("You discovered a premium feature!",  "Upgrading will unlock featured programs.", "Upgrade","Maybe later");

                                      alert.ActionSelected += (sender,action) => {
                                         if(action == PopupAction.OK){
                                             SubscriptionPage page = new SubscriptionPage();
                                             page.OnBeforeShow();
                                             Navigation.PushAsync(page);
                                             //PagesFactory.PushAsync<SubscriptionPage>();
                                        }
                                      };

                // await Task.Delay(100);



                // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
                // {
                //     Message = "Upgrading will unlock featured programs.",
                //     Title = "You discovered a premium feature!",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "Upgrade",
                //     CancelText = "Maybe later",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             Navigation.PushAsync(page);
                //             //PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //         else
                //         {

                //         }
                //     }
                // };
               // await Task.Delay(100);
                //UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
            }
            else
            {
                try
                {
                    //await Navigation.PushAsync(new TestPage());
                    await Navigation.PushAsync(new FeaturedProgramPage());
                }
                catch (Exception ex)
                {

                }
            }
        }
        catch (Exception ex)
        {

        }
    }

    private async void BtnResetPlan_Clicked(object sender, EventArgs e)
    {
        try {
      var alert = await HelperClass.DisplayCustomPopup("Are you sure?","", "Reset plan","Cancel");
                 var results =false;

                                      alert.ActionSelected += async (sender,action) => {
                                         if(action == PopupAction.OK){
                                             HideWithoutAnimations();
                                             App.IsResetPlan = true;
                                              try
                                            {
                                                if ((bool)(Navigation.NavigationStack?.Any()))
                                                {
                                                    var currentPage = Navigation.NavigationStack.LastOrDefault();
                                                    if (currentPage is MealInfoPage)
                                                    {
                                                        // Navigation.RemovePage(currentPage);

                                                        var newMealPage = new MealInfoPage();
                                                        newMealPage.OnBeforeShow();
                                                        Navigation.InsertPageBefore(newMealPage, currentPage);

                                                        // Then remove the current page
                                                        Navigation.RemovePage(currentPage);
                                                    }
                                                }
                                                else
                                                {
                                                      try
                                                      {
                                                          MealInfoPage mealInfoPage = new MealInfoPage();
                                                          mealInfoPage.OnBeforeShow();
                                                          await Navigation.PushAsync(mealInfoPage);
                                                      }
                                                      catch (Exception ex)
                                                      {

                                                      }
                                                  }
                                            }
                                            catch (Exception ex)
                                            {

                                            }
                                        }else{
                                             HideWithoutAnimations();
                                        }
                                      };

            // await Task.Delay(100);





            // ConfirmConfig ShowWelcomePopUp2 = new ConfirmConfig()
            // {
            //     Message = "",
            //     Title = "Are you sure?",
            //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
            //     OkText = "Reset plan",
            //     CancelText = "Cancel",
            //     OnAction = async (bool ok) =>
            //     {
            //         if (ok)
            //         {
            //             HideWithoutAnimations();
            //             App.IsResetPlan = true;
            //             try
            //             {
            //                 if ((bool)(Navigation.NavigationStack?.Any()))
            //                 {
            //                     var currentPage = Navigation.NavigationStack.LastOrDefault();
            //                     if (currentPage is MealInfoPage)
            //                     {
            //                         // Navigation.RemovePage(currentPage);

            //                         var newMealPage = new MealInfoPage();
            //                         newMealPage.OnBeforeShow();
            //                         Navigation.InsertPageBefore(newMealPage, currentPage);

            //                         // Then remove the current page
            //                         Navigation.RemovePage(currentPage);
            //                     }
            //                 }
            //                 else
            //                 {
            //                     MealInfoPage mealInfoPage = new MealInfoPage();
            //                     mealInfoPage.OnBeforeShow();
            //                     await Navigation.PushAsync(mealInfoPage);
            //                 }
            //             }
            //             catch (Exception ex)
            //             {

            //             }
            //             //await PagesFactory.PushAsync<MealInfoPage>();
            //         }
            //         else
            //         {
            //             HideWithoutAnimations();
            //         }
            //     }
            // };
            // await Task.Delay(100);
            // UserDialogs.Instance.Confirm(ShowWelcomePopUp2);
        }
        catch (Exception ex)
        {

        }
    }

    private void BtnFReviewTips_Clicked(object sender, EventArgs e)
    {
        HideWithoutAnimations();
        var totalMacros = LocalDBManager.Instance.GetDBSetting("totalMacros")?.Value;

        var modalPage = new Views.GeneralPopup("lamp.png", "", "", "");
        modalPage = new Views.GeneralPopup("medal.png", $"", $"", "Review tips", null, false, false, "false", "false", "false", "false", "true", "false", "false", false, true, totalMacros);
        Config.ShowTipsNumber += 1;
        Application.Current.MainPage.ShowPopup(modalPage);
    }

    private void TapMoreReviews_Tapped(object sender, TappedEventArgs e)
    {
        HideWithoutAnimations();
        Browser.OpenAsync("https://dr-muscle.com/reviews/", BrowserLaunchMode.SystemPreferred);
    }

    private void HideWithoutAnimations()
    {
        ToggleMenu();
    }

    private void Handle_BuildVersionTapped(object sender, TappedEventArgs e)
    {
        try
        {
            ActionSheetConfig config = new ActionSheetConfig()
            {
                AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
            };

            bool isProduction = LocalDBManager.Instance.GetDBSetting("Environment") == null || LocalDBManager.Instance.GetDBSetting("Environment").Value == "Production";

            config.Add(isProduction ? $"Production (active)" : $"Production", () =>
            {
                if (LocalDBManager.Instance.GetDBSetting("Environment") == null)
                {
                    SetProduction();
                    return;
                }
                if (LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production")
                {
                    SetProduction();
                    LogOut();
                }
            });
            config.Add(isProduction ? "Staging" : "Staging (active)", () =>
            {
                if (LocalDBManager.Instance.GetDBSetting("Environment") == null)
                {
                    SetStaging();
                    LogOut();

                    return;
                }
                if (LocalDBManager.Instance.GetDBSetting("Environment").Value != "Staging")
                {
                    SetStaging();
                    LogOut();
                }
            });
            config.Add("Crash", () =>
            {
                // uncomment code please
                var kill = DependencyService.Get<IKillAppService>();
                kill.ExitApp();

            });
            config.SetCancel(AppResources.Cancel, null);
            config.SetTitle(AppResources.ChooseEnvironment);
            //config.Options = new List<Acr.UserDialogs.ActionSheetOption>() { "Production API", "Staging (test) API" };
            UserDialogs.Instance.ActionSheet(config);
        }
        catch (Exception ex)
        {

        }
    }

    private void Handle_ModeChange(object sender, TappedEventArgs e)
    {
        string val = LocalDBManager.Instance.GetDBSetting("BetaVersion")?.Value;
        if (val == "Beta")
        {
            ModeLbl.Text = "";
            LocalDBManager.Instance.SetDBSetting("BetaVersion", "Normal");
        }
        else
        {
            ModeLbl.Text = "";
            LocalDBManager.Instance.SetDBSetting("BetaVersion", "Beta");
        }
        HideWithoutAnimations();
    }

    private void TapGestureRecognizer_Tapped(object sender, TappedEventArgs e)
    {
        TimerEntry.Unfocus();
    }

    private async void TimerEntry_Focused(object sender, FocusEventArgs e)
    {
        await Task.Delay(300);
        ShowTimer();
    }

    void TapGestureRecognizer_Tapped_1(System.Object sender, Microsoft.Maui.Controls.TappedEventArgs e)
    {
        ToggleMenu1();
    }
}