﻿using System;
using System.Globalization;
using DrMuscle.Helpers;
using Xamarin.Forms;

namespace DrMuscle.Converters
{
    public class SurveySelectionEnumToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            SatisfactionSurveyEnum satisfactionSurveyEnum = (SatisfactionSurveyEnum)value;
            return satisfactionSurveyEnum != SatisfactionSurveyEnum.None;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}

