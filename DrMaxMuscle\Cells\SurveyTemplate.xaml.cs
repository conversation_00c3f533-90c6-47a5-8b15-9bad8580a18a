using DrMaxMuscle.Helpers;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Cells;

public partial class SurveyTemplate : ContentView
{
    #region privateField
    private static bool _shouldAnimateButton = false;
    private static bool _isProcessRunning = false;
    #endregion
    public SurveyTemplate()
    {
        InitializeComponent();
    }

    void SadReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad)
            return;

        _shouldAnimateButton = true;
        App.SurveyValue = -1;
        MessagingCenter.Send<string>(SatisfactionSurveyEnum.Sad.ToString(), "ResponseGiven");
    }

    void NeutralReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Neutral)
            return;

        _shouldAnimateButton = true;
        App.SurveyValue = 0;
        MessagingCenter.Send<string>(SatisfactionSurveyEnum.Neutral.ToString(), "ResponseGiven");
    }

    void HappyReviewButton_Clicked(System.Object sender, System.EventArgs e)
    {
        if (BindingContext == null || !(BindingContext is BotModel))
            return;

        BotModel botModel = BindingContext as BotModel;
        if (botModel == null || botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Happy)
            return;

        _shouldAnimateButton = true;
        App.SurveyValue = 1;
        MessagingCenter.Send<string>(SatisfactionSurveyEnum.Happy.ToString(), "ResponseGiven");
    }

    /// <summary>
    /// Animate the given ImageButton.
    /// </summary>
    /// <param name="img">Represent the Image button to show animation on it.</param>
    private async void AnimateImage(View img)
    {
        System.Diagnostics.Debug.WriteLine($"Animate Image : {_shouldAnimateButton}");
        if (!_shouldAnimateButton)
            return;

        /* Shake Animation */
        uint Time = 100;
        int Rounds = 2;
        for (int i = 0; i < Rounds; i++)
        {
            await img.TranslateTo(15, 0, Time);
            await img.TranslateTo(0, 0, Time);
            await img.TranslateTo(-15, 0, Time);
            await img.TranslateTo(0, 0, Time);
        }
        _shouldAnimateButton = false;
    }

    protected void OnAppearing()
    {
        BotModel botModel = BindingContext as BotModel;
        if (botModel.SelectedSurveyOption != SatisfactionSurveyEnum.None)
        {
            switch (botModel.SelectedSurveyOption)
            {
                case SatisfactionSurveyEnum.Sad:
                    AnimateImage(PancakeSadReview);
                    break;
                case SatisfactionSurveyEnum.Neutral:
                    AnimateImage(PancakeNeutralReview);
                    break;
                case SatisfactionSurveyEnum.Happy:
                    AnimateImage(PancakeHappyReview);
                    break;
            }
        }
    }

    async void BtnShareFreeTrial_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isProcessRunning)
            return;

        _isProcessRunning = true;
        await HelperClass.ShareApp("Satisfaction_survey");

        _isProcessRunning = false;
    }

    async void BtnRate5Stars_Clicked(System.Object sender, System.EventArgs e)
    {
        await Task.Delay(500);
        await HelperClass.RateApp("rate_5_star_satisfaction_survey");
    }

    async void BtnEmailUs_Clicked(System.Object sender, System.EventArgs e)
    {
        if (_isProcessRunning)
            return;
        _isProcessRunning = true;

        BotModel botModel = BindingContext as BotModel;
        await HelperClass.SendMail(botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad ? "Bad experience with Dr. Muscle" : "Average experience with Dr. Muscle");

        _isProcessRunning = false;
    }

    async void BtnChatWithUs_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (_isProcessRunning)
                return;
            _isProcessRunning = true;

            BotModel botModel = BindingContext as BotModel;
            string messageToSend = string.Empty;
            if (botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Sad)
                messageToSend = "I had a bad experience working out with the app.";
            else if (botModel.SelectedSurveyOption == SatisfactionSurveyEnum.Neutral)
                messageToSend = "I had an average experience working out with the app.";


            //open chat page
            ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<string>(messageToSend, "ChatWithUsFromSatisfactionSurvey");

            _isProcessRunning = false;
        }
        catch (Exception ex)
        {
        }
    }
}