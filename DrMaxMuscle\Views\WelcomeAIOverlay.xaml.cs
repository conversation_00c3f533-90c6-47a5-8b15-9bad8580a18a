﻿using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.User.OnBoarding;
using DrMuscleWebApiSharedModel;
using Newtonsoft.Json;
using Microsoft.Maui.Networking;
using System.Globalization;
using System.Text.RegularExpressions;
using DrMaxMuscle.Utility;

namespace DrMaxMuscle.Views;

public partial class WelcomeAIOverlay : ContentPage
{
    public WelcomeAIOverlay()
    {
        InitializeComponent();

        try
        {
            NavigationPage.SetHasNavigationBar(this, false);
            if (LocalDBManager.Instance.GetDBSetting("gender")?.Value.Trim() != "Man")
            {
                ImgGender.Source = "exercise_background";
            }
            LblAssistUser.Text = "\nI'll be here for you every step of the way, analyzing your workouts, giving you personalized tips, and more.";

        }
        catch (Exception ex)
        {

        }
    }

    public void SetDetails(string title, string desc)
    {
        var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
        //DateTime currentTime = DateTime.Now;
        //int currentHour = currentTime.Hour;
        //string dayTime = (currentHour >= 0 && currentHour < 12) ? " morning " : (currentHour >= 12 && currentHour < 18) ? " afternoon " : " evening ";
        //string dayTime = (currentHour >= 5 && currentHour < 12) ? " morning " : (currentHour >= 12 && currentHour < 17) ? " afternoon " : (currentHour >= 17 && currentHour < 21) ? " evening " : " night ";

        LblGptTitle.Text = $"Welcome {name}!";
        if (string.IsNullOrEmpty(desc))
        {
            AnaliesAIWithChatGPT();
        }
        else
        {
            LblGptDesc.Text = $"As your AI coach, I'm pumped to help you reach your fitness goals. Let's begin!\n\n{desc}";

            DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
            {
                ReceiverId = AppThemeConstants.ChatReceiverId,
                Message = LblGptDesc.Text,
                IsFromAI = true
            });
            RegisterModel registerModel = JsonConvert.DeserializeObject<RegisterModel>(LocalDBManager.Instance.GetDBSetting("ReadyRegisterModel").Value);
            if (registerModel != null)
            {
                registerModel.AIMessage = LblGptDesc.Text;
                DrMuscleRestClient.Instance.RegisterUserSendEmail(registerModel);
            }

        }

        // Note Send Firebase value 0 for Old btn & 1 for new button;

        //if (LocalDBManager.Instance.GetDBSetting("BetaLoaded")?.Value != "true")
        //{
        //continueBtnFrame.IsVisible = true;
        //LblAssistUser.IsVisible = false;
        //btnStack.IsVisible = false;
        //}
        //else
        //{
        //    continueBtnFrame.IsVisible = false;
        //    LblAssistUser.IsVisible = true;
        //    btnStack.IsVisible = true;
        //}
        //string val = LocalDBManager.Instance.GetDBSetting("BetaVersion")?.Value;
        //if (!string.IsNullOrEmpty(val))
        //{
        //    if (val.Equals("Beta") || val.Equals("Beta1"))
        //    {
        continueBtnFrame.IsVisible = false;
        LblAssistUser.IsVisible = true;
        btnStack.IsVisible = true;
        //    }
        //}

    }

    //async Task BoomSuccessPopup()
    //{
    //    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
    //    var modalPage = new Views.GeneralPopup("Lists.png", "Settings saved", "Do a demo workout to unlock the full experience", "Demo workout", new Thickness(18, 0, 0, 0));
    //    modalPage.Disappearing += (sender2, e2) =>
    //    {
    //        waitHandle.Set();
    //    };
    //    await PopupNavigation.Instance.PushAsync(modalPage);

    //    await Task.Run(() => waitHandle.WaitOne());

    //}

    async void Close_Tapped(object sender, EventArgs e)
    {


        // // OpenChat_Clicked(sender, e);
        // return;
        //try
        //{
        //    await BoomSuccessPopup();
        //    if (PopupNavigation.Instance.PopupStack.Count > 0)
        //        PopupNavigation.Instance.PopAllAsync();
        //    await OpenDemo();
        //}
        //catch (Exception ex)
        //{

        //}
        //PopupNavigation.Instance.PushAsync(new WorkoutPreviewOverlay());
        try
        {

            if (!CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
            {
                if (((App)Application.Current).UserWorkoutContexts.workouts != null)
                {
                    ((App)Application.Current).UserWorkoutContexts.workouts.LastWorkoutDate = DateTime.UtcNow;
                    ((App)Application.Current).UserWorkoutContexts.SaveContexts();
                }
            }
            CurrentLog.Instance.IsFromEndExercise = false;
            // uncomment code please
            //if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("ChooseYourDrMuscleExercisePage"))
            //    await PagesFactory.PushAsync<ChooseYourDrMuscleExercisePage>();
            //else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("ChooseYourCustomExercisePage"))
            //    await PagesFactory.PushAsync<ChooseYourCustomExercisePage>();
            //else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisePage"))
            //    await PagesFactory.PushAsync<AllExercisePage>();
            //else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("AllExercisesView"))
            //    await PagesFactory.PushAsync<AllExercisePage>();
            //else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("KenkoSingleExercisePage"))
            //    await PagesFactory.PushAsync<AllExercisePage>();
            //else if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("NewDemoPage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("PreviewOverlay") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("NewDemoPage2") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoChallengePage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("RightSideMasterPage"))
            if (CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("NewDemoPage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("PreviewOverlay") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("NewDemoPage2") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoChallengePage") || CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("RightSideMasterPage"))
            {
                CurrentLog.Instance.IsDemoRunningStep1 = true;
                
                //if (PopupNavigation.Instance.PopupStack.Count > 0)
                //    PopupNavigation.Instance.PopAsync();
                CurrentLog.Instance.IsDemoRunningStep2 = false;
                CurrentLog.Instance.IsDemoRunningStep1 = false;
                long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId")?.Value);
                long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId")?.Value);

                var upi = new GetUserProgramInfoResponseModel()
                {
                    NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel")?.Value },
                    RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value) },
                };
                if (upi != null)
                {
                    CurrentLog.Instance.WorkoutStarted = true;
                    if (Device.RuntimePlatform.Equals(Device.Android))
                    {
                        await Navigation.PopToRootAsync(true);
                        App.IsDemoProgress = false;
                        App.IsWelcomeBack = true;
                        App.IsNewUser = true;
                        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                        CurrentLog.Instance.Exercise1RM.Clear();

                        Device.BeginInvokeOnMainThread(async () =>
                        {
                            //MainTabbedPage p = App.GetTabbedPage<MainTabbedPage>();
                            //Application.Current.MainPage = new NavigationPage(App.tabpage);
                            await Navigation.PopToRootAsync(true);
                            await Task.Delay(1000);
                            MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
                        });

                    }
                    else
                    {

                        App.IsDemoProgress = false;
                        App.IsWelcomeBack = true;
                        App.IsNewUser = true;
                        LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                        CurrentLog.Instance.Exercise1RM.Clear();
                        //MainTabbedPage p = App.GetTabbedPage<MainTabbedPage>();
                        //Application.Current.MainPage = new NavigationPage(p);
                        await Navigation.PopToRootAsync(true);
                        await Task.Delay(1000);
                        MessagingCenter.Send<SignupFinishMessage>(new SignupFinishMessage(), "SignupFinishMessage");
                    }


                }




            }
            else
            {
                //if (PopupNavigation.Instance.PopupStack.Count > 0)
                //    PopupNavigation.Instance.PopAsync();
                CurrentLog.Instance.IsMovingOnBording = true;
                MainOnboardingPage page = new MainOnboardingPage();
                page.OnBeforeShow();
                await Application.Current.MainPage.Navigation.PushAsync(page);
                //Application.Current.MainPage = new NavigationPage(page);
                //PagesFactory.PushAsync<MainOnboardingPage>();

            }

        }
        catch (Exception ex)
        {
            //if (PopupNavigation.Instance.PopupStack.Count > 0)
            //    PopupNavigation.Instance.PopAsync();
            CurrentLog.Instance.IsMovingOnBording = true;
            MainOnboardingPage page = new MainOnboardingPage();
            page.OnBeforeShow();
            await Application.Current.MainPage.Navigation.PushAsync(page);
            //Application.Current.MainPage = new NavigationPage(page);
            //PagesFactory.PushAsync<MainOnboardingPage>();
        }
    }

    private async Task<string> AnaliesAIWithChatGPT(bool isloader = false, double temperature = 0.7, int maxTokens = 2500, double topP = 1, double frequencyPenalty = 0, double presencePenalty = 0)
    {
        try
        {
            //LblGptTitle.Text = "Loading...";
            LblGptDesc.Text = "Loading analysis...";
            var name = LocalDBManager.Instance.GetDBSetting("firstname")?.Value;
            var experience = LocalDBManager.Instance.GetDBSetting("CustomExperience")?.Value;
            var iskg = LocalDBManager.Instance.GetDBSetting("massunit")?.Value == "kg";
            var value = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("BodyWeight")?.Value.Replace(",", "."), System.Globalization.CultureInfo.InvariantCulture);
            var weights = new MultiUnityWeight(value, "kg");
            var goalWeightNum = Convert.ToDecimal(LocalDBManager.Instance.GetDBSetting("WeightGoal")?.Value.Replace(",", "."), CultureInfo.InvariantCulture);
            var fetchweights = new MultiUnityWeight((decimal)value, "kg");
            var goalMultiWeight = new MultiUnityWeight((decimal)goalWeightNum, "kg");
            var weight = "";
            var goalWeight = "";
            if (iskg)
            {
                weight = string.Format("{0:0.##} kg", fetchweights.Kg);
                goalWeight = string.Format("{0:0.##} kg", goalMultiWeight.Kg);
            }
            else
            {
                weight = string.Format("{0:0.##} lbs", fetchweights.Lb);
                goalWeight = string.Format("{0:0.##} lbs", goalMultiWeight.Lb);
            }
            var gender = LocalDBManager.Instance.GetDBSetting("gender")?.Value.Trim();
            var age = LocalDBManager.Instance.GetDBSetting("Age")?.Value;
            var ageInt = int.Parse(age);

            var focusText = LocalDBManager.Instance.GetDBSetting("focusText")?.Value;
            if (!string.IsNullOrEmpty(focusText))
            {
                focusText = focusText.Replace("\nStronger sex drive", "");
                focusText = focusText.Replace("Stronger sex drive", "");
            }
            if (string.IsNullOrEmpty(focusText))
                focusText = "Better health";
            var muscleProirity = "";//
            var bodyPart = LocalDBManager.Instance.GetDBSetting("BodypartPriority")?.Value;
            if (!string.IsNullOrEmpty(bodyPart))
                muscleProirity = $"Muscle priority: {bodyPart}\n";
            var program = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value;
            decimal targetIntake = 0;
            try
            {

                var weightinPounds = fetchweights.Lb;
                if (weightinPounds > 150 && Config.UserHeight != 0 && Config.UserHeight > 0 && ageInt != null && ageInt > 0)
                {
                    //9.99*weight (Kg) + 6.25*height (cm) − 4.92*age + 166*sex (M = 1; F = 0) −161
                    targetIntake = Math.Round(((decimal)9.99 * (decimal)fetchweights.Kg) + ((decimal)6.25 * (decimal)Config.UserHeight) - ((decimal)4.92 * (decimal)ageInt) + (166 * (gender == "Man" ? 1 : 0)) - 161, 2);


                    if (goalMultiWeight.Kg > fetchweights.Kg)
                        targetIntake = targetIntake * (decimal)1.15;
                    else
                        targetIntake = targetIntake * (decimal)0.75;

                    //Activity level Suggested by Carl: https://app.startinfinity.com/b/7N8FXx54wWE/sDxhY6Rxbjh/132a879f-7be1-4b91-b91c-408c72e770b3?t=comments&open=true&view=c956a7e0-0553-42e6-af24-7b6c915d3c44
                    targetIntake = targetIntake * (decimal)1.5;
                }
                else
                {
                    if (goalMultiWeight.Kg > fetchweights.Kg)
                        targetIntake = (decimal)Math.Round(15 * Math.Round(fetchweights.Lb, 2), 4);
                    else
                        targetIntake = (decimal)Math.Round(11 * Math.Round(fetchweights.Lb, 2), 4);

                }
                
            }
            catch (Exception ex)
            {

            }

            var protein = $"{Math.Round(fetchweights.Kg * (decimal)1.8)} - {Math.Round(fetchweights.Kg * (decimal)2.5)} g";


            /*

             string macros = $"Target intake: {_targetIntake}\n";
                macros += $"\nProtein range: ";
                macros += $"{_userBodyWeightInPounds} g";
                _targetIntake = _targetIntake - (_userBodyWeightInPounds * 4);

                macros += $"\nCarbs range: " + Math.Round((double)_targetIntake * 0.6 / 4) + " g";
                macros += $"\nFats range: ";
                macros += Math.Round((double)_targetIntake * 0.4 / 9) + " g";

                LblCarbText2.Text = Math.Round((double)_targetIntake * 0.6 / 4) + " g";
                LblProteinText2.Text = $"{_userBodyWeightInPounds} g";
                LblFatText2.Text = Math.Round((double)_targetIntake * 0.4 / 9) + " g";
             */

            var query = $"You are a coach in a mobile app. The app creates custom programs and updates them automatically every workout. I just finished creating an account and you're giving me my program.\n\nGender: {gender}\nExperience: {experience}\nAge: {age}\nWeight: {weight}\nTarget weight: {goalWeight}\nGoal: {focusText}\n{muscleProirity}Program: {program}\n\nWhat can you tell me about that? Write a few paragraphs of about 40 words each in the style of John Meadows. Use short sentences and short, common words. Follow this outline:\n\nParagraph 1: Analyze my profile and program.\nParagraph 2: To help reach my goal of... faster, my program updates automatically.\nParagraph 3: Diet tips to reach my goal. Calories ({Math.Round(targetIntake)} a day). Protein ({protein} per day). Mention I can track my body weight in the app to get up-to-date macro recommendations and personalized coaching.\nParagraph 4: Other tips to reach my goal (e.g. consistency, recovery)\nParagraph 5: A quote from Arnold Schwarzenegger. Attribute it. Mention results.\n\nInclude numbers. Number paragraphs with 1, 2, 3..., but don't include headings. Don't use hashtags. Don't mention bodybuilding or bodybuilders.";

            // Create an HTTP client
            var tokenCount = 4097 - query.Length;
            string openaiKey = AppThemeConstants.GPTKey;
            string prompt = query;
            if (isloader)
                UserDialogs.Instance.ShowLoading();
            using (var _httpClient = new HttpClient())
            {
                _httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", openaiKey);
                string model = "gpt-3.5-turbo";//"text-davinci-002"; // model for GPT-3.5 Turbo
                var requestUrl = "https://api.openai.com/v1/chat/completions";
                var requestBody = new
                {
                    messages = new[] { new { role = "user", content = prompt } },
                    model = model,
                    temperature = temperature,
                    max_tokens = tokenCount,
                    top_p = topP,
                    frequency_penalty = frequencyPenalty,
                    presence_penalty = presencePenalty
                };
                var requestBodyJson = Newtonsoft.Json.JsonConvert.SerializeObject(requestBody);
                var requestContent = new StringContent(requestBodyJson, System.Text.Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, requestContent);
                //response.EnsureSuccessStatusCode();
                //UserDialogs.Instance.HideLoading();
                var responseBodyJson = await response.Content.ReadAsStringAsync();

                var chatGPTResponse = JsonConvert.DeserializeObject<ChatGPTResponse>(responseBodyJson);

                var chatResponse = chatGPTResponse.choices[0].message.content;
                var AILoadedText = chatResponse;
                if (string.IsNullOrEmpty(AILoadedText))
                {
                    AILoadedText = "";
                }
                else
                {
                    //var AITitle = AILoadedText.Substring(0, AILoadedText.IndexOf('\n'));
                    var aiDescription = AILoadedText;//.Replace($"{AITitle}\n", "");
                                                     //if (!aiDescription.Contains("\n\n"))
                                                     //    aiDescription = AILoadedText.Replace($"\n", "\n\n");

                    //AITitle = AITitle.Replace("Title: ", "");
                    //var aiTitle = AITitle.Replace("\"", "");
                    //aiTitle = aiTitle.Replace("Paragraph 1: ", "");
                    //aiTitle = aiTitle.Replace(".", "");

                    //aiDescription = aiDescription.Replace("Paragraph 1: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 2: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 3: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 4: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 5: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 6: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 7: ", "");
                    //aiDescription = aiDescription.Replace("Paragraph 8: ", "");

                    if (aiDescription.StartsWith("Paragraph 1: "))
                    {
                        aiDescription = aiDescription.Substring(13);
                        aiDescription = aiDescription.Replace("\nParagraph 2: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 3: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 4: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 5: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 6: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 7: ", "\n");
                        aiDescription = aiDescription.Replace("\nParagraph 8: ", "\n");
                    }
                    if (aiDescription.StartsWith("1. "))
                    {
                        aiDescription = aiDescription.Substring(3);
                        aiDescription = aiDescription.Replace("\n2. ", "\n");
                        aiDescription = aiDescription.Replace("\n3. ", "\n");
                        aiDescription = aiDescription.Replace("\n4. ", "\n");
                        aiDescription = aiDescription.Replace("\n5. ", "\n");
                        aiDescription = aiDescription.Replace("\n6. ", "\n");
                        aiDescription = aiDescription.Replace("\n7. ", "\n");
                        aiDescription = aiDescription.Replace("\n8. ", "\n");
                    }
                    if (aiDescription.StartsWith("(1) "))
                    {
                        aiDescription = aiDescription.Replace("(1) ", "");
                        aiDescription = aiDescription.Replace("(2) ", "");
                        aiDescription = aiDescription.Replace("(3) ", "");
                        aiDescription = aiDescription.Replace("(4) ", "");
                        aiDescription = aiDescription.Replace("(5) ", "");
                        aiDescription = aiDescription.Replace("(6) ", "");
                        aiDescription = aiDescription.Replace("(7) ", "");
                        aiDescription = aiDescription.Replace("(8) ", "");
                    }
                    if (aiDescription.StartsWith("1) "))
                    {
                        aiDescription = aiDescription.Replace("1) ", "");
                        aiDescription = aiDescription.Replace("2) ", "");
                        aiDescription = aiDescription.Replace("3) ", "");
                        aiDescription = aiDescription.Replace("4) ", "");
                        aiDescription = aiDescription.Replace("5) ", "");
                        aiDescription = aiDescription.Replace("6) ", "");
                        aiDescription = aiDescription.Replace("7) ", "");
                        aiDescription = aiDescription.Replace("8) ", "");
                    }

                    if (!aiDescription.Contains("\n\n"))
                    {
                        aiDescription = aiDescription.Replace("\n", "\n\n");
                    }
                    CurrentLog.Instance.AiDescription = aiDescription;
                    //LblGptTitle.Text = aiTitle;

                    LblGptDesc.Text = $"As your AI coach, I'm pumped to help you reach your fitness goals. Let's begin!\n\n{aiDescription}";
                    DrMuscleRestClient.Instance.SendAdminMessageWithoutLoader(new ChatModel()
                    {
                        ReceiverId = AppThemeConstants.ChatReceiverId,
                        Message = LblGptDesc.Text,
                        IsFromAI = true
                    });
                    RegisterModel registerModel = JsonConvert.DeserializeObject<RegisterModel>(LocalDBManager.Instance.GetDBSetting("ReadyRegisterModel").Value);
                    if (registerModel != null)
                    {
                        registerModel.AIMessage = LblGptDesc.Text;
                        DrMuscleRestClient.Instance.RegisterUserSendEmail(registerModel);
                    }
                }


                return chatResponse;
            }
        }
        catch (Exception ex)
        {
            return "";
            UserDialogs.Instance.HideLoading();
        }
        finally
        {

        }
    }


    async Task OpenDemo()
    {
        CurrentLog.Instance.CurrentExercise = new ExerciceModel()
        {
            BodyPartId = 7,
            VideoUrl = "https://youtu.be/Plh1CyiPE_Y",
            IsBodyweight = true,
            IsEasy = false,
            IsFinished = false,
            IsMedium = false,
            IsNextExercise = false,
            IsNormalSets = false,
            IsSwapTarget = false,
            IsSystemExercise = true,
            IsTimeBased = false,
            IsUnilateral = false,
            Label = "Crunch",
            RepsMaxValue = null,
            RepsMinValue = null,
            Timer = null,
            Id = 864
        };
        App.IsDemoProgress = true;
        LocalDBManager.Instance.SetDBSetting("DemoProgress", "true");
        // uncomment code please
        //await PagesFactory.PushAsync<Screens.Demo.NewDemoPage>();

    }
    private async void HelpWithGoal_Clicked(object sender, EventArgs e)
    {
        try
        {

            await SetSetupDetails();
            await Navigation.PopToRootAsync(true);
            //await PagesFactory.PopToRootAsync(true);
            //if (PopupNavigation.Instance.PopupStack.Count > 0)
            //    PopupNavigation.Instance.PopAsync();
            //MainTabbedPage p = App.GetTabbedPage<MainTabbedPage>();
            //Application.Current.MainPage = new NavigationPage(p);

            // Navigate to the third tab after MainPage is set
            var navPage = (NavigationPage)Application.Current.MainPage;
            var tabbedPage = (MainTabbedPage)navPage.CurrentPage;

            // Ensure the Children count is more than 2
            if (tabbedPage.Children.Count > 2)
            {
                tabbedPage.CurrentPage = tabbedPage.Children[2];
            }
            else
            {
                // Handle the case when there are fewer than 3 tabs
                Console.WriteLine("There are fewer than 3 tabs in MainTabbedPage.");
            }
            //((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            //await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "HelpWithGoalChatMessage");

        }
        catch (Exception ex)
        {

        }
    }
    private async void InspireMe_Clicked(object sender, EventArgs e)
    {
        try
        {

            await SetSetupDetails();
            await Navigation.PopToRootAsync(true);
            //MainTabbedPage p = App.GetTabbedPage<MainTabbedPage>();
            //Application.Current.MainPage = new NavigationPage(p);

            // Navigate to the third tab after MainPage is set
            var navPage = (NavigationPage)Application.Current.MainPage;
            var tabbedPage = (MainTabbedPage)navPage.CurrentPage;

            // Ensure the Children count is more than 2
            if (tabbedPage.Children.Count > 2)
            {
                tabbedPage.CurrentPage = tabbedPage.Children[2];
            }
            else
            {
                // Handle the case when there are fewer than 3 tabs
                Console.WriteLine("There are fewer than 3 tabs in MainTabbedPage.");
            }
            //await PagesFactory.PopToRootAsync(true);
            //if (PopupNavigation.Instance.PopupStack.Count > 0)
            //    PopupNavigation.Instance.PopAsync();
            //((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage() { IsInspireMe = true }, "HelpWithGoalChatMessage");
        }
        catch (Exception ex)
        {

        }
    }

    private async void OpenChat_Clicked(object sender, EventArgs e)
    {
        try
        {
            await SetSetupDetails();
            await Navigation.PopToRootAsync(true);
            //MainTabbedPage p = App.GetTabbedPage<MainTabbedPage>();
            //Application.Current.MainPage = new NavigationPage(p);

            // Navigate to the third tab after MainPage is set
            var navPage = (NavigationPage)Application.Current.MainPage;
            var tabbedPage = (MainTabbedPage)navPage.CurrentPage;

            // Ensure the Children count is more than 2
            if (tabbedPage.Children.Count > 2)
            {
                tabbedPage.CurrentPage = tabbedPage.Children[2];
            }
            else
            {
                // Handle the case when there are fewer than 3 tabs
                Console.WriteLine("There are fewer than 3 tabs in MainTabbedPage.");
            }
            //if (PopupNavigation.Instance.PopupStack.Count > 0)
            //    PopupNavigation.Instance.PopAsync();
            //((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMaxMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "AIChat");
        }
        catch (Exception ex)
        {

        }
    }
    public async Task SetSetupDetails()
    {
        if (!CurrentLog.Instance.EndExerciseActivityPage.FullName.Contains("DemoWorkoutPage"))
        {
            if (((App)Application.Current).UserWorkoutContexts.workouts != null)
            {
                ((App)Application.Current).UserWorkoutContexts.workouts.LastWorkoutDate = DateTime.UtcNow;
                ((App)Application.Current).UserWorkoutContexts.SaveContexts();
            }
        }
        CurrentLog.Instance.IsFromEndExercise = false;

        CurrentLog.Instance.IsDemoRunningStep1 = true;


        CurrentLog.Instance.IsDemoRunningStep2 = false;
        CurrentLog.Instance.IsDemoRunningStep1 = false;
        long workoutTemplateId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedWorkoutId")?.Value);
        long programId = Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("recommendedProgramId")?.Value);

        var upi = new GetUserProgramInfoResponseModel()
        {
            NextWorkoutTemplate = new WorkoutTemplateModel() { Id = workoutTemplateId, Label = LocalDBManager.Instance.GetDBSetting("recommendedWorkoutLabel")?.Value },
            RecommendedProgram = new WorkoutTemplateGroupModel() { Id = programId, Label = LocalDBManager.Instance.GetDBSetting("recommendedProgramLabel")?.Value, RemainingToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value), RequiredWorkoutToLevelUp = Convert.ToInt32(LocalDBManager.Instance.GetDBSetting("recommendedRemainingWorkout")?.Value) },
        };
        if (upi != null)
        {
            CurrentLog.Instance.WorkoutStarted = true;
            if (Device.RuntimePlatform.Equals(Device.Android))
            {

                App.IsDemoProgress = false;
                App.IsWelcomeBack = true;
                App.IsNewUser = true;
                LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                CurrentLog.Instance.Exercise1RM.Clear();

                Device.BeginInvokeOnMainThread(async () =>
                {
                    await Navigation.PopToRootAsync();
                    //await PagesFactory.PopToRootAsync(true);
                    App.IsHomeOpenAfterChat = true;
                });

            }
            else
            {

                App.IsDemoProgress = false;
                App.IsWelcomeBack = true;
                App.IsNewUser = true;
                LocalDBManager.Instance.SetDBSetting("DemoProgress", "false");
                CurrentLog.Instance.Exercise1RM.Clear();
                await Application.Current.MainPage.Navigation.PopToRootAsync();
                //await PagesFactory.PopToRootMoveAsync(true);
                App.IsHomeOpenAfterChat = true;
            }


        }
    }

    private async void BtnShare_Clicked(object sender, EventArgs e)
    {
        await HelperClass.ShareApp("Account_Creation_Analysis", "Share_Account_Creation_Analysis", LblGptDesc.Text);
    }
}
