﻿using DrMaxMuscle.Dependencies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class KillAppService : IKillAppService
    {
        public KillAppService()
        {
        }

        public void ExitApp()
        {
            Thread.CurrentThread.Abort();
        }
    }
}
