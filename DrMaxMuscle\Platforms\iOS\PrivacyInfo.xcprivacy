﻿<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <!-- Define Data Collection -->
    <key>NSPrivacyCollectedDataTypes</key>
    <array>
      <dict>
        <key>DataType</key>
        <string>Device ID</string>
        <key>Purposes</key>
        <array>
          <string>App Functionality</string>
        </array>
      </dict>
      <dict>
        <key>DataType</key>
        <string>Crash Data</string>
        <key>Purposes</key>
        <array>
          <string>App Performance</string>
        </array>
      </dict>
      <dict>
        <key>DataType</key>
        <string>App Usage Data</string>
        <key>Purposes</key>
        <array>
          <string>Analytics</string>
        </array>
      </dict>
    </array>

    <!-- Define Accessed APIs (Retaining Your Existing API Categories) -->
    <key>NSPrivacyAccessedAPITypes</key>
    <array>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryFileTimestamp</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>C617.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryDiskSpace</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>E174.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategoryUserDefaults</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>CA92.1</string>
        </array>
      </dict>
      <dict>
        <key>NSPrivacyAccessedAPIType</key>
        <string>NSPrivacyAccessedAPICategorySystemBootTime</string>
        <key>NSPrivacyAccessedAPITypeReasons</key>
        <array>
          <string>35F9.1</string>
        </array>
      </dict>
    </array>
  </dict>
</plist>
