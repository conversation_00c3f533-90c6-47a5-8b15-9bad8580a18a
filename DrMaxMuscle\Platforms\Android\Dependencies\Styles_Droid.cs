﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Platforms.Android.Dependencies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

//[assembly: Dependency(typeof(Styles_Droid))]
namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class Styles_Droid : IStyles
    {
        public int GetStyleId(EAlertStyles alertStyle)
        {
            switch (alertStyle)
            {
                default:
                case EAlertStyles.AlertDialogCustomGray:
                    return Resource.Style.AlertDialogCustomGray;
                case EAlertStyles.AlertDialogCustomGreen:
                    return Resource.Style.AlertDialogCustomGreen;
                case EAlertStyles.AlertDialogCustomRed:
                    return Resource.Style.AlertDialogCustomRed;
                case EAlertStyles.AlertDialogFirstTimeExercise:
                    return Resource.Style.AlertDialogFirstTimeExercise;

                //default: return 0;
            }
        }
    }
}
