﻿<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:pages="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
     BackgroundColor="#AA333333"
                 CloseWhenBackgroundIsClicked="False"
   
             x:Class="DrMaxMuscle.Views.ProgramListPopup">
     <StackLayout Margin="20,20,20,20">
        <StackLayout  Padding="20"
                      BackgroundColor="White"
                      VerticalOptions="CenterAndExpand">
            <Label Text="Select a program to do next?"
                   Style="{StaticResource BoldLabelStyle}"
                   FontSize="20"
                   FontAttributes="Bold"
                   TextColor="Black" />
            <CollectionView x:Name="ProgramListView"
                                HeightRequest="100"
                                BackgroundColor="Transparent"
                                VerticalOptions="FillAndExpand">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Grid HeightRequest="50" Padding="10">
                                <Label Text="{Binding Label}"
                                       HorizontalOptions="StartAndExpand"
                                       VerticalTextAlignment="Center"
                                       Style="{StaticResource LabelStyle}" />
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Tapped="OnItemTapped" />
                            </Grid.GestureRecognizers>
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <!--<t:DrMuscleListView x:Name="ProgramListView"
                    HeightRequest="100"
                    BackgroundColor="Transparent"
                    SeparatorVisibility="None"
                    VerticalOptions="FillAndExpand"
                    SeparatorColor="White">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <ViewCell Height="50">
                            <StackLayout Orientation="Horizontal"
                             BackgroundColor="Transparent">
                                <Label Text="{Binding Label}"
                           HorizontalOptions="StartAndExpand"
                           VerticalTextAlignment="Center"
                           Style="{StaticResource LabelStyle}" />
                            </StackLayout>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </t:DrMuscleListView>-->
        </StackLayout>
    </StackLayout>
</pages:PopupPage>

