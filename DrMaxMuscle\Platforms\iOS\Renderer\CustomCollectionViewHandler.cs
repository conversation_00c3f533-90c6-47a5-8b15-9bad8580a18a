﻿using Microsoft.Maui.Controls.Handlers.Items;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rollbar;
using UIKit;

namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class CustomCollectionViewHandler : CollectionViewHandler
    {
        protected override UIView CreatePlatformView()
        {
            return base.CreatePlatformView();
        }
        protected override void ConnectHandler(UIView platformView)
        {
            base.ConnectHandler(platformView);
            // Now you have access to the native UICollectionView
        }

        // Method to scroll by a specific number of pixels
        public void ScrollByPixels(int pixels)
        {
            try
            {
                var collectionView = FindUICollectionView(PlatformView);
                if (collectionView != null)
                {
                    // Adjust the scrolling offset based on the current content offset
                    //var currentOffset = collectionView.ContentOffset.Y;

                    //// Handle the rotation case by checking the Transform property
                    //bool isRotated = collectionView.Transform.yy == -1;
                    //var newOffsetY = isRotated ? currentOffset - pixels : currentOffset + pixels;

                    //// Ensure the new offset doesn't exceed the bounds (don't scroll past content)
                    //newOffsetY = (nfloat)Math.Max(0, Math.Min(newOffsetY, collectionView.ContentSize.Height - collectionView.Frame.Height));
                    // Apply the new content offset
                    var newOffset = new CoreGraphics.CGPoint(collectionView.ContentOffset.X, collectionView.ContentOffset.Y - pixels);
                    collectionView.SetContentOffset(newOffset, true);
                }
            }
            catch (Exception ex)
            {
                
            }
        }
        private UICollectionView FindUICollectionView(UIView view)
        {
            try
            {
                if (view is UICollectionView collectionView)
                {
                    return collectionView;
                }

                foreach (var subview in view.Subviews)
                {
                    var result = FindUICollectionView(subview);
                    if (result != null)
                    {
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {

            }

            return null;
        }

    }
}

