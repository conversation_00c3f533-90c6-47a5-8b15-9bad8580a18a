﻿using CommunityToolkit.Maui.Views;
using RGPopup.Maui.Pages;

namespace DrMaxMuscle.Views;

public partial class VideoViewPopup : PopupPage
{
    string VideoId = "";
    public VideoViewPopup(string videoId)
    {
        InitializeComponent();
        VideoId = videoId;
        
    }


    protected override void OnAppearing()
    {
        base.OnAppearing();
        if(!string.IsNullOrEmpty(VideoId))
            LoadYouTubeVideo($"https://www.youtube.com/embed/{VideoId}?autoplay=1&start=3&color=white&modestbranding=1");
    }
    private void LoadYouTubeVideo(string videoUrl, string title = "Dr. Muscle")
    {

        try
        {
            // Embed the YouTube video using an iframe
            string html = $"<html><body><iframe width='100%' height='99%' src='{videoUrl}' title='{title}' frameborder='0' allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share' referrerpolicy='strict-origin-when-cross-origin' allowfullscreen></iframe></body></html>";
            // Load the HTML content into the WebView
            //
            if (webVideo != null)
            {
                webVideo.Source = new HtmlWebViewSource { Html = html };
                webVideo.Navigated += WebVideo_Navigated;
            }
        }
        catch (Exception ex)
        {

        }

    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        try
        {
            if (webVideo != null)
            {
                webVideo.Source = null;
            }
        }
        catch (Exception ex)
        {

        }
    }

    private void WebVideo_Navigated(object sender, WebNavigatedEventArgs e)
    {

    }
}
