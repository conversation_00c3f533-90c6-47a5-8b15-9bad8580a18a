﻿#region Assembly Plugin.Firebase.Auth.Google, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// /Users/<USER>/.nuget/packages/plugin.firebase.auth.google/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Auth.Google.dll
// Decompiled with ICSharpCode.Decompiler 
#endregion

using Foundation;
using UIKit;
using Plugin.Firebase.Auth.Platforms.iOS.Extensions;
using Google.SignIn;
using Firebase.Auth;
using Plugin.Firebase.Auth;
using Plugin.Firebase.Auth.Google;
using Plugin.Firebase.Core;
using Plugin.Firebase.Core.Exceptions;

namespace DrMaxMuscle.Plateforms.iOS.Firebase;

public class MyFirebaseAuthGoogleImplementation : DisposableBase, IFirebaseAuthGoogle, IDisposable
{
    public static MyFirebaseAuthGoogleImplementation Instance { get; set;}
    private readonly global::Firebase.Auth.Auth _firebaseAuth;

    private static Lazy<MyGoogleAuth> _googleAuth;

    private static UIViewController ViewController
    {
        get
        {
            UIViewController rootViewController = UIApplication.SharedApplication.KeyWindow.RootViewController;
            if (rootViewController == null)
            {
                throw new NullReferenceException("RootViewController is null");
            }

            return rootViewController.PresentedViewController ?? rootViewController;
        }
    }

    public static void Initialize()
    {
        string clientId = NSMutableDictionary.FromFile("GoogleService-Info.plist")["CLIENT_ID"].ToString();
        SignIn.SharedInstance.Configuration = new Configuration(clientId);
    }

    public static bool OpenUrl(UIApplication app, NSUrl url, NSDictionary options)
    {
        return SignIn.SharedInstance.HandleUrl(url);
    }

    public MyFirebaseAuthGoogleImplementation()
    {
        _firebaseAuth = global::Firebase.Auth.Auth.DefaultInstance;
        _googleAuth = new Lazy<MyGoogleAuth>(() => new MyGoogleAuth());
    }

    public async Task<IFirebaseUser> SignInWithGoogleAsync()
    {
        _ = 1;
        try
        {
            var user = await SignInWithCredentialAsync(await _googleAuth.Value.GetCredentialAsync(ViewController));
            // if (user == null)
            // {
            //     TaskCanceledException ex = new TaskCanceledException("User cancelled sign in.");
            //     throw ex;
            // }
            // else
            // {
                return user;
            // }
        }
        catch (NSErrorException ex)
        {
            throw GetFirebaseAuthException(ex);
        }
    }

    private async Task<IFirebaseUser> SignInWithCredentialAsync(AuthCredential credential)
    {
        AuthDataResult authDataResult = await _firebaseAuth.SignInWithCredentialAsync(credential); 
        // if (authDataResult.User == null)
        // {
        // return null;
        // } 
        // else 
        // {
        return authDataResult.User.ToAbstract(authDataResult.AdditionalUserInfo);
        // }
    }

    private static FirebaseAuthException GetFirebaseAuthException(NSErrorException ex)
    {
        Enum.TryParse<FIRAuthError>(((AuthErrorCode)((IntPtr.Size != 8) ? ((int)ex.Error.Code) : ex.Error.Code)).ToString(), out var result);
        return new FirebaseAuthException(result, ex.Error.LocalizedDescription);
    }

    public async Task<IFirebaseUser> LinkWithGoogleAsync()
    {
        _ = 1;
        try
        {
            return await LinkWithCredentialAsync(await _googleAuth.Value.GetCredentialAsync(ViewController));
        }
        catch (NSErrorException ex)
        {
            _googleAuth.Value.SignOut();
            throw GetFirebaseAuthException(ex);
        }
    }

    private async Task<IFirebaseUser> LinkWithCredentialAsync(AuthCredential credential)
    {
        AuthDataResult authDataResult = await _firebaseAuth.CurrentUser.LinkAsync(credential);
        return authDataResult.User.ToAbstract(authDataResult.AdditionalUserInfo);
    }

    public Task SignOutAsync()
    {
        _googleAuth.Value.SignOut();
        return Task.CompletedTask;
    }
}
#if false // Decompilation log
'267' items in cache
------------------
Resolve: 'System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Found single assembly: 'System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.dll'
------------------
Resolve: 'Microsoft.iOS, Version=1*******, Culture=neutral, PublicKeyToken=84e04ff9cfb79065'
Found single assembly: 'Microsoft.iOS, Version=1*******, Culture=neutral, PublicKeyToken=84e04ff9cfb79065'
Load from: '/Users/<USER>/.nuget/packages/microsoft.ios.ref.net8.0_18.0/18.0.8324/ref/net8.0/Microsoft.iOS.dll'
------------------
Resolve: 'Firebase.Auth, Version=11.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Firebase.Auth, Version=11.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/adame.firebase.ios.auth/11.0.0/lib/net6.0-ios16.1/Firebase.Auth.dll'
------------------
Resolve: 'System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Found single assembly: 'System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.InteropServices.dll'
------------------
Resolve: 'Google.SignIn, Version=*******, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Google.SignIn, Version=*******, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/adame.google.ios.signin/8.0.0/lib/net6.0-ios16.1/Google.SignIn.dll'
------------------
Resolve: 'Plugin.Firebase.Auth, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Plugin.Firebase.Auth, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/plugin.firebase.auth/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Auth.dll'
------------------
Resolve: 'Plugin.Firebase.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'Plugin.Firebase.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null'
Load from: '/Users/<USER>/.nuget/packages/plugin.firebase.core/3.1.1/lib/net8.0-ios18.0/Plugin.Firebase.Core.dll'
------------------
Resolve: 'System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=null'
Found single assembly: 'System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'
Load from: '/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.14/ref/net8.0/System.Runtime.CompilerServices.Unsafe.dll'
#endif
