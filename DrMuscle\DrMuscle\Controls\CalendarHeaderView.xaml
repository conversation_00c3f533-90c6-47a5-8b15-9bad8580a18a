﻿<?xml version="1.0" encoding="UTF-8" ?>
   <DataTemplate
    x:Class="DrMuscle.Controls.CalendarHeaderView"
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <Grid
        Margin="0,2"
        Padding="0"
        HorizontalOptions="FillAndExpand"
        IsVisible="{Binding ShowMonthPicker}"
        VerticalOptions="Start">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="1*" />
            <ColumnDefinition Width="3*" />
            <ColumnDefinition Width="1*" />
        </Grid.ColumnDefinitions>

        <Frame
            Grid.Column="0"
            Padding="0"
            BackgroundColor="White"
            
            CornerRadius="18"
            HasShadow="False"
            HeightRequest="36"
            HorizontalOptions="CenterAndExpand"
            VerticalOptions="Center"
            WidthRequest="36">
            <Image
                WidthRequest="30"
                HeightRequest="30"
                Source="calArrow.png"
                Rotation="180"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                InputTransparent="True"
                 />

            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding PrevMonthCommand}" />
            </Frame.GestureRecognizers>
        </Frame>

        <Label
            Grid.Column="1"
            FontAttributes="Bold"
            FontSize="Medium"
            HorizontalOptions="Center"
            TextColor="{Binding MonthLabelColor}"
            VerticalOptions="Center">
            <Label.FormattedText>
                <FormattedString>
                    <Span Text="{Binding MonthText, Mode=TwoWay}" />
                    <Span Text=", " />
                    <Span Text="{Binding Year, Mode=TwoWay}" />
                </FormattedString>
            </Label.FormattedText>
        </Label>

        <Frame
            Grid.Column="2"
            Padding="0"
            BackgroundColor="White"
            
            CornerRadius="18"
            HasShadow="False"
            HeightRequest="36"
            HorizontalOptions="CenterAndExpand"
            VerticalOptions="CenterAndExpand"
            WidthRequest="36">
            <Image
                WidthRequest="30"
                HeightRequest="30"
                Source="calArrow.png"

                HorizontalOptions="Center"
                VerticalOptions="Center"
                InputTransparent="True"
                 />


            <Frame.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding NextMonthCommand}" />
            </Frame.GestureRecognizers>
        </Frame>
    </Grid>
</DataTemplate>