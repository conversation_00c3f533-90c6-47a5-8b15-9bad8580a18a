﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.LiftedCell">
    <Grid
    VerticalOptions="FillAndExpand"
    HorizontalOptions="FillAndExpand"
        Margin="0"
    Padding="10,20,10,0">
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition
            Width="*" />
            <ColumnDefinition
            Width="*" />
            <ColumnDefinition
            Width="*" />
        </Grid.ColumnDefinitions>
        <StackLayout
        Grid.Row="0"
        HorizontalOptions="FillAndExpand"
        Grid.Column="0">
            <Image
            Source="bodyweight.png"
            Aspect="AspectFit"
            HeightRequest="32"
            HorizontalOptions="CenterAndExpand" />
            <Label
            Text="{Binding LbsLifted}"
            x:Name="LblBodyweight"
            FontAttributes="Bold"
FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            TextColor="Black" />
            <Label
            Text="{Binding LbsLiftedText}"
            x:Name="LblBodyweightText"
            FontSize="17"
            LineHeight="{OnPlatform Android='1.3'}" 
            TextColor="Black"
            HorizontalOptions="CenterAndExpand"
            HorizontalTextAlignment="Center" />
            <StackLayout.GestureRecognizers>
                <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
            </StackLayout.GestureRecognizers>
        </StackLayout>
        <StackLayout
        Grid.Row="0"
        HorizontalOptions="FillAndExpand"
        Grid.Column="1">
            <Image
            Source="restrecovery.png"
            Aspect="AspectFit"
            HeightRequest="32"
            HorizontalOptions="CenterAndExpand" />
            <Label
            Text="{Binding SinceTime}"
            FontAttributes="Bold"
FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center"
            TextColor="Black" />
            <Label
            Text="{Binding LastWorkoutText}"
            FontSize="17"
            LineHeight="{OnPlatform Android='1.3'}" 
            TextColor="Black"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center" />
        </StackLayout>
        <StackLayout
        Grid.Row="0"
        HorizontalOptions="FillAndExpand"
        Grid.Column="2">

            <ffimageloading:CachedImage
            HorizontalOptions="Center"
            WidthRequest="20"
            HeightRequest="32"
            Source="orange2.png"
            Aspect="AspectFit"
                ErrorPlaceholder="backgroundblack.png">
                <ffimageloading:CachedImage.Triggers>
                    <DataTrigger
                    TargetType="ffimageloading:CachedImage"
                    Binding="{Binding TrainRest}"
                    Value="Train">
                        <Setter
                        Property="Source"
                        Value="green.png" />
                    </DataTrigger>
                    <DataTrigger
                    TargetType="ffimageloading:CachedImage"
                    Binding="{Binding TrainRest}"
                    Value="Rest">
                        <Setter
                        Property="Source"
                        Value="orange2.png" />
                    </DataTrigger>
                </ffimageloading:CachedImage.Triggers>
            </ffimageloading:CachedImage>
            <Label
            Text="{Binding TrainRest}"
            TextColor="{Binding StrengthTextColor}"
            FontAttributes="Bold"
FontSize="17"
            Style="{StaticResource LabelStyle}"
            HorizontalOptions="Center"
            HorizontalTextAlignment="Center" />

            <Label
            Text="{Binding TrainRestText}"
            FontSize="17"
            LineHeight="{OnPlatform Android='1.3'}" 
            TextColor="Black"
            HorizontalOptions="CenterAndExpand"
            HorizontalTextAlignment="Center" />
        </StackLayout>


    </Grid>
</ContentView>
