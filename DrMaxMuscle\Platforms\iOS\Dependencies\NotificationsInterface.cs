﻿using System;
using DrMaxMuscle.Dependencies;
using UIKit;


namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class NotificationsInterface : INotificationsInterface
    {
        public NotificationsInterface()
        {
        }

        public bool registeredForNotifications()
        {
            UIUserNotificationType types = UIApplication.SharedApplication.CurrentUserNotificationSettings.Types;
            if (types.HasFlag(UIUserNotificationType.Alert))
            {
                return true;
            }
            return false;
        }
    }
}

