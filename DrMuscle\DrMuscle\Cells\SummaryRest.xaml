﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
        xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    x:Class="DrMuscle.Cells.SummaryRest">
    <ViewCell.View>
        <controls:CustomFrame
                            x:Name="WeightProgress2"
                            Margin="10,0,10,10"
                            Padding="10,10,10,10"
                            CornerRadius="12"
                            HasShadow="False">
            <StackLayout Spacing="0">
                <Label
                x:Name="LblAnswer"
                FontSize="Medium"
                Text="{Binding Question}"
                IsVisible="true"
                Font="Bold,20"
                Style="{StaticResource LabelStyle}"
                TextColor="Black"
         Padding="10,10,0,5"
                Margin="0" />
        <Grid
            VerticalOptions="FillAndExpand"
            HorizontalOptions="FillAndExpand"
            Padding="10,15,10,15">
            <Grid.Margin>
                <OnPlatform
                    x:TypeArguments="Thickness"
                    iOS="0,0,0,0"
                    Android="0" />
            </Grid.Margin>
            <Grid.RowDefinitions>
                <RowDefinition
                    Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition
                    Width="*" />
                <ColumnDefinition
                    Width="*" />
                <ColumnDefinition
                    Width="*" />
            </Grid.ColumnDefinitions>
            
            <!--<StackLayout
                Grid.Row="0"
                HorizontalOptions="FillAndExpand"
                Grid.Column="1">
                <Image
                    Source="history.png"
                    Aspect="AspectFit"
                    HeightRequest="32"
                    HorizontalOptions="CenterAndExpand" />
                <Label
                    Text="{Binding SinceTime}"
                    Font="Bold,17"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    TextColor="Black" />
                <Label
                    Text="{Binding LastWorkoutText}"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center" />
            </StackLayout>-->
            <StackLayout
                Grid.Row="0"
                HorizontalOptions="FillAndExpand"
                Grid.Column="0">
                <Image
                    Source="nextworkout.png"
                    Aspect="AspectFit"
                    HeightRequest="32"
                    HorizontalOptions="CenterAndExpand" />
                <Label
                    Text="{Binding LbsLiftedText}"
                    x:Name="LblBodyweight"
                    Font="Bold,17"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    TextColor="Black" />
                <Label
                    Text=""
                    FontSize="17"
                    TextColor="#AA000000"
                    HorizontalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center" />
                </StackLayout>
            <StackLayout
                Grid.Row="0"
                HorizontalOptions="FillAndExpand"
                Grid.Column="1">
                <Image
                    Source="RestRecovery.png"
                    Aspect="AspectFit"
                    HeightRequest="32"
                    HorizontalOptions="CenterAndExpand" />
                <Label
                    Text="{Binding SinceTime}"
                    Font="Bold,17"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    TextColor="Black" />
                <Label
                    Text="{Binding LastWorkoutText}"
                    FontSize="17"
                    TextColor="#AA000000"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center" />
            </StackLayout>
            <!--<StackLayout
                Grid.Row="0"
                HorizontalOptions="FillAndExpand"
                Grid.Column="2">

                <ffimageloading:CachedImage
                    HorizontalOptions="Center"
                    WidthRequest="20"
                    HeightRequest="32"
                    Source="orange2.png"
                    Aspect="AspectFit">
                    <ffimageloading:CachedImage.Triggers>
                        <DataTrigger
                            TargetType="ffimageloading:CachedImage"
                            Binding="{Binding TrainRest}"
                            Value="Train">
                            <Setter
                                Property="Source"
                                Value="green.png" />
                        </DataTrigger>
                        <DataTrigger
                            TargetType="ffimageloading:CachedImage"
                            Binding="{Binding TrainRest}"
                            Value="Rest">
                            <Setter
                                Property="Source"
                                Value="orange2.png" />
                        </DataTrigger>
                    </ffimageloading:CachedImage.Triggers>
                </ffimageloading:CachedImage>
                <Label
                    Text="{Binding TrainRest}"
                    TextColor="{Binding StrengthTextColor}"
                    Font="Bold,17"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center" />

                <Label
                    Text="{Binding TrainRestText}"
                    Style="{StaticResource LabelStyle}"
                    HorizontalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center" />
            </StackLayout>-->
             <StackLayout
                    Grid.Row="0"
                    IsVisible="{Binding IsLastVisible}"
                    HorizontalOptions="FillAndExpand"
                    Grid.Column="2">
                    <Image
                        Source="WorkoutDone.png"
                        Aspect="AspectFit"
                        HeightRequest="32"
                        HorizontalOptions="CenterAndExpand" />
                    <Label
                        Text="{Binding LevelUpMessage}"
                        Font="Bold,17"
                        Style="{StaticResource LabelStyle}"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        TextColor="Black"/>
                    <Label
                        Text="{Binding LevelUpText}"
                        FontSize="17"
                        TextColor="#AA000000"
                        HorizontalOptions="CenterAndExpand"
                        HorizontalTextAlignment="Center" />
                </StackLayout>

        </Grid>
            </StackLayout>
            </controls:CustomFrame>
        </ViewCell.View>
</ViewCell>
