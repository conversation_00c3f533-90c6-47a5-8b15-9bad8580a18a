# Email/Password Authentication Implementation Guide

**Target platforms:** watchOS app (current Swift/SwiftUI code-base) and future web portal  
**Goal:** Reuse the existing "legacy" OAuth/token flow so both Apple-ID and email+password logins hit the same backend and keep client logic minimal.

## 1. Current Backend Contract

Based on analysis of `backend-files.md` and iOS app implementation:

### Endpoint
- **URL:** `POST /token` (form-urlencoded)
- **Content-Type:** `application/x-www-form-urlencoded`

### Required Parameters (order must be maintained)
```
grant_type accesstoken provider email name bodyweight massunit userid
```

### Email/Password Mode (used in mobile app)
```
grant_type=password
provider=password
accesstoken=<user_password>  // backend ignores this field
userid=                      // empty string
email=<user_email>
name=                        // ignored for login
bodyweight=                  // empty
massunit=                    // empty
```

### Response Format
- **Success:** `LoginSuccessResult { access_token, expires_in, userName ... }`
- **Errors:** Follow OAuth spec format with `invalid_grant`, `error_description`

## 2. Watch-side Data Model Changes

### a. Extend LoginViewModel
```swift
enum AuthenticationMode {
    case apple
    case emailPassword
}

@Published var authMode: AuthenticationMode = .apple
@Published var email: String = ""
@Published var password: String = ""
```

### b. Add EmailLoginSheet Component
- Two input fields: email and password
- "Log In" button
- Error display area
- "Back to Apple Sign In" option

### c. Credential Storage
- Store credentials **only** in keychain (no UserDefaults)
- Use separate keychain entries for email vs Apple credentials
- Reuse existing `UserInfosModel` & `AuthenticationManager.storeAuthState()`

## 3. AuthenticationManager Extensions

### New Methods
```swift
/// Signs in with email and password using OAuth token endpoint
func signInWithEmail(email: String, password: String) async throws -> Void {
    print("[AuthManager] Starting email/password sign in")
    
    // Clear any previous errors
    self.authError = nil
    
    do {
        let loginResult = try await DrMuscleAPIClient.shared.signInWithEmail(
            email: email,
            password: password
        )
        
        // Create UserInfosModel from login result
        let userInfo = UserInfosModel(
            id: email, // Use email as user ID for email logins
            email: loginResult.userName ?? email,
            token: loginResult.accessToken,
            firstName: nil, // Will be populated from GetUserInfo if needed
            lastName: nil
        )
        
        // Store authentication state
        self.storeAuthState(userInfo: userInfo)
        print("[AuthManager] Email authentication successful")
        
    } catch {
        print("[AuthManager] Email authentication failed: \(error)")
        
        // Map common errors to user-friendly messages
        if let apiError = error as? APIError {
            switch apiError {
            case .serverError(let message):
                if message.lowercased().contains("invalid_grant") {
                    self.authError = "Invalid email or password. Please try again."
                } else {
                    self.authError = "Sign in failed: \(message)"
                }
            case .httpError(let statusCode):
                switch statusCode {
                case 401:
                    self.authError = "Invalid email or password. Please try again."
                case 500...599:
                    self.authError = "Server error. Please try again later."
                default:
                    self.authError = "Connection error (Code: \(statusCode))"
                }
            default:
                self.authError = "Sign in failed. Please check your connection."
            }
        } else {
            self.authError = "An unexpected error occurred."
        }
    }
}
```

### Error Handling
- Clear `authError` on sign-in start (same as Apple flow)
- Map common failure codes:
  - `invalid_grant` → "Invalid email or password"
  - HTTP 401 → "Invalid email or password"
  - HTTP 5xx → "Server error. Please try again later."

### Credential Caching
- If first email login succeeds, cache email for future Apple-ID linking attempts
- Use same key format: `"AppleSignIn_<email>"` for consistency

## 4. DrMuscleAPIClient API Extension

### New Method
```swift
/// Signs in with email and password using the OAuth token endpoint
/// 
/// Note: This method uses the same endpoint as Apple/Google Sign In for compatibility.
/// The backend distinguishes email/password by provider=password.
///
/// - Parameters:
///   - email: User's email address
///   - password: User's password
/// - Returns: Login success result with access token
func signInWithEmail(email: String, password: String) async throws -> LoginSuccessResult {
    // Use exact parameter order as other authentication methods
    let parameters = [
        "grant_type": "password",        // Indicates password flow
        "accesstoken": password,         // Backend may ignore but required for compatibility
        "provider": "password",          // Backend uses this to identify auth type
        "email": email,
        "name": "",                      // Empty for login requests
        "bodyweight": "",               // Empty for login requests
        "massunit": "",                 // Empty for login requests
        "userid": ""                    // Empty for email/password flow
    ]
    
    print("[DrMuscleAPI] === Email/Password Sign-in Request ===")
    print("[DrMuscleAPI] Email: \(email)")
    print("[DrMuscleAPI] Using OAuth endpoint with grant_type=password")
    print("[DrMuscleAPI] =======================================")
    
    do {
        let response: LoginSuccessResult = try await postFormUrlEncoded(
            route: "token", 
            parameters: parameters
        )
        print("[DrMuscleAPI] Email sign-in successful, access token received")
        
        // Store the token for future API calls
        self.token = response.accessToken
        
        return response
    } catch {
        print("[DrMuscleAPI] Email sign-in failed with error: \(error)")
        throw error
    }
}
```

### Implementation Notes
- Keep identical logging, Authorization header, and error handling as Apple method
- Reuse existing `postFormUrlEncoded` method for consistency
- Maintain parameter order for backend compatibility

## 5. UI/UX Implementation

### LoginView Updates
- Default shows "Sign in with Apple" button (primary)
- Add small "Use email instead" link below
- When tapped, present `EmailLoginSheet`

### EmailLoginSheet Design
```swift
struct EmailLoginSheet: View {
    @Binding var email: String
    @Binding var password: String
    @Binding var isLoading: Bool
    @Binding var errorMessage: String?
    
    let onSignIn: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Sign in with Email")
                .font(.headline)
            
            TextField("Email", text: $email)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
            
            SecureField("Password", text: $password)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            Button("Sign In") {
                onSignIn()
            }
            .disabled(email.isEmpty || password.isEmpty || isLoading)
            
            Button("Use Apple Sign In instead") {
                onCancel()
            }
            .foregroundColor(.secondary)
        }
        .padding()
    }
}
```

### DiagnosticLoginView Updates
- Surface email/password parameters for testing verification
- Show which authentication mode is active
- Display parameter values being sent to backend

### Flow
1. When email login succeeds, automatically proceed to workout list (same as Apple flow)
2. Store credentials securely in keychain
3. Auto-fill email on subsequent launches if previously used

## 6. Testing Strategy (TDD)

### AuthenticationIntegrationTests.swift
Add new test cases:
```swift
func testEmailLoginSuccess() async throws {
    // Test successful email/password authentication
    // Verify correct parameters sent to backend
    // Verify successful token storage
}

func testEmailLoginInvalidPassword() async throws {
    // Test invalid credentials
    // Verify proper error mapping
    // Verify no token storage on failure
}

func testEmailLoginNetworkError() async throws {
    // Test network connectivity issues
    // Verify appropriate error messages
}
```

### APIMockTests.swift
Add fixture for email/password requests:
```swift
func testEmailPasswordParameterFormat() {
    // Verify grant_type=password format
    // Verify parameter order matches backend expectations
    // Verify form encoding is correct
}
```

### Update docs/testing.md
- Document new test cases
- Add email/password testing procedures
- Include parameter verification steps

## 7. Web Frontend Alignment (Future)

### JavaScript Implementation
```javascript
async function signInWithEmail(email, password) {
    const params = new URLSearchParams({
        grant_type: 'password',
        accesstoken: password,
        provider: 'password',
        email: email,
        name: '',
        bodyweight: '',
        massunit: '',
        userid: ''
    });
    
    const response = await fetch('/token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params
    });
    
    if (!response.ok) {
        throw new Error('Authentication failed');
    }
    
    return await response.json();
}
```

### Security Considerations
- Ensure CORS is properly configured for web requests
- Store JWT in secure httpOnly cookie for web
- Implement refresh token mechanism if backend supports it

## 8. Security & Edge Cases

### Security Measures
- **TLS-only:** API already enforces HTTPS
- **Rate limiting:** Backend responsibility - document expected behavior
- **Password requirements:** Follow backend validation rules
- **Secure storage:** Keychain for watch, secure cookies for web

### Edge Cases
- **Account linking:** If user has both Apple ID and email accounts, provide migration path
- **Password reset:** Direct users to web portal or mobile app
- **Account lockout:** Display appropriate messaging for locked accounts

### Error Scenarios
- Network connectivity issues
- Invalid email format
- Account not found vs invalid password (security consideration)
- Server maintenance/downtime

## 9. Migration & Compatibility

### Backward Compatibility
- Existing Apple Sign In flow remains unchanged
- Shared authentication state management
- Unified token handling and refresh logic

### Future Endpoint Migration
- If backend introduces proper `/login` endpoint, maintain wrapper for compatibility
- Document migration path for new endpoint adoption
- Ensure consistent error response format

## 10. Deliverables

### Code Changes
- [ ] Updated `DrMuscleAPIClient.swift` with `signInWithEmail` method
- [ ] Extended `AuthenticationManager.swift` with email authentication
- [ ] New `EmailLoginSheet.swift` UI component
- [ ] Updated `LoginView.swift` with mode switching
- [ ] Enhanced `DiagnosticLoginView.swift` for testing

### Testing
- [ ] New test files: `EmailAuthenticationTests.swift`
- [ ] Updated `AuthenticationIntegrationTests.swift`
- [ ] Updated `APIMockTests.swift` with email fixtures
- [ ] Updated `docs/testing.md` with new procedures

### Documentation
- [ ] README section: "Email/Password Authentication"
- [ ] API documentation updates
- [ ] Security considerations document
- [ ] Web implementation guide

### Validation
- [ ] Email/password login works on physical watch
- [ ] Error handling provides clear user feedback
- [ ] Backend compatibility confirmed with existing endpoints
- [ ] No regression in Apple Sign In functionality

## Implementation Timeline

1. **Phase 1:** Backend endpoint verification and testing (1 day)
2. **Phase 2:** Watch app implementation - core authentication logic (2 days)
3. **Phase 3:** UI implementation and integration (1 day)
4. **Phase 4:** Testing and validation (1 day)
5. **Phase 5:** Documentation and deployment (1 day)

**Total estimated time:** 6 days

## Notes

- This implementation maintains full compatibility with existing backend infrastructure
- No changes required to backend code - uses existing OAuth/token endpoint
- Provides foundation for future web portal authentication
- Follows established patterns from Apple Sign In implementation
- Maintains security best practices throughout
``` 