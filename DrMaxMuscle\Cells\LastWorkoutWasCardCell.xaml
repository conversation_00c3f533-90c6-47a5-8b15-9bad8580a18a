<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Cells.LastWorkoutWasCardCell"
             xmlns:controls="clr-namespace:DrMaxMuscle.Controls">
    <controls:CustomFrame
        x:Name="WeightProgress2"
        Margin="10,10,10,0"
        Padding="0,10,10,10"
        CornerRadius="12"
        BorderColor="Transparent"
        HasShadow="true">
        <controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" /> 
</controls:CustomFrame.Shadow>
        <controls:CustomFrame.Triggers>
            <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="True" TargetType="Frame">
                <Setter Property="Margin" Value="10,11,10,10" />
            </DataTrigger>
            <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="False" TargetType="Frame">
                <Setter Property="Margin" Value="10,1,10,0" />
            </DataTrigger>
            <DataTrigger Binding="{Binding StrengthImage}" Value="chekedgreen.png" TargetType="Frame">
                <Setter Property="Margin" Value="10,11,10,0" />
            </DataTrigger>
        </controls:CustomFrame.Triggers>
        <StackLayout Padding="10,15,10,15">

            
            <Grid
                                    Margin="0,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition
                                            Height="*" />
                    <RowDefinition
                                            Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition
                                            Width="40" />
                    <ColumnDefinition
                                            Width="*" />
                </Grid.ColumnDefinitions>

                <Image
                                        Source="{Binding StrengthImage}"
                                        Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
                                        Grid.Row="0"
                                        WidthRequest="27"
                                        VerticalOptions="Start"
                                        HeightRequest="27" />
                <StackLayout
                                        Grid.Column="1"
                                        Grid.Row="0"
                                        Grid.RowSpan="2">
                    <Label
                                            x:Name="LblStrengthUp"
                                            Text="{Binding Question}"
                                            Margin="0,-8,0,9"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="19" />
                    <Label
                                            x:Name="LblStrengthUpText"
                                            Margin="0,-2,0,0"
                                            Text="{Binding Part1}"
                                            FontSize="17"
                                            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                                            TextColor="#AA000000" />
                </StackLayout>
            </Grid>

        </StackLayout>
    </controls:CustomFrame>
</ContentView>
