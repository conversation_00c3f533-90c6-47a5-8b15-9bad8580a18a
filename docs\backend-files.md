(Files content cropped to 300k characters, download full ingest to see more)
================================================
FILE: DrMaxMuscle2.sln
================================================
﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27703.2042
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMaxMuscleDataLayer", "DrMaxMuscleDataLayer\DrMaxMuscleDataLayer.csproj", "{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMaxMuscleAdminWeb", "DrMaxMuscleAdminWeb\DrMaxMuscleAdminWeb.csproj", "{D313CF64-5335-48E7-A318-26AFB2932C65}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMaxMuscleWebApi", "DrMaxMuscleWebApi\DrMaxMuscleWebApi.csproj", "{EBFD5672-65B7-458F-A0AA-3B149769912F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWeb", "DrMuscleWeb\DrMuscleWeb.csproj", "{70E664E9-23FE-48F1-8061-D71A178DFD87}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscle.Droid", "DrMuscle\DrMuscle.Droid\DrMuscle.Droid.csproj", "{B312BAC9-C489-4133-A4C5-7952A125BBC5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscle.iOS", "DrMuscle\DrMuscle.iOS\DrMuscle.iOS.csproj", "{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWebApiSharedModel", "DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel\DrMuscleWebApiSharedModel.csproj", "{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{7E827725-41CB-4B61-B42A-808AE7F1E35F}"
	ProjectSection(SolutionItems) = preProject
		.vs\config\applicationhost.config = .vs\config\applicationhost.config
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CATest", "CATest\CATest.csproj", "{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWebFeaturedProgram", "DrMuscleWebFeaturedProgram\DrMuscleWebFeaturedProgram.csproj", "{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWatch.WatchOSApp", "DrMuscleWatch\DrMuscleWatch.WatchOSApp\DrMuscleWatch.WatchOSApp.csproj", "{15C97B55-2876-44EC-B41A-DC9E32F9CF52}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWatch.WatchOSExtension", "DrMuscleWatch\DrMuscleWatch.WatchOSExtension\DrMuscleWatch.WatchOSExtension.csproj", "{746A5706-CF13-47CB-93E1-A18E38A05383}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscleWebProgram", "DrMuscleWebProgram\DrMuscleWebProgram.csproj", "{6D0C3C18-F613-453C-800C-2A64BED2BC5D}"
EndProject
Project("{9344BDBB-3E7F-41FC-A0DD-8665D75EE146}") = "DrMaxMuscleDb", "DrMaxMuscleDb\DrMaxMuscleDb.sqlproj", "{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrMuscle", "DrMuscle\DrMuscle\DrMuscle.csproj", "{BE1EE65E-49AA-451B-843C-40477246DB1B}"
EndProject
Project("{9344BDBB-3E7F-41FC-A0DD-8665D75EE146}") = "DrMuscleWear", "DrMuscleWear\DrMuscleWear\DrMuscleWear.csproj", "{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Ad-Hoc|Any CPU = Ad-Hoc|Any CPU
		Ad-Hoc|ARM = Ad-Hoc|ARM
		Ad-Hoc|iPhone = Ad-Hoc|iPhone
		Ad-Hoc|iPhoneSimulator = Ad-Hoc|iPhoneSimulator
		Ad-Hoc|x64 = Ad-Hoc|x64
		Ad-Hoc|x86 = Ad-Hoc|x86
		AppStore|Any CPU = AppStore|Any CPU
		AppStore|ARM = AppStore|ARM
		AppStore|iPhone = AppStore|iPhone
		AppStore|iPhoneSimulator = AppStore|iPhoneSimulator
		AppStore|x64 = AppStore|x64
		AppStore|x86 = AppStore|x86
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM = Debug|ARM
		Debug|iPhone = Debug|iPhone
		Debug|iPhoneSimulator = Debug|iPhoneSimulator
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|ARM = Release|ARM
		Release|iPhone = Release|iPhone
		Release|iPhoneSimulator = Release|iPhoneSimulator
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|ARM.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|iPhone.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|x64.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|x64.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|x86.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.AppStore|x86.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|ARM.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|iPhone.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|x64.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Debug|x86.Build.0 = Debug|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|ARM.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|ARM.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|iPhone.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|iPhone.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|x64.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|x64.Build.0 = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|x86.ActiveCfg = Release|Any CPU
		{1F82F559-BC4A-4A83-B0DE-7EA95CE611C7}.Release|x86.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|ARM.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|iPhone.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|x64.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|x64.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|x86.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.AppStore|x86.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|ARM.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|iPhone.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|x64.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Debug|x86.Build.0 = Debug|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|Any CPU.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|ARM.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|ARM.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|iPhone.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|iPhone.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|x64.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|x64.Build.0 = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|x86.ActiveCfg = Release|Any CPU
		{D313CF64-5335-48E7-A318-26AFB2932C65}.Release|x86.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|ARM.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|iPhone.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|x64.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|x64.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|x86.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.AppStore|x86.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|ARM.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|iPhone.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|x64.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Debug|x86.Build.0 = Debug|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|ARM.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|ARM.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|iPhone.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|iPhone.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|x64.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|x64.Build.0 = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|x86.ActiveCfg = Release|Any CPU
		{EBFD5672-65B7-458F-A0AA-3B149769912F}.Release|x86.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|ARM.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|iPhone.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|x64.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|x64.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|x86.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.AppStore|x86.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|ARM.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|iPhone.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|x64.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|x64.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|x86.ActiveCfg = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Debug|x86.Build.0 = Debug|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|Any CPU.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|ARM.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|ARM.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|iPhone.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|iPhone.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|x64.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|x64.Build.0 = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|x86.ActiveCfg = Release|Any CPU
		{70E664E9-23FE-48F1-8061-D71A178DFD87}.Release|x86.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|Any CPU.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|ARM.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhone.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x64.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Ad-Hoc|x86.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|Any CPU.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|ARM.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|ARM.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhone.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhone.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x64.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x64.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x64.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x86.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x86.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.AppStore|x86.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|ARM.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|ARM.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhone.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhone.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|iPhoneSimulator.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x64.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x64.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x86.Build.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Debug|x86.Deploy.0 = Debug|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|ARM.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|ARM.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|ARM.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhone.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhone.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhone.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x64.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x64.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x64.Deploy.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x86.ActiveCfg = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x86.Build.0 = Release|Any CPU
		{B312BAC9-C489-4133-A4C5-7952A125BBC5}.Release|x86.Deploy.0 = Release|Any CPU
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|Any CPU.ActiveCfg = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|ARM.ActiveCfg = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|iPhone.ActiveCfg = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|iPhone.Build.0 = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Ad-Hoc|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|iPhoneSimulator.Build.0 = Ad-Hoc|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|x64.ActiveCfg = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Ad-Hoc|x86.ActiveCfg = Ad-Hoc|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|Any CPU.ActiveCfg = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|ARM.ActiveCfg = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|iPhone.ActiveCfg = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|iPhone.Build.0 = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|iPhoneSimulator.ActiveCfg = AppStore|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|iPhoneSimulator.Build.0 = AppStore|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|x64.ActiveCfg = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.AppStore|x86.ActiveCfg = AppStore|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|Any CPU.ActiveCfg = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|ARM.ActiveCfg = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|iPhone.ActiveCfg = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|iPhone.Build.0 = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|iPhoneSimulator.ActiveCfg = Debug|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|iPhoneSimulator.Build.0 = Debug|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|x64.ActiveCfg = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Debug|x86.ActiveCfg = Debug|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|Any CPU.ActiveCfg = Release|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|ARM.ActiveCfg = Release|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|iPhone.ActiveCfg = Release|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|iPhone.Build.0 = Release|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|iPhoneSimulator.ActiveCfg = Release|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|iPhoneSimulator.Build.0 = Release|iPhoneSimulator
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|x64.ActiveCfg = Release|iPhone
		{E3261BAB-F82E-4BF7-AFDC-64F3EB488B78}.Release|x86.ActiveCfg = Release|iPhone
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|ARM.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|iPhone.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|x64.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|x64.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|x86.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.AppStore|x86.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|ARM.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|iPhone.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|x64.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Debug|x86.Build.0 = Debug|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|ARM.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|ARM.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|iPhone.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|iPhone.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|x64.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|x64.Build.0 = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|x86.ActiveCfg = Release|Any CPU
		{B4E17091-29B9-41E6-8DE5-7F6CD8C13E51}.Release|x86.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|ARM.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|iPhone.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|x64.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|x64.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|x86.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.AppStore|x86.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|ARM.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|iPhone.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|x64.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Debug|x86.Build.0 = Debug|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|ARM.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|ARM.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|iPhone.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|iPhone.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|x64.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|x64.Build.0 = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|x86.ActiveCfg = Release|Any CPU
		{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}.Release|x86.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|ARM.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|iPhone.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|x64.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|x64.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|x86.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.AppStore|x86.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|ARM.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|iPhone.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|x64.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Debug|x86.Build.0 = Debug|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|ARM.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|ARM.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|iPhone.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|iPhone.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|x64.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|x64.Build.0 = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|x86.ActiveCfg = Release|Any CPU
		{9472C1A6-D4D8-439C-BDB5-E4EE9A927F4D}.Release|x86.Build.0 = Release|Any CPU
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|Any CPU.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|Any CPU.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|ARM.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|ARM.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|iPhone.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|iPhone.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|x64.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|x64.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|x86.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Ad-Hoc|x86.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|Any CPU.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|Any CPU.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|ARM.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|ARM.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|iPhone.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|iPhone.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|iPhoneSimulator.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|iPhoneSimulator.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|x64.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|x64.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|x86.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.AppStore|x86.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|Any CPU.ActiveCfg = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|Any CPU.Build.0 = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|ARM.ActiveCfg = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|ARM.Build.0 = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|iPhone.ActiveCfg = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|iPhone.Build.0 = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|iPhoneSimulator.ActiveCfg = Debug|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|iPhoneSimulator.Build.0 = Debug|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|x64.ActiveCfg = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|x64.Build.0 = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|x86.ActiveCfg = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Debug|x86.Build.0 = Debug|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|Any CPU.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|Any CPU.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|ARM.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|ARM.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|iPhone.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|iPhone.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|iPhoneSimulator.ActiveCfg = Release|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|iPhoneSimulator.Build.0 = Release|iPhoneSimulator
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|x64.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|x64.Build.0 = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|x86.ActiveCfg = Release|iPhone
		{15C97B55-2876-44EC-B41A-DC9E32F9CF52}.Release|x86.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|Any CPU.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|Any CPU.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|ARM.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|ARM.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|iPhone.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|iPhone.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|x64.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|x64.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|x86.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Ad-Hoc|x86.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|Any CPU.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|Any CPU.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|ARM.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|ARM.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|iPhone.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|iPhone.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|iPhoneSimulator.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|iPhoneSimulator.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|x64.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|x64.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|x86.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.AppStore|x86.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|Any CPU.ActiveCfg = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|Any CPU.Build.0 = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|ARM.ActiveCfg = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|ARM.Build.0 = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|iPhone.ActiveCfg = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|iPhone.Build.0 = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|iPhoneSimulator.ActiveCfg = Debug|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|iPhoneSimulator.Build.0 = Debug|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|x64.ActiveCfg = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|x64.Build.0 = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|x86.ActiveCfg = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Debug|x86.Build.0 = Debug|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|Any CPU.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|Any CPU.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|ARM.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|ARM.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|iPhone.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|iPhone.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|iPhoneSimulator.ActiveCfg = Release|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|iPhoneSimulator.Build.0 = Release|iPhoneSimulator
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|x64.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|x64.Build.0 = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|x86.ActiveCfg = Release|iPhone
		{746A5706-CF13-47CB-93E1-A18E38A05383}.Release|x86.Build.0 = Release|iPhone
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|ARM.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|iPhone.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|x64.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|x64.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|x86.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.AppStore|x86.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|ARM.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|iPhone.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|x64.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Debug|x86.Build.0 = Debug|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|ARM.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|ARM.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|iPhone.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|iPhone.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|x64.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|x64.Build.0 = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|x86.ActiveCfg = Release|Any CPU
		{6D0C3C18-F613-453C-800C-2A64BED2BC5D}.Release|x86.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|Any CPU.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|ARM.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhone.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x64.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Ad-Hoc|x86.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|Any CPU.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|ARM.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|ARM.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhone.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhone.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x64.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x64.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x64.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x86.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x86.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.AppStore|x86.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|ARM.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|ARM.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhone.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhone.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|iPhoneSimulator.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x64.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x64.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x86.Build.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Debug|x86.Deploy.0 = Debug|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|Any CPU.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|ARM.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|ARM.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|ARM.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhone.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhone.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhone.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|iPhoneSimulator.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x64.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x64.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x64.Deploy.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x86.ActiveCfg = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x86.Build.0 = Release|Any CPU
		{FEB7F2AA-1F6B-4239-94E5-52E4F2DFA126}.Release|x86.Deploy.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|ARM.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|iPhone.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|x64.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|x64.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|x86.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.AppStore|x86.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|ARM.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|iPhone.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|x64.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Debug|x86.Build.0 = Debug|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|ARM.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|ARM.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|iPhone.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|iPhone.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|x64.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|x64.Build.0 = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|x86.ActiveCfg = Release|Any CPU
		{BE1EE65E-49AA-451B-843C-40477246DB1B}.Release|x86.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|Any CPU.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|Any CPU.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|ARM.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|ARM.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|iPhone.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|iPhone.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|iPhoneSimulator.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|x64.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|x64.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|x86.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Ad-Hoc|x86.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|Any CPU.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|Any CPU.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|ARM.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|ARM.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|iPhone.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|iPhone.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|iPhoneSimulator.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|x64.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|x64.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|x86.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.AppStore|x86.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|ARM.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|ARM.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|iPhone.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|x64.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Debug|x86.Build.0 = Debug|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|ARM.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|ARM.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|iPhone.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|iPhone.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|x64.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|x64.Build.0 = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|x86.ActiveCfg = Release|Any CPU
		{83AC7A0D-A34F-4D01-AB4E-10326EC903FC}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {DCF36D3D-3E0D-4A35-BE01-CEEE586725D3}
	EndGlobalSection
EndGlobal



================================================
FILE: publishingdoc.keystore
================================================
[Non-text file]


================================================
FILE: CATest/Algo.cs
================================================
﻿using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CATest
{
    public class Algo
    {
        public RecommendationModel GetRecommendation(int LastWorkoutNbSeriesForExercise,
                                                        decimal Weight0,
                                                        decimal Weight1,
                                                        decimal Weight2,
                                                        int Reps0,
                                                        int Reps1,
                                                        int Reps2
                                                        )
        {
            // Calcul du 1RM pour les deux précédent sets
            decimal OneRM0 = ComputeOneRM(Weight0, Reps0);
            decimal OneRM1 = ComputeOneRM(Weight1, Reps1);

            // Calcul du progres en % entre les deux dernier workout
            RecommendationModel result = new RecommendationModel();
            result.OneRMProgress = (OneRM0 - OneRM1) * 100 / OneRM1;

            // Calcul du nombre de reps de la recommendation
            // NOUVEAU CODE
            // En fonction du nombre de reps au dernier workout

            int nbReps = 0;

            if (Reps0 > 11)
                nbReps = 6;

            if (Reps0 == 6)
                nbReps = 9;

            if (Reps0 == 9)
                nbReps = 11;

            if (Reps0 == 11)
                nbReps = 5;

            if (Reps0 == 5)
                nbReps = 8;

            if (Reps0 == 8)
                nbReps = 10;

            if (Reps0 == 10)
                nbReps = 4;

            if (Reps0 < 5)
                nbReps = 7;

            if (Reps0 == 7)
                nbReps = 12;

            result.Reps = nbReps;

            // FIN DU NOUVEAU CODE

            // Calcul du poids (en Kg) de la recommendation
            decimal percent = (decimal)(102.78 - 2.78 * nbReps);
            decimal recommendationInKg = OneRM0 * percent / 100;
            decimal oneRMRelativeProgress = (OneRM0 - OneRM1) / OneRM0;

            // Code pour limiter le progress à entre 1.1% et 10%

            // Si le progres en % entre les deux dernier workout est inférieur à 1.1 %, on le fixe à 1.1 %

            if (oneRMRelativeProgress < (decimal)0.011)
                oneRMRelativeProgress = (decimal)0.011;

            // Si le progres en % entre les deux dernier workout est supérieur à 5 %, on le fixe à 5 %

            if (oneRMRelativeProgress > (decimal)0.05)
                oneRMRelativeProgress = (decimal)0.05;

            // FIN DU NOUVEAU CODE

            recommendationInKg = recommendationInKg + (recommendationInKg * (oneRMRelativeProgress * (decimal)0.9));
            recommendationInKg = Math.Floor(recommendationInKg);
            result.Weight = new MultiUnityWeight(recommendationInKg, WeightUnities.kg);

            // Calcul du nombre de séries de recommendation 
            // En fonction du progres réalisé entre les
            if ((double)result.OneRMProgress >= 7.5)
            {
                result.Series = LastWorkoutNbSeriesForExercise + 1;
            }
            else
            {
                if ((double)result.OneRMProgress >= 2 && (double)result.OneRMProgress < 7.5)
                {
                    result.Series = LastWorkoutNbSeriesForExercise;
                }
                else
                {
                    if ((double)result.OneRMProgress >= 0.1 && result.OneRMProgress < 2)
                    {
                        result.Series = LastWorkoutNbSeriesForExercise - 1;
                        if (result.Series == 0)
                            result.Series = 1;
                    }
                    else
                    {
                        if ((double)result.OneRMProgress < 0.1)
                        {
                            result.Series = LastWorkoutNbSeriesForExercise / 2;
                        }
                    }
                }
            }
            return result;
        }

        public decimal ComputeOneRM(decimal weight, int reps)
        {
            return (decimal)(weight / (decimal)(1.0278 - (0.0278 * reps)));
        }
    }
}



================================================
FILE: CATest/AlgoCode.txt
================================================
﻿using DrMuscleWebApiSharedModel;
using System;

namespace DrMuscleAlgo
{
    public class Algo
    {
        public RecommendationModel GetRecommendation(int LastWorkoutNbSeriesForExercise,
                                                        decimal Weight0,
                                                        decimal Weight1,
                                                        decimal Weight2,
                                                        int Reps0,
                                                        int Reps1,
                                                        int Reps2
                                                        )
        {
			// Calcul du 1RM pour les deux précédent sets
			decimal OneRM0 = ComputeOneRM(Weight0, Reps0);
			decimal OneRM1 = ComputeOneRM(Weight1, Reps1);
            
            // Calcul du progres en % entre les deux dernier workout
            RecommendationModel result = new RecommendationModel();
            result.OneRMProgress = (OneRM0 - OneRM1) * 100 / OneRM1;

            // Calcul du nombre de reps de la recommendation
            // en fonction de la moyenne du nombre de reps sur les 3 précédents workout
            int repsAverage = Convert.ToInt32(Math.Ceiling((decimal)((Reps0 + Reps1 + Reps2) / 3)));
            if (repsAverage == 0)
                repsAverage = 1;
            int nbReps = 0;
            int min = 0;
            int max = 0;
            switch (repsAverage)
            {
                case 1:
                    min = 2;
                    max = 3;
                    break;
                case 2:
                    min = 3;
                    max = 4;
                    break;
                case 3:
                    min = 6;
                    max = 8;
                    break;
                case 4:
                    min = 7;
                    max = 9;
                    break;
                case 5:
                    min = 9;
                    max = 11;
                    break;
                case 6:
                    min = 10;
                    max = 12;
                    break;
                case 7:
                case 8:
                    Random r2 = new Random();
                    int t = r2.Next(0, 1);
                    if (t == 0)
                    {
                        min = 3;
                        max = 4;
                    }
                    else
                    {
                        min = 11;
                        max = 12;
                    }
                    break;
                case 9:
                    min = 3;
                    max = 5;
                    break;
                case 10:
                    min = 4;
                    max = 6;
                    break;
                case 11:
                    min = 5;
                    max = 7;
                    break;
                case 12:
                    min = 6;
                    max = 8;
                    break;
                default:
                case 13:
                    min = 8;
                    max = 10;
                    break;
            }
            Random random = new Random();
            nbReps = random.Next(min, max);
            result.Reps = nbReps;

            // Calcul du poids (en Kg) de la recommendation
			decimal percent = (decimal)(102.78 - 2.78 * nbReps);
            decimal recommendationInKg = OneRM0 * percent / 100;
            decimal oneRMRelativeProgress = (OneRM0 - OneRM1) / OneRM0;
            recommendationInKg = recommendationInKg + (recommendationInKg * (oneRMRelativeProgress * (decimal)0.9));
            recommendationInKg = Math.Floor(recommendationInKg);
            result.Weight = new MultiUnityWeight(recommendationInKg, WeightUnities.kg);

            // Calcul du nombre de séries de recommendation 
            // En fonction du progres réalisé entre les
            if ((double)result.OneRMProgress >= 7.5)
            {
                result.Series = LastWorkoutNbSeriesForExercise + 1;
            }
            else
            {
                if ((double)result.OneRMProgress >= 2 && (double)result.OneRMProgress < 7.5)
                {
                    result.Series = LastWorkoutNbSeriesForExercise;
                }
                else
                {
                    if ((double)result.OneRMProgress >= 0.1 && result.OneRMProgress < 2)
                    {
                        result.Series = LastWorkoutNbSeriesForExercise - 1;
                        if (result.Series == 0)
                            result.Series = 1;
                    }
                    else
                    {
                        if ((double)result.OneRMProgress < 0.1)
                        {
                            result.Series = LastWorkoutNbSeriesForExercise / 2;
                        }
                    }
                }
            }
            return result;
        }

		public decimal ComputeOneRM(decimal weight, int reps)
		{
			return (decimal)(weight / (decimal)(1.0278 - (0.0278 * reps)));
		}
    }
}


================================================
FILE: CATest/App.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <connectionStrings>
    <clear />
    <!--
    <add name="DrMaxMuscleDbEntities" connectionString="metadata=res://*/DrMaxMuscleModel.csdl|res://*/DrMaxMuscleModel.ssdl|res://*/DrMaxMuscleModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=localhost;Initial Catalog=DrMaxMuscleDb;User ID=sa;Password=************;Connect Timeout=15;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultipleActiveResultSets=True;MultiSubnetFailover=True&quot;" providerName="System.Data.EntityClient" />
    <add name="AuthContext" connectionString="Data Source=localhost;Initial Catalog=DrMaxMuscleDb;User ID=sa;Password=************;Connect Timeout=15;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultipleActiveResultSets=True;MultiSubnetFailover=True" providerName="System.Data.SqlClient" />
    -->
    <add name="DrMaxMuscleDbEntities" connectionString="metadata=res://*/DrMaxMuscleModel.csdl|res://*/DrMaxMuscleModel.ssdl|res://*/DrMaxMuscleModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Server=tcp:drmuscledbserver.database.windows.net,1433;Initial Catalog=DrMuscleDB;Persist Security Info=False;User ID=drmuscle;Password=************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;&quot;" providerName="System.Data.EntityClient" />
    <add name="AuthContext" connectionString="Server=tcp:drmuscledbserver.database.windows.net,1433;Initial Catalog=DrMuscleDB;Persist Security Info=False;User ID=drmuscle;Password=************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.2.0" newVersion="4.1.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Xml.ReaderWriter" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.0.0" newVersion="4.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.Algorithms" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.0.0" newVersion="4.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.FileSystem" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IO.FileSystem.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>



================================================
FILE: CATest/CATest.csproj
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DCA36025-F01B-4CA2-A0A2-8818DECC4D2B}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>CATest</RootNamespace>
    <AssemblyName>CATest</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DrMuscleAlgo, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\DrMuscleAlgo.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DrMaxMuscleWebApi\DrMaxMuscleWebApi.csproj">
      <Project>{ebfd5672-65b7-458f-a0aa-3b149769912f}</Project>
      <Name>DrMaxMuscleWebApi</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="AlgoCode.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>


================================================
FILE: CATest/packages.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.1.3" targetFramework="net461" />
</packages>


================================================
FILE: CATest/Program.cs
================================================
﻿using DrMaxMuscleWebApi.Repository;
using DrMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;

namespace CATest
{
    class Program
    {
        public static decimal RoundDownToNearest(decimal numToRound, decimal step)
        {
            

            if (step == 0)
                return numToRound;

            //Calc the floor value of numToRound
            decimal floor = ((long)(numToRound / step)) * step;

            //round up if more than half way of step
            decimal round = floor;
            decimal remainder = numToRound - floor;
            if (remainder >= 0)
                round += step;

            return round;

        }

		public static string HashPassword(string password)
		{
			byte[] salt;
			byte[] buffer2;
			if (password == null)
			{
				throw new ArgumentNullException("password");
			}
			using (Rfc2898DeriveBytes bytes = new Rfc2898DeriveBytes(password, 0x10, 0x3e8))
			{
				salt = bytes.Salt;
				buffer2 = bytes.GetBytes(0x20);
			}
			byte[] dst = new byte[0x31];
			Buffer.BlockCopy(salt, 0, dst, 1, 0x10);
			Buffer.BlockCopy(buffer2, 0, dst, 0x11, 0x20);
			return Convert.ToBase64String(dst);
		}

        static void Main(string[] args)
        {
            RecommendationModel result = GetRecommendation(3, 20, 20, 20, 10, 9, 8);
        }

        public static RecommendationModel GetRecommendation(int LastWorkoutNbSeriesForExercise,
                                                        decimal Weight0,
                                                        decimal Weight1,
                                                        decimal Weight2,
                                                        int Reps0,
                                                        int Reps1,
                                                        int Reps2
                                                        )
        {
            // Calcul du 1RM pour les deux précédent sets
            decimal OneRM0 = ComputeOneRM(Weight0, Reps0);
            decimal OneRM1 = ComputeOneRM(Weight1, Reps1);
            decimal OneRM2 = ComputeOneRM(Weight2, Reps2);

            // Calcul du progres en % entre les deux dernier workout
            RecommendationModel result = new RecommendationModel();
            result.OneRMProgress = (OneRM0 - OneRM1) * 100 / OneRM1;

            // REPS
            // En fonction du nombre de reps au dernier workout

            int nbReps = 0;

            if (Reps0 > 11)
                nbReps = 6;

            if (Reps0 == 6)
                nbReps = 9;

            if (Reps0 == 9)
                nbReps = 11;

            if (Reps0 == 11)
                nbReps = 7;

            if (Reps0 == 7)
                nbReps = 10;

            if (Reps0 == 10)
                nbReps = 5;

            if (Reps0 < 6)
                nbReps = 8;

            if (Reps0 == 8)
                nbReps = 12;

            result.Reps = nbReps;

            // POIDS (EN KG)
            // On calcule la progression relative entre le 1RM le plus récent (OneRM0) et le précédent (OneRM1)
            decimal oneRMRelativeProgress = (OneRM0 - OneRM1) / OneRM0;

            // On limite cette progression entre 1% et 5%
            // Si le progress en % entre les deux dernier workout est inférieur à 1 %, on le fixe à 1 %

            if (oneRMRelativeProgress < (decimal)0.01)
                oneRMRelativeProgress = (decimal)0.01;

            // Si le progress en % entre les deux dernier workout est supérieur à 5 %, on le fixe à 5 %
            if (oneRMRelativeProgress > (decimal)0.05)
                oneRMRelativeProgress = (decimal)0.05;

            // On calcule l'augmentation (increase) prévue pour le 1RM aujourd'hui
            decimal OneRMIncrease = OneRM0 * oneRMRelativeProgress;

            // On calcule le 1RM prévu pour aujourd'hui (OneRM00) en additionnant le 1RM le plus récent (OneRM0) et l'augmentation (increase) prévue pour le 1RM aujourd'hui
            decimal OneRM00 = OneRM0 + OneRMIncrease;

            // On calcule le poids recommandé aujourd'hui à partir du 1RM prévu pour aujourd'hui (OneRM00)			
            // Mayhew
            decimal percent = (decimal)(52.2 + 41.9 * Math.Exp(-0.055 * nbReps));
            decimal recommendationInKg = OneRM00 * percent / 100;

            recommendationInKg = Math.Ceiling(recommendationInKg);
            result.Weight = new MultiUnityWeight(recommendationInKg, WeightUnities.kg);

            // SÉRIES			
            // En fonction du nombre de reps au dernier workout (ajustement +1 série aux ~3 workouts)
            if (result.Reps == 5 || result.Reps == 6 || result.Reps == 7)
            {
                result.Series = LastWorkoutNbSeriesForExercise + 1;
            }
            else
            {
                result.Series = LastWorkoutNbSeriesForExercise;
            }
            // Fin en fonction du nombre de reps au dernier workout (ajustement +1 série aux ~3 workouts)


            // Ajustement des séries en fonction du progres réalisé entre les 2 derniers workouts
            if ((double)result.OneRMProgress >= 4)
            {
                result.Series = result.Series + 1;
            }
            else
            {
                if ((double)result.OneRMProgress >= 0 && (double)result.OneRMProgress < 4)
                {
                    result.Series = result.Series;
                }
                else
                {
                    if ((double)result.OneRMProgress >= 0 && result.OneRMProgress < 2)
                    {
                        result.Series = result.Series;
                    }
                    else
                    {
                        if ((double)result.OneRMProgress < 0)
                        {
                            result.Series = LastWorkoutNbSeriesForExercise / 2;

                            // Deload: si le 1RM a baissé, je mets 90%. S'il a baissé depuis 2 workouts, je mets 105%			
                            if (OneRM0 < OneRM2 && OneRM1 < OneRM2)
                            {
                                recommendationInKg = recommendationInKg * (decimal)1.05;
                            }
                            else
                            {
                                recommendationInKg = recommendationInKg * (decimal)0.90;
                            }
                            recommendationInKg = Math.Ceiling(recommendationInKg);
                            result.Weight = new MultiUnityWeight(recommendationInKg, WeightUnities.kg);
                        }
                    }
                }
            }
            // Fin ajustement des séries en fonction du progres réalisé entre les 2 derniers workouts

            // Protection nombre de series < 2 et > 5
            if (result.Series < 2)
                result.Series = 2;
            if (result.Series > 5)
                result.Series = 5;
            // Fin protection nombre de series < 2 et > 5
            return result;
        }

        public static decimal ComputeOneRM(decimal weight, int reps)
        {
            ////////// Mayhew ////////////
            return (decimal)(100 * weight) / (decimal)(52.2 + 41.9 * Math.Exp(-0.055 * reps));
        }

        public static string AssemblyDirectory
        {
            get
            {
                string codeBase = Assembly.GetExecutingAssembly().CodeBase;
                UriBuilder uri = new UriBuilder(codeBase);
                string path = Uri.UnescapeDataString(uri.Path);
                return Path.GetDirectoryName(path);
            }
        }
    }
}



================================================
FILE: CATest/Properties/AssemblyInfo.cs
================================================
﻿using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// Les informations générales relatives à un assembly dépendent de 
// l'ensemble d'attributs suivant. Changez les valeurs de ces attributs pour modifier les informations
// associées à un assembly.
[assembly: AssemblyTitle("CATest")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("CATest")]
[assembly: AssemblyCopyright("Copyright ©  2016")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

// L'affectation de la valeur false à ComVisible rend les types invisibles dans cet assembly 
// aux composants COM.  Si vous devez accéder à un type dans cet assembly à partir de 
// COM, affectez la valeur true à l'attribut ComVisible sur ce type.
[assembly: ComVisible(false)]

// Le GUID suivant est pour l'ID de la typelib si ce projet est exposé à COM
[assembly: Guid("dca36025-f01b-4ca2-a0a2-8818decc4d2b")]

// Les informations de version pour un assembly se composent des quatre valeurs suivantes :
//
//      Version principale
//      Version secondaire 
//      Numéro de build
//      Révision
//
// Vous pouvez spécifier toutes les valeurs ou indiquer les numéros de build et de révision par défaut 
// en utilisant '*', comme indiqué ci-dessous :
// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]



================================================
FILE: DrMaxMuscle/DrMaxMuscle/app.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="XLabs.Serialization" publicKeyToken="d65109b36e5040e4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.5782.15703" newVersion="2.0.5782.15703" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/App.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<Application xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.App">
  <Application.Resources>
    <ResourceDictionary>
      <!-- Application resource dictionary -->
      <Style x:Key="buttonStyle" TargetType="Button">
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="TextColor" Value="White" />
        <Setter Property="BorderWidth" Value="2" />
        <Setter Property="BorderRadius" Value="5" />
        <Setter Property="BorderColor" Value="White" />
        <Setter Property="FontAttributes" Value="Bold" />
      </Style>
      <Style x:Key="entryStyle" TargetType="Button">
        <Setter Property="BackgroundColor" Value="White" />
      </Style>
	  <Style x:Key="slideMenuButtonStyle" TargetType="Button">
	  	<Setter Property="TextColor" Value="#212121" />
	  </Style>
    </ResourceDictionary>
  </Application.Resources>
</Application>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/App.xaml.cs
================================================
﻿using Acr.UserDialogs;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class App : Application
    {
        public NavigationPage NavPage { get; private set; }
        public App()
        {
            InitializeComponent();

            //MainPage = new NavigationPage(new LearnMorePage());return;
            // Check if we have a setting with a private key
            DBSetting dbToken = LocalDBManager.Instance.GetDBSetting("token");
            DBSetting dbTokenExpirationDate = LocalDBManager.Instance.GetDBSetting("token_expires_date");

            DrMaxMuscleRestClient.Instance.StartPost += DrMaxMuscleRestClient_StartPost;
            DrMaxMuscleRestClient.Instance.EndPost += DrMaxMuscleRestClient_EndPost;
            if (dbToken != null && dbTokenExpirationDate != null && DateTime.Now < new DateTime(Convert.ToInt64(dbTokenExpirationDate.Value)))
            {
                DrMaxMuscleRestClient.Instance.SetToken(LocalDBManager.Instance.GetDBSetting("token").Value);
                NavPage = new NavigationPage(new ChooseYourExercicePage())/* { BarBackgroundColor = Color.Gray }*/;
                
            }
            else
            {
                NavPage = new NavigationPage(new WelcomePage())/* { BarBackgroundColor = Color.Gray }*/;
            }
			NavPage.BarBackgroundColor = Color.Black;
			NavPage.BarTextColor = Color.White;
			MainPage = NavPage;
        }

        private void DrMaxMuscleRestClient_EndPost()
        {
            UserDialogs.Instance.HideLoading();
        }

        private void DrMaxMuscleRestClient_StartPost()
        {
            UserDialogs.Instance.ShowLoading("Loading...");
        }

        protected override async void OnStart()
        {
            // Handle when your app starts
        }

        protected override void OnSleep()
        {
            // Handle when your app sleeps
        }

        protected override async void OnResume()
        {
            // Handle when your app resumes
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/ChooseYourExercicePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<t:MenuContainerPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:DrMaxMuscle"
			 xmlns:t="clr-namespace:SlideOverKit"
             x:Class="DrMaxMuscle.ChooseYourExercicePage">
  <t:MenuContainerPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </t:MenuContainerPage.Padding>
	<t:MenuContainerPage.ToolbarItems>
  		<ToolbarItem Name="Buy" Activated="BuyButton_Clicked" Icon="menu.png" Order="Primary" Priority="0" />
 	</t:MenuContainerPage.ToolbarItems>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
      <StackLayout VerticalOptions="FillAndExpand">
        <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
          <ListView x:Name="ExerciseListView" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="White">
            <ListView.ItemTemplate>
			  <DataTemplate>
			      <TextCell Text="{Binding Label}" TextColor="White">
			         <TextCell.ContextActions>
			            <MenuItem Text="Rename" Clicked="OnRename" CommandParameter="{Binding .}" />
			            <MenuItem Text="Delete" Clicked="OnDelete" IsDestructive="True" CommandParameter="{Binding .}" />
			         </TextCell.ContextActions>
			      </TextCell>
			    </DataTemplate>
            </ListView.ItemTemplate>
          </ListView>
        </StackLayout>
      </StackLayout>
      <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
        <Button x:Name="HistoryButton" Text="History" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
		
  </AbsoluteLayout>
</t:MenuContainerPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/ChooseYourExercicePage.xaml.cs
================================================
﻿using Acr.UserDialogs;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using SlideOverKit;

namespace DrMaxMuscle
{
	public partial class ChooseYourExercicePage : MenuContainerPage
	{
		public ObservableCollection<ExerciceModel> exerciseItems = new ObservableCollection<ExerciceModel>();
		public ChooseYourExercicePage()
		{
			InitializeComponent();

			ExerciseListView.ItemsSource = exerciseItems;
			ExerciseListView.ItemTapped += ExerciseListView_ItemTapped;
			HistoryButton.Clicked += HistoryButton_Clicked;
			Title = "Choose your exercise";
			this.SlideMenu = new RightSideMasterPage();

			Device.BeginInvokeOnMainThread(async () =>
			{
				await UpdateExerciseList();
			});
		}

		public async void BuyButton_Clicked (object sender, EventArgs e)
		{
			ShowMenu();
			//await((NavigationPage)Application.Current.MainPage).PushAsync(new SubscriptionPage());
		}

		private void HistoryButton_Clicked(object sender, EventArgs e)
		{
			((NavigationPage)App.Current.MainPage).PushAsync(new HistoryPage());
		}

        public bool CanGoFurther()
        {
            if (string.IsNullOrEmpty(LocalDBManager.Instance.GetDBSetting("creation_date").Value))
            {
                DateTime setDate = DateTime.Now.ToUniversalTime();
                LocalDBManager.Instance.SetDBSetting("creation_date", setDate.Ticks.ToString());
                DrMaxMuscleRestClient.Instance.SetUserCreationDate(setDate);
            }

            DateTime creationDate = new DateTime(Convert.ToInt64(LocalDBManager.Instance.GetDBSetting("creation_date").Value));
            if ((DateTime.Now.ToUniversalTime() - creationDate).TotalDays < 14)
                return true;

            if (DependencyService.Get<IDrMuscleSubcription>().IsLifetimeAccessPuchased())
                return true;

            return false;
        }

        private async void ExerciseListView_ItemTapped(object sender, ItemTappedEventArgs e)
		{
			if (CanGoFurther())
			{
				if (((ExerciceModel)e.Item).Id != -1)
				{
					CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
					CurrentLog.Instance.ExerciseLog.Exercice = (ExerciceModel)e.Item;
					try
					{
						BooleanModel newExercise = await DrMaxMuscleRestClient.Instance.IsNewExercise(CurrentLog.Instance.ExerciseLog.Exercice);

						if (newExercise.Result)
						{
							var page = new FirstTimeExerciceWeight1Page();
							await ((NavigationPage)Application.Current.MainPage).PushAsync(page);
						}
						else
						{
							var page = new ExerciseChartPage();
							await ((NavigationPage)Application.Current.MainPage).PushAsync(page);
						}
					}
					catch (Exception ex)
					{
						await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
					}
				}
				else
				{
					AddMyOwnExercise();
				}
			}
			else 
			{
				await ((NavigationPage)Application.Current.MainPage).PushAsync(new SubscriptionPage());
			}
		}

		private void AddMyOwnExercise()
		{
			PromptConfig p = new PromptConfig()
			{
				InputType = InputType.Default,
				IsCancellable = true,
				Title = "New exercise",
				Placeholder = "Tap to enter name",
				OnAction = new Action<PromptResult>(AddExercisesAction)
			};

			UserDialogs.Instance.Prompt(p);
		}

		public void OnRename(object sender, EventArgs e)
		{
			var mi = ((MenuItem)sender);
			ExerciceModel m = (ExerciceModel)mi.CommandParameter;
			PromptConfig p = new PromptConfig()
			{
				InputType = InputType.Default,
				IsCancellable = true,
				Title = string.Format("Rename exercise \"{0}\"", m.Label),
				Placeholder = "Tap to enter exercise's new name",
				OnAction = new Action<PromptResult>((PromptResult response) => {
					if (response.Ok)
					{
						
						RenameExercisesAction(m, response.Text);
					}
				})
			};

			UserDialogs.Instance.Prompt(p);

		}

		public void RenameExercisesAction(ExerciceModel model, string newLabel)
		{
			
		}

		public void DeleteExercisesAction(ExerciceModel model)
		{

		}

		public void OnDelete(object sender, EventArgs e)
		{
			var mi = ((MenuItem)sender);
			ExerciceModel m = (ExerciceModel)mi.CommandParameter;
			ConfirmConfig p = new ConfirmConfig()
			{
				Title = "Delete exercise", 
				Message = string.Format("Delete all datas for exercise \"{0}\"?", m.Label),
				OkText = "I confirm !",
				CancelText = "No don't delete ma datas"
			};
			p.OnAction = (obj) =>
			{
				if (obj)
				{
					DeleteExercisesAction(m);
				}
			};
			UserDialogs.Instance.Confirm(p);
		}

		private async Task UpdateExerciseList()
		{
			exerciseItems.Clear();
			try
			{
				GetUserExerciseResponseModel itemsSource = await DrMaxMuscleRestClient.Instance.GetUserExercise(LocalDBManager.Instance.GetDBSetting("email").Value);
				foreach (ExerciceModel em in itemsSource.Exercises)
					exerciseItems.Add(em);

				ExerciceModel addExerciseItem = new ExerciceModel();
				addExerciseItem.Id = -1;
				addExerciseItem.Label = "Add my own...";
				exerciseItems.Add(addExerciseItem);
			}
			catch (Exception e)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
		}
		private async void AddExercisesAction(PromptResult response)
		{
			if (response.Ok)
			{
				try
				{
					ExerciceModel newExercise = await DrMaxMuscleRestClient.Instance.CreateNewExercise(new AddUserExerciseModel()
					{
						Username = LocalDBManager.Instance.GetDBSetting("email").Value,
						ExerciseName = response.Text
					});

					CurrentLog.Instance.ExerciseLog = new WorkoutLogSerieModel();
					CurrentLog.Instance.ExerciseLog.Exercice = newExercise;
					await ((NavigationPage)Application.Current.MainPage).PushAsync(new FirstTimeExerciceWeight1Page());
				}
				catch (Exception e)
				{
					await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
				}
			}
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/CurrentLog.cs
================================================
﻿using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public class CurrentLog
    {
        private static CurrentLog _instance;
        public WorkoutLogSerieModel ExerciseLog { get; set; }
        public MultiUnityWeight Weight1 { get; set; }
        public MultiUnityWeight Weight2 { get; set; }
        public MultiUnityWeight Weight3 { get; set; }
        public string Reps1 { get; set; }
        public string Reps2 { get; set; }
        public string Reps3 { get; set; }
        public bool IsNewExercise { get; set; }
        public RecommendationModel Recommendation { get; set; }
        private CurrentLog()
        { }

        public static CurrentLog Instance
        {
            get
            {
                if (_instance == null)
                    _instance = new CurrentLog();
                return _instance;
            }
        }


    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/DBSetting.cs
================================================
﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    [Table("Setting")]
    public class DBSetting
    {
        [PrimaryKey]
        public string Key { get; set; }
        public string Value { get; set; }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/DrMaxMuscle.csproj
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <MinimumVisualStudioVersion>10.0</MinimumVisualStudioVersion>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1B8D3453-60B4-4195-8A4D-941F48DB41DD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DrMaxMuscle</RootNamespace>
    <AssemblyName>s</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <TargetFrameworkProfile>Profile111</TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{786C830F-07A1-408B-BD7F-6EE04809D6DB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <ReleaseVersion>2.0</ReleaseVersion>
    <SynchReleaseVersion>false</SynchReleaseVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
    </Compile>
    <Compile Include="ChooseYourExercicePage.xaml.cs">
      <DependentUpon>ChooseYourExercicePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="CurrentLog.cs" />
    <Compile Include="DBSetting.cs" />
    <Compile Include="EndExercisePage.xaml.cs">
      <DependentUpon>EndExercisePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ExerciceNavigationPage.xaml.cs">
      <DependentUpon>ExerciceNavigationPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ExerciseChartPage.xaml.cs">
      <DependentUpon>ExerciseChartPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceReps1Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceReps1Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceReps2Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceReps2Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceReps3Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceReps3Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceWeight1Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceWeight1Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceWeight2Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceWeight2Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciceWeight3Page.xaml.cs">
      <DependentUpon>FirstTimeExerciceWeight3Page.xaml</DependentUpon>
    </Compile>
    <Compile Include="FirstTimeExerciseIntroPage.xaml.cs">
      <DependentUpon>FirstTimeExerciseIntroPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="HistoryExerciseLogView.xaml.cs">
      <DependentUpon>HistoryExerciseLogView.xaml</DependentUpon>
    </Compile>
    <Compile Include="HistoryExercisePage.xaml.cs">
      <DependentUpon>HistoryExercisePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="HistoryPage.xaml.cs">
      <DependentUpon>HistoryPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="ISQLite.cs" />
    <Compile Include="LearnMorePage.xaml.cs">
      <DependentUpon>LearnMorePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="LocalDBManager.cs" />
    <Compile Include="LoginPage.xaml.cs">
      <DependentUpon>LoginPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="MainPage.xaml.cs">
      <DependentUpon>MainPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="NowPage.xaml.cs">
      <DependentUpon>NowPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SaveWorkoutPage.xaml.cs">
      <DependentUpon>SaveWorkoutPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="TellMeMoreEmailPage.xaml.cs">
      <DependentUpon>TellMeMoreEmailPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="TellMeMoreFirstnamePage.xaml.cs">
      <DependentUpon>TellMeMoreFirstnamePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="TellMeMoreGenderPage.xaml.cs">
      <DependentUpon>TellMeMoreGenderPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="TellMeMoreMassUnitPage.xaml.cs">
      <DependentUpon>TellMeMoreMassUnitPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="TellMeMorePasswordPage.xaml.cs">
      <DependentUpon>TellMeMorePasswordPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Timer.cs" />
    <Compile Include="WelcomePage.xaml.cs">
      <DependentUpon>WelcomePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="SubscriptionPage.xaml.cs">
      <DependentUpon>SubscriptionPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="IDrMuscleSubcription.cs" />
    <Compile Include="RightSideMasterPage.xaml.cs">
      <DependentUpon>RightSideMasterPage.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="MainPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="App.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="SubscriptionPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="RightSideMasterPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="GettingStarted.Xamarin" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Acr.UserDialogs, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Acr.UserDialogs.6.2.0\lib\portable-win+net45+wp8+win8+wpa81\Acr.UserDialogs.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Acr.UserDialogs.Interface, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Acr.UserDialogs.6.2.0\lib\portable-win+net45+wp8+win8+wpa81\Acr.UserDialogs.Interface.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ExifLib, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ExifLib.PCL.1.0.1\lib\portable-net45+sl50+win+WindowsPhoneApp81+wp80+Xamarin.iOS10+MonoAndroid10+MonoTouch10\ExifLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Bcl.Async.1.0.168\lib\portable-net45+win8+wpa81\Microsoft.Threading.Tasks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Threading.Tasks.Extensions, Version=1.0.12.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Bcl.Async.1.0.168\lib\portable-net45+win8+wpa81\Microsoft.Threading.Tasks.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.6.0.4\lib\portable-net45+wp80+win8+wpa81\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot, Version=*******, Culture=neutral, PublicKeyToken=638079a8f0bd61e9, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Core.1.0.0-unstable1983\lib\portable-net45+netcore45+wpa81+wp8+MonoAndroid1+MonoTouch1+Xamarin.iOS10\OxyPlot.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Forms, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.1.0.0-unstable1983\lib\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\OxyPlot.Xamarin.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PortableRest, Version=3.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\PortableRest.3.0.1\lib\portable-net45+sl5+wp8+win8+wpa81+MonoTouch1+MonoAndroid1\PortableRest.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SlideOverKit, Version=1.0.6135.18790, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SlideOverKit.2.1.4\lib\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\SlideOverKit.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Splat, Version=1.6.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Splat.1.6.2\lib\Portable-net45+win+wpa81+wp80\Splat.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLite-net, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\sqlite-net-pcl.1.1.2\lib\portable-net45+wp8+wpa81+win8+MonoAndroid10+MonoTouch10+Xamarin.iOS10\SQLite-net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLitePCL.batteries, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCL.bundle_green.0.9.2\lib\portable-net45+netcore45+wpa81+wp8+MonoAndroid10+MonoTouch10+Xamarin.iOS10\SQLitePCL.batteries.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLitePCL.raw, Version=0.9.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCL.raw.0.9.2\lib\portable-net45+netcore45+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\SQLitePCL.raw.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Extensions, Version=1.5.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Net.Http.2.2.22\lib\portable-net45+win8+wpa81\System.Net.Http.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=********, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Net.Http.2.2.22\lib\portable-net45+win8+wpa81\System.Net.Http.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Core, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.2.3.2.127\lib\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Platform, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.2.3.2.127\lib\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.Platform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Xaml, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.2.3.2.127\lib\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.Xaml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XFGloss, Version=1.0.3.35, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Ansuria.XFGloss.1.0.3.35\lib\portable-net45+wp8+win8+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10+UAP10\XFGloss.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Core, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Core.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Forms, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Forms.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+monoandroid+MonoTouch+Xamarin.iOS10\XLabs.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.IOC, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.IoC.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.IOC.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Platform, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Platform.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+monoandroid+MonoTouch+Xamarin.iOS10\XLabs.Platform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Serialization, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Serialization.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.Serialization.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="WelcomePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="LearnMorePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TellMeMoreGenderPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TellMeMoreFirstnamePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TellMeMoreEmailPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TellMeMoreMassUnitPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ChooseYourExercicePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HistoryPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="NowPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ExerciceNavigationPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="TellMeMorePasswordPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciseIntroPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceWeight1Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceReps1Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceWeight2Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceReps2Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceWeight3Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FirstTimeExerciceReps3Page.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ExerciseChartPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="SaveWorkoutPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="EndExercisePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="LoginPage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HistoryExerciseLogView.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="HistoryExercisePage.xaml">
      <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="TODO.txt" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DrMaxMuscleWebApiClient\DrMaxMuscleWebApiClient.csproj">
      <Project>{d73b9d73-61a2-4ed7-bd70-54064bf09161}</Project>
      <Name>DrMaxMuscleWebApiClient</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DrMaxMuscleWebApiSharedModel\DrMaxMuscleWebApiSharedModel.csproj">
      <Project>{6e5ed3f4-02de-4b67-bcf4-d18664976209}</Project>
      <Name>DrMaxMuscleWebApiSharedModel</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\Portable\$(TargetFrameworkVersion)\Microsoft.Portable.CSharp.targets" />
  <Import Project="..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <Import Project="..\..\packages\Xamarin.Forms.2.3.2.127\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets" Condition="Exists('..\..\packages\Xamarin.Forms.2.3.2.127\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Ce projet fait référence à des packages NuGet qui sont manquants sur cet ordinateur. Utilisez l'option de restauration des packages NuGet pour les télécharger. Pour plus d'informations, consultez http://go.microsoft.com/fwlink/?LinkID=322105. Le fichier manquant est : {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Xamarin.Forms.2.3.2.127\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Forms.2.3.2.127\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/EndExercisePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:oxy="clr-namespace:OxyPlot.Xamarin.Forms;assembly=OxyPlot.Xamarin.Forms"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.EndExercisePage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout>
        <Grid HeightRequest="250">
          <oxy:PlotView x:Name="plotView" IsVisible="true" VerticalOptions="Start" HeightRequest="250"></oxy:PlotView>
        </Grid>
        <StackLayout x:Name="ResultStackLayout" VerticalOptions="FillAndExpand" Padding="0,10,0,0">
          <StackLayout Orientation="Horizontal" HeightRequest="32">
            <Label x:Name="lblResult1" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" Font="Bold,20" TextColor="White"></Label>
            <Image x:Name="IconResultImage" Source="green.png" Aspect="AspectFit" WidthRequest="32"></Image>
          </StackLayout>
          <Label x:Name="lblResult2" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" TextColor="White"></Label>
          <Label x:Name="lblResult21" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" TextColor="White"></Label>
          <Label x:Name="lblResult3" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" TextColor="White"></Label>
          <Label x:Name="lblResult4" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" TextColor="White"></Label>
          <Label x:Name="lblResult6" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" TextColor="White"></Label>
        </StackLayout>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextExerciseButton" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" IsVisible="true" Text="Next Exercise" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/EndExercisePage.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Globalization;
using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class EndExercisePage : ContentPage
    {
        List<OneRMModel> _lastWorkoutLog = new List<OneRMModel>();
        public EndExercisePage()
        {
            InitializeComponent();

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;

            Device.BeginInvokeOnMainThread(async () =>
            {
				try
				{
					_lastWorkoutLog = await DrMaxMuscleRestClient.Instance.GetOneRMForExercise(
						new GetOneRMforExerciseModel()
						{
							Username = LocalDBManager.Instance.GetDBSetting("email").Value,
							Massunit = LocalDBManager.Instance.GetDBSetting("massunit").Value,
							ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id
						}
					);

					var plotModel = new PlotModel
					{
						Title = "1RM - LAST 3 WORKOUTS",
						//Subtitle = "for the 3 last workouts",
						Background = OxyColors.LightYellow,
						PlotAreaBackground = OxyColors.LightGray
					};

					double minY;
					double maxY;

					switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
					{
						default:
						case "kg":
							minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Kg) / 10) * 10);
							maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Kg) / 10) * 10);
							break;
						case "lb":
							minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Lb) / 10) * 10);
							maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Lb) / 10) * 10);
							break;
					}

					LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = minY - 5, Maximum = maxY + 5 };
					LinearAxis xAxis = new DateTimeAxis { Position = AxisPosition.Bottom };
					// xAxis.LabelFormatter = _formatter;
					xAxis.MinimumPadding = 5;
					xAxis.IsPanEnabled = false;
					xAxis.IsZoomEnabled = false;
					xAxis.Minimum = DateTimeAxis.ToDouble(_lastWorkoutLog.Min(l => l.OneRMDate).AddDays(-1));
					xAxis.Maximum = DateTimeAxis.ToDouble(_lastWorkoutLog.Max(l => l.OneRMDate).AddDays(1));

					yAxis.IsPanEnabled = false;
					yAxis.IsZoomEnabled = false;
					plotModel.Axes.Add(yAxis);
					plotModel.Axes.Add(xAxis);


					var s1 = new LineSeries()
					{
						Color = OxyColors.SkyBlue,
						MarkerType = MarkerType.Circle,
						MarkerSize = 6,
						MarkerStroke = OxyColors.White,
						MarkerFill = OxyColors.SkyBlue,
						MarkerStrokeThickness = 1,
						LabelFormatString = "{1:0}",
						FontSize = 15
					};



					int i = 1;
					foreach (OneRMModel m in _lastWorkoutLog.OrderBy(w => w.OneRMDate))
					{

						switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
						{
							default:
							case "kg":
								s1.Points.Add(new DataPoint(DateTimeAxis.ToDouble(m.OneRMDate), Convert.ToDouble(m.OneRM.Kg)));
								break;
							case "lb":
								s1.Points.Add(new DataPoint(DateTimeAxis.ToDouble(m.OneRMDate), Convert.ToDouble(m.OneRM.Lb)));
								break;
						}
						i++;
					}

					plotModel.Series.Add(s1);
					plotView.Model = plotModel;

					lblResult1.Text = string.Format("Congratulations {0}!", LocalDBManager.Instance.GetDBSetting("firstname").Value);
					lblResult2.Text = "You have set a new record!";
					lblResult21.Text = string.Format("Today's 1RM is: {0:0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
						_lastWorkoutLog.ElementAt(0).OneRM.Kg :
						_lastWorkoutLog.ElementAt(0).OneRM.Lb,
						LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs");
					lblResult3.Text = string.Format("Previous 1RM was: {0:0} {1}", LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
						_lastWorkoutLog.ElementAt(1).OneRM.Kg :
						_lastWorkoutLog.ElementAt(1).OneRM.Lb,
						LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs");


					DateTime minDate = _lastWorkoutLog.Min(p => p.OneRMDate);
					DateTime maxDate = _lastWorkoutLog.Max(p => p.OneRMDate);
					OneRMModel beforelast = _lastWorkoutLog.Where(p => p.OneRMDate > minDate && p.OneRMDate < maxDate).First();
					OneRMModel last = _lastWorkoutLog.First(p => p.OneRMDate == maxDate);

					decimal weight0 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
													last.OneRM.Kg :
													last.OneRM.Lb;
					decimal weight1 = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
													beforelast.OneRM.Kg :
													beforelast.OneRM.Lb;

					lblResult4.Text = string.Format("Change is: {0:0} %", ((weight0 - weight1) * 100 / weight1));

					if (weight0 <= weight1)
					{
						lblResult1.IsVisible = false;
						lblResult2.IsVisible = false;
						IconResultImage.Source = "orange.png";
					}
					else
					{
						lblResult1.IsVisible = true;
						lblResult2.IsVisible = true;
					}
				}
				catch (Exception e)
				{
					await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
				}
            });

            NextExerciseButton.Clicked += NextExerciseButton_Clicked;
        }

        private void NextExerciseButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new ChooseYourExercicePage());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/ExerciceNavigationPage.xaml
================================================
﻿<?xml version="1.0" encoding="utf-8" ?>
<NavigationPage xmlns="http://xamarin.com/schemas/2014/forms"
                xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                xmlns:local="clr-namespace:DrMaxMuscle"
                x:Class="DrMaxMuscle.ExerciceNavigationPage">
  <x:Arguments>
    <local:ChooseYourExercicePage />
    <local:FirstTimeExerciseIntroPage />
  </x:Arguments>
</NavigationPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/ExerciceNavigationPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class ExerciceNavigationPage : NavigationPage
    {
        public ExerciceNavigationPage()
        {
            InitializeComponent();
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/ExerciseChartPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:oxy="clr-namespace:OxyPlot.Xamarin.Forms;assembly=OxyPlot.Xamarin.Forms"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.ExerciseChartPage">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout>
        <Grid HeightRequest="250">
          <oxy:PlotView x:Name="plotView" IsVisible="true" VerticalOptions="Start" HeightRequest="250"></oxy:PlotView>
        </Grid>
        <StackLayout x:Name="ResultStackLayout" VerticalOptions="FillAndExpand" Padding="0,10,0,0">
          <Label x:Name="lblResult3" Text="" IsVisible="true" HorizontalOptions="StartAndExpand" Font="Bold,16"></Label>
          <Label x:Name="lblResult4" Text="" IsVisible="true" HorizontalOptions="StartAndExpand"></Label>
          <Label x:Name="lblResult5" Text="" IsVisible="true" HorizontalOptions="StartAndExpand"></Label>
        </StackLayout>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <!--<Button x:Name="HistoryButton" Text="History" HorizontalOptions="FillAndExpand" BackgroundColor="#94c6e3"></Button>-->
      <Button x:Name="BeginButton" Text="Begin Exercise" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/ExerciseChartPage.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class ExerciseChartPage : ContentPage
    {
        List<OneRMModel> _lastWorkoutLog = new List<OneRMModel>();
        public ExerciseChartPage()
        {
            InitializeComponent();

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            BeginButton.Clicked += BeginButton_Clicked;
            // HistoryButton.Clicked += HistoryButton_Clicked;
            Device.BeginInvokeOnMainThread(async () =>
            {
				try
				{
					_lastWorkoutLog = await DrMaxMuscleRestClient.Instance.GetOneRMForExercise(
						new GetOneRMforExerciseModel()
						{
							Username = LocalDBManager.Instance.GetDBSetting("email").Value,
							Massunit = LocalDBManager.Instance.GetDBSetting("massunit").Value,
							ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id
						}
					);

					var plotModel = new PlotModel
					{
						Title = "1RM - LAST 3 WORKOUTS",
						//Subtitle = "for the 3 last workouts",
						Background = OxyColors.LightYellow,
						PlotAreaBackground = OxyColors.LightGray
					};

					double minY;
					double maxY;

					switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
					{
						default:
						case "kg":
							minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Kg) / 10) * 10);
							maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Kg) / 10) * 10);
							break;
						case "lb":
							minY = (double)(Math.Floor(_lastWorkoutLog.Min(o => o.OneRM.Lb) / 10) * 10);
							maxY = (double)(Math.Ceiling(_lastWorkoutLog.Max(o => o.OneRM.Lb) / 10) * 10);
							break;
					}

					LinearAxis yAxis = new LinearAxis { Position = AxisPosition.Left, Minimum = minY - 5, Maximum = maxY + 5 };
					LinearAxis xAxis = new DateTimeAxis { Position = AxisPosition.Bottom };
					//xAxis.LabelFormatter = _formatter;
					xAxis.IsPanEnabled = false;
					xAxis.IsZoomEnabled = false;
					xAxis.Minimum = DateTimeAxis.ToDouble(_lastWorkoutLog.Min(l => l.OneRMDate).AddDays(-1));
					xAxis.Maximum = DateTimeAxis.ToDouble(_lastWorkoutLog.Max(l => l.OneRMDate).AddDays(1));
					yAxis.IsPanEnabled = false;
					yAxis.IsZoomEnabled = false;

					plotModel.Axes.Add(yAxis);
					plotModel.Axes.Add(xAxis);


					var s1 = new LineSeries()
					{
						Color = OxyColors.SkyBlue,
						MarkerType = MarkerType.Circle,
						MarkerSize = 6,
						MarkerStroke = OxyColors.White,
						MarkerFill = OxyColors.SkyBlue,
						MarkerStrokeThickness = 1,
						LabelFormatString = "{1:0}",
						FontSize = 15
					};

					int i = 1;
					foreach (OneRMModel m in _lastWorkoutLog.OrderBy(w => w.OneRMDate))
					{

						switch (LocalDBManager.Instance.GetDBSetting("massunit").Value)
						{
							default:
							case "kg":
								s1.Points.Add(new DataPoint(DateTimeAxis.ToDouble(m.OneRMDate), Convert.ToDouble(m.OneRM.Kg)));
								break;
							case "lb":
								s1.Points.Add(new DataPoint(DateTimeAxis.ToDouble(m.OneRMDate), Convert.ToDouble(m.OneRM.Lb)));
								break;
						}
						i++;
					}


					plotModel.Series.Add(s1);

					plotView.Model = plotModel;

					string WeightRecommandation;
					RecommendationModel reco = await DrMaxMuscleRestClient.Instance.GetRecommendationForExercise(new GetRecommendationForExerciseModel()
					{
						Username = LocalDBManager.Instance.GetDBSetting("email").Value,
						ExerciseId = CurrentLog.Instance.ExerciseLog.Exercice.Id
					});
					CurrentLog.Instance.Recommendation = reco;

					WeightRecommandation = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
											string.Format("{0:0}", reco.Weight.Kg) :
											string.Format("{0:0}", reco.Weight.Lb);

					if ((double)reco.OneRMProgress >= 7.5)
					{
						;
						lblResult3.Text = "Do this today to build muscle faster:";
						lblResult4.Text = string.Format("- Do {0} {1} for {2} reps", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Reps);
						lblResult5.Text = "- Push hard and do 1 more set than last workout";
						ResultStackLayout.BackgroundColor = Color.Transparent;
						Color textColor = Color.White;
						lblResult3.TextColor = textColor;
						lblResult4.TextColor = textColor;
						lblResult5.TextColor = textColor;
					}
					else
					{
						if ((double)reco.OneRMProgress >= 2 && (double)reco.OneRMProgress < 7.5)
						{
							lblResult3.Text = "Do this today to build muscle faster:";
							lblResult4.Text = string.Format("- Do {0} {1} for {2} reps", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Reps);
							lblResult5.Text = "- Do as many sets as last workout";
							ResultStackLayout.BackgroundColor = Color.Transparent;
							Color textColor = Color.White;
							lblResult3.TextColor = textColor;
							lblResult4.TextColor = textColor;
							lblResult5.TextColor = textColor;
						}
						else
						{
							if ((double)reco.OneRMProgress >= 0.1 && reco.OneRMProgress < 2)
							{
								lblResult3.Text = "Do this today to build muscle faster:";
								lblResult4.Text = string.Format("- Do {0} {1} for {2} reps", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Reps);
								lblResult5.Text = "- Do 1 less set than last workout to deload";
								ResultStackLayout.BackgroundColor = Color.Transparent;
								Color textColor = Color.White;
								lblResult3.TextColor = textColor;
								lblResult4.TextColor = textColor;
								lblResult5.TextColor = textColor;
							}
							else
							{
								if ((double)reco.OneRMProgress < 0.1)
								{
									lblResult3.Text = "Do this today to build muscle faster:";
									lblResult4.Text = string.Format("- Do {0} {1} for {2} reps", WeightRecommandation, LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? "kg" : "lbs", reco.Reps);
									lblResult5.Text = "- Do half the sets you normally would to deload";
									ResultStackLayout.BackgroundColor = Color.Transparent;
									Color textColor = Color.White;
									lblResult3.TextColor = textColor;
									lblResult4.TextColor = textColor;
									lblResult5.TextColor = textColor;
								}
							}
						}
					}
				}
				catch (Exception e)
				{
					await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
				}
            });
        }

        private void HistoryButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new HistoryExercisePage());
        }

        private void BeginButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new SaveWorkoutPage());
        }

        /// <summary>
        /// Formatter the specified d for the chart (X axis).
        /// </summary>
        /// <param name="d">D.</param>
        private string _formatter(double d)
        {
            if (d == 1)
                return _lastWorkoutLog.ElementAt(2).OneRMDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture);
            if (d == 2)
                return _lastWorkoutLog.ElementAt(1).OneRMDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture);
            if (d == 3)
                return _lastWorkoutLog.ElementAt(0).OneRMDate.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture);
            return "";
        }

        public static string DoFormat(double myNumber)
        {
            var s = string.Format("{0:0.00}", myNumber);

            if (s.EndsWith("00"))
            {
                return ((int)myNumber).ToString();
            }
            else
            {
                return s;
            }
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps1Page.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceReps1Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White"/>
      <Entry x:Name="RepsEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
	
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps1Page.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceReps1Page : ContentPage
    {
        public FirstTimeExerciceReps1Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "You lifted ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? string.Format("{0:0.0} kg", CurrentLog.Instance.Weight1.Kg) : string.Format("{0:0.0} lbs", CurrentLog.Instance.Weight1.Lb), FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = " for how many reps ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;

            RepsEntry.BindingContext = CurrentLog.Instance;
            RepsEntry.SetBinding(Entry.TextProperty, "Reps1", BindingMode.TwoWay);

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;

            RepsEntry.Completed += NextButton_Clicked;
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceWeight2Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps2Page.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceReps2Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White" />
      <Entry x:Name="RepsEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps2Page.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceReps2Page : ContentPage
    {
        public FirstTimeExerciceReps2Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "You lifted ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? string.Format("{0:0.0} kg", CurrentLog.Instance.Weight2.Kg) : string.Format("{0:0.0} lbs", CurrentLog.Instance.Weight2.Lb), FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = " for how many reps ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;

            RepsEntry.BindingContext = CurrentLog.Instance;
            RepsEntry.SetBinding(Entry.TextProperty, "Reps2", BindingMode.TwoWay);

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            RepsEntry.Completed += NextButton_Clicked;
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceWeight3Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps3Page.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceReps3Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White"/>
      <Entry x:Name="RepsEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceReps3Page.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceReps3Page : ContentPage
    {
        public FirstTimeExerciceReps3Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "OK. You lifted ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? string.Format("{0:0.0} kg", CurrentLog.Instance.Weight3.Kg) : string.Format("{0:0.0} lbs", CurrentLog.Instance.Weight3.Lb), FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = " for how many reps ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;
            
            RepsEntry.BindingContext = CurrentLog.Instance;
            RepsEntry.SetBinding(Entry.TextProperty, "Reps3", BindingMode.TwoWay);

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            RepsEntry.Completed += NextButton_Clicked;
        }

        private async void NextButton_Clicked(object sender, EventArgs e)
        {
			try
			{
				NewExerciceLogModel model = new NewExerciceLogModel();
				model.ExerciseId = (int)CurrentLog.Instance.ExerciseLog.Exercice.Id;
				model.Weight3 = CurrentLog.Instance.Weight3;
				model.Weight2 = CurrentLog.Instance.Weight2;
				model.Weight1 = CurrentLog.Instance.Weight1;
				model.Reps3 = CurrentLog.Instance.Reps3;
				model.Reps2 = CurrentLog.Instance.Reps2;
				model.Reps1 = CurrentLog.Instance.Reps1;
				model.Username = LocalDBManager.Instance.GetDBSetting("email").Value;
				BooleanModel result = await DrMaxMuscleRestClient.Instance.AddNewExerciseLog(model);
				((NavigationPage)App.Current.MainPage).PushAsync(new ExerciseChartPage());
			}
			catch (Exception ex)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight1Page.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceWeight1Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White"/>
      <Entry x:Name="WeightEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight1Page.xaml.cs
================================================
﻿using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceWeight1Page : ContentPage
    {
        public FirstTimeExerciceWeight1Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = LocalDBManager.Instance.GetDBSetting("firstname").Value + ", last time you did ", FontSize=16 });
            fs.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = ", how much did you lift (on your best set) ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;

            WeightEntry.Completed += WeightEntry_Completed;
        }

        private void WeightEntry_Completed(object sender, EventArgs e)
        {
            NextButton_Clicked(sender, e);
        }

        private void WhyButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciseIntroPage());
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            CurrentLog.Instance.Weight1 = new MultiUnityWeight(Convert.ToDecimal(WeightEntry.Text), LocalDBManager.Instance.GetDBSetting("massunit").Value);
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceReps1Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight2Page.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceWeight2Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White"/>
      <Entry x:Name="WeightEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
      <Label x:Name="IntroText3Label" />
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight2Page.xaml.cs
================================================
﻿using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceWeight2Page : ContentPage
    {
        public FirstTimeExerciceWeight2Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "OK! The workout before that, for ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = ", how much did you lift ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            WeightEntry.Completed += WeightEntry_Completed;
        }

        private void WeightEntry_Completed(object sender, EventArgs e)
        {
            NextButton_Clicked(sender, e);
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            CurrentLog.Instance.Weight2 = new MultiUnityWeight(Convert.ToDecimal(WeightEntry.Text), LocalDBManager.Instance.GetDBSetting("massunit").Value);
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceReps2Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight3Page.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciceWeight3Page">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroTextLabel" VerticalOptions="Center" HorizontalOptions="Center" TextColor="White"/>
      <Entry x:Name="WeightEntry" Keyboard="Numeric" Style="{StaticResource entryStyle}"></Entry>
      <Label x:Name="IntroText3Label" />
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciceWeight3Page.xaml.cs
================================================
﻿using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciceWeight3Page : ContentPage
    {
        public FirstTimeExerciceWeight3Page()
        {
            InitializeComponent();

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "Finally, the workout before that, for ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = ", how much did you lift ?", FontSize = 16 });
            IntroTextLabel.FormattedText = fs;

            NextButton.Clicked += NextButton_Clicked;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            WeightEntry.Completed += WeightEntry_Completed;
        }
        private void WeightEntry_Completed(object sender, EventArgs e)
        {
            NextButton_Clicked(sender, e);
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            CurrentLog.Instance.Weight3 = new MultiUnityWeight(Convert.ToDecimal(WeightEntry.Text), LocalDBManager.Instance.GetDBSetting("massunit").Value);
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceReps3Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciseIntroPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.FirstTimeExerciseIntroPage">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <Label x:Name="IntroText1Label" TextColor="White"/>
      <Label x:Name="IntroText2Label" TextColor="White"/>
      <Label x:Name="IntroText3Label" TextColor="White"/>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Padding="0,0,0,20">
      <Button x:Name="NextButton" Text="Continue" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/FirstTimeExerciseIntroPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class FirstTimeExerciseIntroPage : ContentPage
    {
        public string IntroText1;
        public FirstTimeExerciseIntroPage()
        {
            InitializeComponent();

            NextButton.Clicked += NextButton_Clicked;

            var fs = new FormattedString();
            fs.Spans.Add(new Span { Text = "Ok " + LocalDBManager.Instance.GetDBSetting("firstname").Value + ". Now I will tell you how much you should lift today for ", FontSize = 16 });
            fs.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs.Spans.Add(new Span { Text = " based on your last 3 workouts.", FontSize = 16 });
            IntroText1Label.FormattedText = fs;

            FormattedString fs2 = new FormattedString();
            fs2.Spans.Add(new Span { Text = "But first, since this is the first time we're doing ", FontSize = 16 });
            fs2.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs2.Spans.Add(new Span { Text = ", you need to enter information about your last workouts with ", FontSize = 16 });
            fs2.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            IntroText2Label.FormattedText = fs2;

            FormattedString fs3 = new FormattedString();
            fs3.Spans.Add(new Span { Text = "Next time you do ", FontSize = 16 });
            fs3.Spans.Add(new Span { Text = CurrentLog.Instance.ExerciseLog.Exercice.Label, FontAttributes = FontAttributes.Bold, FontSize = 16 });
            fs3.Spans.Add(new Span { Text = ", I'll remember that information (you'll never have to enter it again)", FontSize = 16 });
            IntroText3Label.FormattedText = fs3;

            CurrentLog.Instance.IsNewExercise = true;

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
        }

        private void NextButton_Clicked(object sender, EventArgs e)
        {
            ((NavigationPage)App.Current.MainPage).PushAsync(new FirstTimeExerciceWeight1Page());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/GettingStarted.Xamarin
================================================
<GettingStarted>
	<LocalContent>GS\XF\CS\App\GettingStarted.html</LocalContent>
	<EmbeddedNavigation>false</EmbeddedNavigation>
</GettingStarted>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryExerciseLogView.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="DrMaxMuscle.HistoryExerciseLogView">
  <ContentView.Content>
    <StackLayout Orientation="Vertical" Padding="20,0,0,0">
	  <Label x:Name="ExerciseLabel" TextColor="White"></Label>
      <StackLayout Orientation="Vertical" Padding="20,0,0,0">
      <StackLayout x:Name="SetsStackLayout" Orientation="Vertical"></StackLayout>
        <Label x:Name="Best1RMLabel" TextColor="White"></Label>
        <Label x:Name="OverallLabel" FontSize="10" TextColor="White"></Label>
      </StackLayout>
    </StackLayout>
  </ContentView.Content>
</ContentView>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryExerciseLogView.xaml.cs
================================================
﻿using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class HistoryExerciseLogView : ContentView
    {
        public HistoryExerciseLogView()
        {
            InitializeComponent();
        }

        public void SetModel(HistoryExerciseModel model)
        {
			ExerciseLabel.Text = model.Exercise.Label;
            foreach (WorkoutLogSerieModel set in model.Sets)
            {
				Label setLabel = new Label() { TextColor = Color.White };
                setLabel.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? string.Format("{0:0.0} kg", set.Weight.Kg) : string.Format("{0:0.0} lbs", set.Weight.Lb);
                setLabel.Text += string.Format(" {0} reps", set.Reps);
                SetsStackLayout.Children.Add(setLabel);
            }
            Best1RMLabel.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? string.Format("{0:0.0} kg best 1RM", model.BestSerie1RM.Kg) : string.Format("{0:0.0} lbs best 1RM", model.BestSerie1RM.Lb);
            OverallLabel.Text = string.Format("{0:0.0} total ({1} sets | {2} reps | {3} per rep on average)",
                LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? 
                    string.Format("{0:0.0} kg", model.TotalWeight.Kg) : 
                    string.Format("{0:0.0} lbs", model.TotalWeight.Lb),
                model.Series,
                model.Reps,
                LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? 
                    string.Format("{0:0.0} kg", model.AverageWeightByRep.Kg) : 
                    string.Format("{0:0.0} lbs", model.AverageWeightByRep.Lb)
                );
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryExercisePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.HistoryExercisePage">
  <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,20,20,0">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout BackgroundColor="Black" Padding="0" VerticalOptions="FillAndExpand">
        <ScrollView  Orientation="Vertical" VerticalOptions="FillAndExpand" BackgroundColor="White">
          <StackLayout x:Name="ExerciseHistoryStackLayout" Orientation="Vertical">
          </StackLayout>
        </ScrollView>
      </StackLayout>
    </StackLayout>
  </StackLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryExercisePage.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class HistoryExercisePage : ContentPage
    {
        List<HistoryModel> history = new List<HistoryModel>();
        public HistoryExercisePage()
        {
            InitializeComponent();

            Title = string.Format("{0} History", CurrentLog.Instance.ExerciseLog.Exercice.Label); 
        }

        protected async override void OnAppearing()
        {
            base.OnAppearing();
            ExerciseHistoryStackLayout.Children.Clear();
			try
			{
				history = await DrMaxMuscleRestClient.Instance.GetExerciseHistory(CurrentLog.Instance.ExerciseLog.Exercice.Id);

				foreach (HistoryModel hm in history)
				{
					StackLayout hsl = new StackLayout();
					hsl.Orientation = StackOrientation.Vertical;
					hsl.Padding = new Thickness(10, 0, 0, 0);

					Label workout = new Label() { Text = hm.WorkoutDate.ToString("dddd, MMMM dd | hh:mm tt"), FontAttributes = FontAttributes.Bold, FontSize = 16 };
					hsl.Children.Add(workout);
					hsl.Children.Add(new BoxView() { Color = Color.Gray, HeightRequest = 1, Opacity = 0.5 });
					foreach (HistoryExerciseModel hem in hm.Exercises)
					{
						HistoryExerciseLogView v = new HistoryExerciseLogView();
						v.SetModel(hem);
						hsl.Children.Add(v);
					}


					ExerciseHistoryStackLayout.Children.Add(hsl);
				}
			}
			catch (Exception e)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.HistoryPage">
   <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
        <ScrollView  Orientation="Vertical" VerticalOptions="FillAndExpand" BackgroundColor="Transparent">
          <StackLayout x:Name="ExerciseHistoryStackLayout" Orientation="Vertical">
          </StackLayout>
        </ScrollView>
      </StackLayout>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/HistoryPage.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class HistoryPage : ContentPage
    {
        List<HistoryModel> history = new List<HistoryModel>();
        public HistoryPage()
        {
            InitializeComponent();
            Title = "History";
        }

        protected async override void OnAppearing()
        {
            base.OnAppearing();
            ExerciseHistoryStackLayout.Children.Clear();
			try
			{
				history = await DrMaxMuscleRestClient.Instance.GetHistory();

				foreach (HistoryModel hm in history)
				{
					StackLayout hsl = new StackLayout();
					hsl.Orientation = StackOrientation.Vertical;
					hsl.Padding = new Thickness(10, 0, 0, 0);

					Label workout = new Label() { Text = hm.WorkoutDate.ToLocalTime().ToString("dddd, MMMM dd | hh:mm tt"), FontAttributes = FontAttributes.Bold, FontSize = 16, TextColor = Color.White };
					hsl.Children.Add(workout);
					hsl.Children.Add(new BoxView() { Color = Color.Gray, HeightRequest = 1, Opacity = 0.5 });
					foreach (HistoryExerciseModel hem in hm.Exercises)
					{
						HistoryExerciseLogView v = new HistoryExerciseLogView();
						v.SetModel(hem);
						hsl.Children.Add(v);
					}


					ExerciseHistoryStackLayout.Children.Add(hsl);
				}
			}
			catch (Exception e)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/IDrMuscleSubcription.cs
================================================
﻿using System;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
	public delegate void LifetimeAccessPurchased();

	public interface IDrMuscleSubcription
	{
		bool IsLifetimeAccessPuchased();
		string GetButtonLabel();
		event LifetimeAccessPurchased OnLifetimeAccessPurchased;
		Task BuyLifetimeAccess();
		void Init();
		void RestorePurchases();
		string GetBuildVersion();
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/ISQLite.cs
================================================
﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public interface ISQLite
    {
        SQLiteConnection GetConnection();
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/LearnMorePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.LearnMorePage" 
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             Title="Learn More">
  <StackLayout>
    <AbsoluteLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">
      <AbsoluteLayout.LayoutBounds>
        <OnPlatform x:TypeArguments="Rectangle">
          <OnPlatform.iOS>
            0.75, 0.75, 0.5, 0.5
          </OnPlatform.iOS>
          <OnPlatform.Android>
            0.5, 0.5, 0.5, 0.5
          </OnPlatform.Android>
        </OnPlatform>
      </AbsoluteLayout.LayoutBounds>
      <ScrollView AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
        <StackLayout Orientation="Vertical" VerticalOptions="StartAndExpand" HorizontalOptions="CenterAndExpand">
          <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand" Padding="20,75,20,0">
            <Label Text="Dr. Muscle uses advanced daily undulating periodization (DUP) models to help you build muscle faster." FontSize="16" Margin="0,10,0,0"></Label>
            <Label Text="Here's how it works:" FontSize="16" Margin="0,10,0,0"></Label>
            <Label Text="1- You select an exercise" FontSize="16" Margin="0,10,0,0"></Label>
            <Label Text="2- Dr. Muscle analyzes your last 3 workouts for that exercise" FontSize="16" Margin="0,10,0,0"></Label>
			<Label Text="3- Dr. Muscle uses its advanced algorithm to automatically and accurately select your next weights, reps, and sets for today's workout" FontSize="16" Margin="0,10,0,0"></Label>
			<Label Text="Dr. Muscle's advanced algorith is based on:" FontSize="16" Margin="0,10,0,0"></Label>
            <Label Text="Brzycki's equation" FontSize="16" Margin="0,10,0,0"></Label>
            <Label Text="Your own past performance" FontSize="16" Margin="0,10,0,0"></Label>
			<Label Text="Daily undulating periodization (DUP)" FontSize="16" Margin="0,10,0,0"></Label>
			<Label Text="With DUP, you change rep bracket every time you work out. You get a new training stimulus, and you build muscle faster. With Dr. Muscle, you'll know exactly how much to lift, even if you change reps every workout." FontSize="16" Margin="0,10,0,0"></Label>
          </StackLayout>
        </StackLayout>
      </ScrollView>
    </AbsoluteLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand"  HorizontalOptions="FillAndExpand">
      <Button x:Name="GetStartedButton" Text="Sign-up" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
    </StackLayout>
  </StackLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/LearnMorePage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class LearnMorePage : ContentPage
    {
        public LearnMorePage()
        {
            InitializeComponent();

            GetStartedButton.Clicked += GetStartedButton_Clicked;
			//NavigationPage.SetHasNavigationBar(this, false);
        }

        private async void GetStartedButton_Clicked(object sender, EventArgs e)
        {
			await((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreGenderPage());
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/LocalDBManager.cs
================================================
﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace DrMaxMuscle
{
    public class LocalDBManager
    {
        private SQLiteConnection database;
        private static LocalDBManager instance;
        public static LocalDBManager Instance
        {
            get
            {
                if (instance == null)
                    instance = new LocalDBManager();
                return instance;
            }
        }
        private LocalDBManager()
        {
            database = DependencyService.Get<ISQLite>().GetConnection();
            InitDatabase();
        }

        private void InitDatabase()
        {
            if (!TableExists<DBSetting>())
            {
                database.CreateTable<DBSetting>();
            }
        }
        
        private bool TableExists<T>()
        {
            const string cmdText = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
            var cmd = database.CreateCommand(cmdText, typeof(T).Name);
            return cmd.ExecuteScalar<string>() != null;
        }

        public DBSetting GetDBSetting(string key)
        {
            return database.Table<DBSetting>().FirstOrDefault(s => s.Key == key);
        }

        public void SetDBSetting(string key, string value)
        {
            SetDBSetting(new DBSetting() { Key = key, Value = value });
        }

        public void SetDBSetting(DBSetting setting)
        {
            if (database.Table<DBSetting>().Count(x => x.Key == setting.Key) == 0)
                database.Insert(setting);
            else
                database.Update(setting);
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/LoginPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.LoginPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 20, 0, 0" />
  </ContentPage.Padding>
  <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,20,20,0">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
        <Entry x:Name="EmailEntry" Placeholder="Tap to enter your email" Keyboard="Email" Style="{StaticResource entryStyle}"></Entry>
        <Entry x:Name="PasswordEntry" Placeholder="Tap to enter your password" IsPassword="True" Style="{StaticResource entryStyle}"></Entry>
        <Button x:Name="LoginButton" Text="Log in" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
    </StackLayout>
  </StackLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/LoginPage.xaml.cs
================================================
﻿using Acr.UserDialogs;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class LoginPage : ContentPage
    {
        public LoginPage()
        {
            InitializeComponent();

            LoginButton.Clicked += LoginButton_Clicked;
        }

        private async void LoginButton_Clicked(object sender, EventArgs e)
        {
	            LoginSuccessResult lr = await DrMaxMuscleRestClient.Instance.Login(new LoginModel()
	            {
	                Username = EmailEntry.Text,
	                Password = PasswordEntry.Text
	            });

	            if (lr != null)
	            {
	                DateTime current = DateTime.Now;


	                UserInfosModel uim = await DrMaxMuscleRestClient.Instance.GetUserInfo();

	                LocalDBManager.Instance.SetDBSetting("email", uim.Email);
	                LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
	                LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
	                LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
	                LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
	                LocalDBManager.Instance.SetDBSetting("password", PasswordEntry.Text);
	                LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
	                LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
					LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());
					await ((NavigationPage)Application.Current.MainPage).PushAsync(new ChooseYourExercicePage());
	            }
	            else
	            {
	                UserDialogs.Instance.Alert(new AlertConfig() { Message = "Login error occur, please check your email and password", Title = "Unable to login" });
	            }
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/MainPage.xaml
================================================
﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
            xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
            xmlns:local="clr-namespace:DrMaxMuscle"
            x:Class="DrMaxMuscle.MainPage">
  
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/MainPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class MainPage : ContentPage
    {
        public MainPage()
        {
            InitializeComponent();
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/NowPage.xaml
================================================
﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.NowPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 20, 0, 0" />
  </ContentPage.Padding>
  <Label Text="NowPage" VerticalOptions="Center" HorizontalOptions="Center" />
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/NowPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class NowPage : ContentPage
    {
        public NowPage()
        {
            InitializeComponent();
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/packages.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Acr.UserDialogs" version="6.2.0" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Ansuria.XFGloss" version="1.0.3.35" targetFramework="portable45-net45+win8+wpa81" />
  <package id="ExifLib.PCL" version="1.0.1" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Microsoft.Bcl" version="1.1.9" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Microsoft.Bcl.Async" version="1.0.168" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Microsoft.Net.Http" version="2.2.22" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Newtonsoft.Json" version="6.0.4" targetFramework="portable45-net45+win8+wpa81" />
  <package id="OxyPlot.Core" version="1.0.0-unstable1983" targetFramework="portable45-net45+win8+wpa81" />
  <package id="OxyPlot.Xamarin.Forms" version="1.0.0-unstable1983" targetFramework="portable45-net45+win8+wpa81" />
  <package id="PortableRest" version="3.0.1" targetFramework="portable45-net45+win8+wpa81" />
  <package id="SlideOverKit" version="2.1.4" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Splat" version="1.6.2" targetFramework="portable45-net45+win8+wpa81" />
  <package id="sqlite-net-pcl" version="1.1.2" targetFramework="portable45-net45+win8+wpa81" />
  <package id="SQLitePCL.bundle_green" version="0.9.2" targetFramework="portable45-net45+win8+wpa81" />
  <package id="SQLitePCL.raw" version="0.9.2" targetFramework="portable45-net45+win8+wpa81" />
  <package id="Xamarin.Forms" version="2.3.2.127" targetFramework="portable45-net45+win8+wpa81" />
  <package id="XLabs.Core" version="2.3.0-pre02" targetFramework="portable45-net45+win8+wpa81" />
  <package id="XLabs.Forms" version="2.3.0-pre02" targetFramework="portable45-net45+win8+wpa81" />
  <package id="XLabs.IoC" version="2.3.0-pre02" targetFramework="portable45-net45+win8+wpa81" />
  <package id="XLabs.Platform" version="2.3.0-pre02" targetFramework="portable45-net45+win8+wpa81" />
  <package id="XLabs.Serialization" version="2.3.0-pre02" targetFramework="portable45-net45+win8+wpa81" />
</packages>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/RightSideMasterPage.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<t:SlideMenuView xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:t="clr-namespace:SlideOverKit" x:Class="DrMaxMuscle.RightSideMasterPage">
	<t:SlideMenuView.Content>
		<StackLayout Orientation="Horizontal" VerticalOptions="FillAndExpand">
			<StackLayout VerticalOptions="FillAndExpand" WidthRequest="2" BackgroundColor="White"></StackLayout>
			<StackLayout Orientation="Vertical" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
				<StackLayout Orientation="Vertical" VerticalOptions="StartAndExpand">
					<Button x:Name="SubscriptionInfosButton" Text="Subscription infos" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}"></Button>
					<BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand"></BoxView>
					<Button x:Name="EmailUsButton" Text="Email us" Margin="20,20,20,0" Style="{StaticResource slideMenuButtonStyle}"></Button>
					<BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand"></BoxView>
				</StackLayout>
				<StackLayout Orientation="Vertical" VerticalOptions="EndAndExpand">
					<Label x:Name="VersionInfoLabel" FontSize="10" VerticalOptions="End" HorizontalOptions="CenterAndExpand" Margin="0,0,0,20"></Label>
				</StackLayout>
			</StackLayout>
		</StackLayout>
	</t:SlideMenuView.Content>
</t:SlideMenuView>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/RightSideMasterPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using SlideOverKit;
using Xamarin.Forms;

namespace DrMaxMuscle
{
	public partial class RightSideMasterPage : SlideMenuView
	{
		public RightSideMasterPage()
		{
			InitializeComponent();
			// You must set IsFullScreen in this case, 
			// otherwise you need to set HeightRequest, 
			// just like the QuickInnerMenu sample
			this.IsFullScreen = true;
			// You must set WidthRequest in this case
			this.WidthRequest = 250;
			this.MenuOrientations = MenuOrientation.RightToLeft;

			// You must set BackgroundColor, 
			// and you cannot put another layout with background color cover the whole View
			// otherwise, it cannot be dragged on Android
			this.BackgroundColor = Color.White;
			this.Opacity = 0.95;
			// This is shadow view color, you can set a transparent color
			this.BackgroundViewColor = Color.Transparent;

			VersionInfoLabel.Text = DependencyService.Get<IDrMuscleSubcription>().GetBuildVersion();

			SubscriptionInfosButton.Clicked += (object sender, EventArgs e) => {
				HideWithoutAnimations();
				((NavigationPage)Application.Current.MainPage).PushAsync(new SubscriptionPage());
			};

			EmailUsButton.Clicked += (object sender, EventArgs e) => { 
				HideWithoutAnimations();
				Device.OpenUri(new Uri("mailto:<EMAIL>"));
			};
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/SaveWorkoutPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.SaveWorkoutPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand" BackgroundColor="Transparent">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand" BackgroundColor="Transparent">
        <Label Text="Do this now:" TextColor="White"></Label>
        <Label Text="Weight :" TextColor="White"></Label>
        <StackLayout Orientation="Horizontal">
          <Button x:Name="WeightLess" Text="-" Style="{StaticResource buttonStyle}"></Button>
          <Entry x:Name="WeightEntry" Text="10"  HorizontalOptions="FillAndExpand" Keyboard="Numeric" HorizontalTextAlignment="Center"  Style="{StaticResource entryStyle}"></Entry>
          <Button x:Name="WeightMore" Text="+" Style="{StaticResource buttonStyle}"></Button>
        </StackLayout>
        <Label Text="Reps"  TextColor="White"></Label>
        <StackLayout Orientation="Horizontal">
          <Button x:Name="RepsLess" Text="-" Style="{StaticResource buttonStyle}"></Button>
          <Entry x:Name="RepsEntry" Text="10" HorizontalOptions="FillAndExpand" Keyboard="Numeric" HorizontalTextAlignment="Center" Style="{StaticResource entryStyle}"></Entry>
          <Button x:Name="RepsMore" Text="+" Style="{StaticResource buttonStyle}"></Button>
        </StackLayout>
        <Button x:Name="SaveSet" Text="Save" Style="{StaticResource buttonStyle}"></Button>
        <StackLayout HeightRequest="150">
          <ScrollView x:Name="SetsScrollView" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
            <StackLayout Orientation="Vertical" x:Name="SavedSetStackLayout">

            </StackLayout>
          </ScrollView>
        </StackLayout>
      </StackLayout>
      <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
        <Button x:Name="FinishedButton" Text="Finish Exercise" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/SaveWorkoutPage.xaml.cs
================================================
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System;
using System.Collections.Generic;
using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle
{
    public partial class SaveWorkoutPage : ContentPage
    {
        private List<WorkoutLogSerieModel> workoutLogSerieModel = new List<WorkoutLogSerieModel>();
        private decimal currentWeight = 0;
        private int currentReps = 0;
		private decimal weightStep = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? (decimal)1 : (decimal)2.5;
        public SaveWorkoutPage()
        {
            InitializeComponent();

            Title = CurrentLog.Instance.ExerciseLog.Exercice.Label;
            FinishedButton.Clicked += Finished_Clicked;

            currentWeight = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ?
			                              RoundDownToNearest(CurrentLog.Instance.Recommendation.Weight.Kg, 1) :
			                              RoundDownToNearest(CurrentLog.Instance.Recommendation.Weight.Lb, (decimal)2.5);
            currentReps = CurrentLog.Instance.Recommendation.Reps;


            WeightEntry.Text = string.Format("{0:0.0}", currentWeight);

            RepsEntry.Text = string.Format("{0}", currentReps);

            WeightLess.Clicked += WeightLess_Clicked;
            WeightMore.Clicked += WeightMore_Clicked;
            RepsLess.Clicked += RepsLess_Clicked;
            RepsMore.Clicked += RepsMore_Clicked;
			WeightEntry.TextChanged += WeightEntry_TextChanged;
			RepsEntry.TextChanged += RepsEntry_TextChanged;
            SaveSet.Clicked += SaveSet_Clicked;
        }

		void RepsEntry_TextChanged (object sender, TextChangedEventArgs e)
		{
			if (RepsEntry.Text.EndsWith(",") || RepsEntry.Text.EndsWith("."))
				return;
			if (!string.IsNullOrEmpty(RepsEntry.Text))
				currentReps = Convert.ToInt32(RepsEntry.Text.Replace(",","").Replace(".",""));
			else
				currentReps = 0;
		}

		private async void SaveSet_Clicked(object sender, EventArgs e)
        {
            WorkoutLogSerieModel serieModel = new WorkoutLogSerieModel()
            {
                Exercice = new ExerciceModel() { Id = CurrentLog.Instance.ExerciseLog.Exercice.Id },
                Reps = currentReps,
                UserId = CurrentLog.Instance.ExerciseLog.UserId,
                Weight = new MultiUnityWeight(currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value)
            };
            workoutLogSerieModel.Add(serieModel);

            StackLayout setLayout = new StackLayout()
            {
                Orientation = StackOrientation.Horizontal,
                HorizontalOptions = LayoutOptions.StartAndExpand
            };

            Label setLabel = new Label()
            {
                Text = string.Format("{0}. {1:0.0} {2} {3} reps", workoutLogSerieModel.Count, currentWeight, LocalDBManager.Instance.GetDBSetting("massunit").Value, currentReps),
                HorizontalOptions = LayoutOptions.CenterAndExpand,
				TextColor = Color.White
            };
            /*Button deleteSetButton = new Button()
            {
                Text = "Remove"
            };*/

            setLayout.Children.Add(setLabel);
            //setLayout.Children.Add(deleteSetButton);

            SavedSetStackLayout.Children.Add(setLayout);
            await SetsScrollView.ScrollToAsync(setLayout, ScrollToPosition.MakeVisible, true);
        }

		void WeightEntry_TextChanged (object sender, TextChangedEventArgs e)
		{
			try
			{
				if (WeightEntry.Text.EndsWith(",") || WeightEntry.Text.EndsWith("."))
					return;
				if (!string.IsNullOrEmpty(WeightEntry.Text))
					currentWeight = RoundDownToNearest(Convert.ToDecimal(WeightEntry.Text.Replace(".", ",")), weightStep);
				else
					currentWeight = 0;
			}
			catch (Exception ex)
			{
				currentWeight = 0;
				WeightEntry.Text = "0";
			}
		}

		private void RepsMore_Clicked(object sender, EventArgs e)
        {
			RepsEntry.TextChanged -= RepsEntry_TextChanged;
            currentReps += 1;
            RepsEntry.Text = string.Format("{0}", currentReps);
			RepsEntry.TextChanged += RepsEntry_TextChanged;
        }

        private void RepsLess_Clicked(object sender, EventArgs e)
        {
			RepsEntry.TextChanged -= RepsEntry_TextChanged;
            currentReps -= 1;
            RepsEntry.Text = string.Format("{0}", currentReps);
			RepsEntry.TextChanged += RepsEntry_TextChanged;
        }

        private void WeightMore_Clicked(object sender, EventArgs e)
        {
			WeightEntry.TextChanged -= WeightEntry_TextChanged;
			currentWeight += weightStep;
            WeightEntry.Text = string.Format("{0:0.0}", currentWeight);
			WeightEntry.TextChanged += WeightEntry_TextChanged;
        }

        private void WeightLess_Clicked(object sender, EventArgs e)
        {
			WeightEntry.TextChanged -= WeightEntry_TextChanged;
			currentWeight -= weightStep;
            WeightEntry.Text = string.Format("{0:0.0}", currentWeight);
			WeightEntry.TextChanged += WeightEntry_TextChanged;
        }

        private async void Finished_Clicked(object sender, EventArgs e)
        {
			try
			{
				bool result = true;
				foreach (WorkoutLogSerieModel l in workoutLogSerieModel)
				{
					BooleanModel b = await DrMaxMuscleRestClient.Instance.AddWorkoutLogSerie(l);
					result = result && b.Result;
				}
				if (result)
					((NavigationPage)App.Current.MainPage).PushAsync(new EndExercisePage());
			}
			catch (Exception ex)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
        }

        private void AddSerie()
        {
			try
			{
				int serieIndex = workoutLogSerieModel.Count;

				WorkoutLogSerieModel serieModel = new WorkoutLogSerieModel()
				{
					Exercice = new ExerciceModel() { Id = CurrentLog.Instance.ExerciseLog.Exercice.Id },
					Reps = CurrentLog.Instance.Recommendation.Reps,
					UserId = CurrentLog.Instance.ExerciseLog.UserId,
					Weight = CurrentLog.Instance.Recommendation.Weight,
				};
				workoutLogSerieModel.Add(serieModel);

				StackLayout serieLayout = new StackLayout()
				{
					Orientation = StackOrientation.Horizontal,
					HorizontalOptions = LayoutOptions.FillAndExpand
				};

				Entry repsEntry = new Entry() { BindingContext = serieModel };
				repsEntry.SetBinding(Entry.TextProperty, "Reps", BindingMode.TwoWay);

				Label xlabel = new Label() { Text = " x " };

				Entry weightEntry = new Entry();// { BindingContext = serieModel };
				weightEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? serieModel.Weight.Kg.ToString("0.00") : serieModel.Weight.Lb.ToString("0.00");


				Button doneButton = new Button() { Text = "Done" };
				doneButton.Clicked += async (object sender, EventArgs e) =>
				{
					serieModel.Weight = new MultiUnityWeight(Convert.ToDecimal(weightEntry.Text), LocalDBManager.Instance.GetDBSetting("massunit").Value);
					BooleanModel b = await DrMaxMuscleRestClient.Instance.AddWorkoutLogSerie(serieModel);
					if (b.Result)
						((Button)sender).IsVisible = false;
				};

				serieLayout.Children.Add(repsEntry);
				serieLayout.Children.Add(xlabel);
				serieLayout.Children.Add(weightEntry);
				serieLayout.Children.Add(doneButton);
			}
			catch (Exception e)
			{
				UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
            //SeriesStackLayout.Children.Add(serieLayout);
        }

		public static decimal RoundDownToNearest(decimal passednumber, decimal roundto)
		{
			// 105.5 down to nearest 1 = 105
			// 105.5 down to nearest 10 = 100
			// 105.5 down to nearest 7 = 105
			// 105.5 down to nearest 100 = 100
			// 105.5 down to nearest 0.2 = 105.4
			// 105.5 down to nearest 0.3 = 105.3

			//if no rounto then just pass original number back
			if (roundto == 0)
			{
				return passednumber;
			}
			else
			{
				return Math.Floor(passednumber / roundto) * roundto;
			}
		}
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/SubscriptionPage.cs
================================================
﻿using System;

using Xamarin.Forms;

namespace DrMaxMuscle
{
	public class SubscriptionPage : ContentPage
	{
		public SubscriptionPage()
		{
			Content = new StackLayout
			{
				Children = {
					new Label { Text = "Hello ContentPage",  }
				}
			};
		}
	}
}




================================================
FILE: DrMaxMuscle/DrMaxMuscle/SubscriptionPage.xaml
================================================
<?xml version="1.0" encoding="UTF-8"?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" x:Class="DrMaxMuscle.SubscriptionPage">
	<AbsoluteLayout>
      <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
      <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
        <StackLayout VerticalOptions="FillAndExpand">
          <StackLayout x:Name="buttonStacklayout" Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
					<Label Text="Buy now to continue" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
					<Label Text="using Dr. Muscle after your free trial ends" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
					<Button x:Name="BuyLifetimeAccessButton"  Text="Buy Lifetime Access" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand"></Button>
	      </StackLayout>
	    </StackLayout>
	  </StackLayout>
	</AbsoluteLayout>
</ContentPage>



================================================
FILE: DrMaxMuscle/DrMaxMuscle/SubscriptionPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using Xamarin.Forms;

namespace DrMaxMuscle
{
	public partial class SubscriptionPage : ContentPage
	{
		public SubscriptionPage()
		{
			InitializeComponent();

			Device.OnPlatform(iOS : () => {
				Button RestorePurchaseButton = new Button() { 
					Text = "Restore purchases",
					HorizontalOptions = LayoutOptions.FillAndExpand,
					TextColor = Color.White,
					BackgroundColor = Color.Transparent,
					BorderWidth = 2,
					BorderRadius = 5,
					BorderColor = Color.White,
					FontAttributes = FontAttributes.Bold
				};

				RestorePurchaseButton.Clicked += (sender, e) => {
					DependencyService.Get<IDrMuscleSubcription>().RestorePurchases();
				};

				buttonStacklayout.Children.Add(RestorePurchaseButton);
			});

			Device.BeginInvokeOnMainThread(async () =>
			{
				BooleanModel m = await DrMaxMuscleRestClient.Instance.IsV1User();

				if (DependencyService.Get<IDrMuscleSubcription>().IsLifetimeAccessPuchased() ||
					m.Result)
				{
					BuyLifetimeAccessButton.Text = "You already have lifetime access";
					BuyLifetimeAccessButton.Clicked += async (object sender, EventArgs e) =>
					{
						await ((NavigationPage)Application.Current.MainPage).PushAsync(new ChooseYourExercicePage());
					};
				}
				else
				{
					DependencyService.Get<IDrMuscleSubcription>().OnLifetimeAccessPurchased += async delegate {
						await ((NavigationPage)Application.Current.MainPage).PushAsync(new ChooseYourExercicePage());
					};
					BuyLifetimeAccessButton.Text = DependencyService.Get<IDrMuscleSubcription>().GetButtonLabel();
					BuyLifetimeAccessButton.Clicked += async (object sender, EventArgs e) =>
					{
						await DependencyService.Get<IDrMuscleSubcription>().BuyLifetimeAccess();	
					};
				}
			});
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreEmailPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.TellMeMoreEmailPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
      <ScrollView AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
        <StackLayout Orientation="Vertical" VerticalOptions="StartAndExpand" HorizontalOptions="FillAndExpand" Padding="20,75,20,0">
            <Label Text="What's your email?"  FontSize="20" FontAttributes="Bold" TextColor="White"></Label>
            <Entry x:Name="EmailEntry" Placeholder="Tap to enter your email" Keyboard="Email" Style="{StaticResource entryStyle}"></Entry>
            <Button x:Name="NextButton" Text="Continue" Style="{StaticResource buttonStyle}"></Button>
        </StackLayout>
      </ScrollView>
    </StackLayout>
  </AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreEmailPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Acr.UserDialogs;
using DrMaxMuscleWebApiSharedModel;
using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class TellMeMoreEmailPage : ContentPage
    {
        public TellMeMoreEmailPage()
        {
            InitializeComponent();

            NextButton.Clicked += NextButton_Clicked;
        }

        private async void NextButton_Clicked(object sender, EventArgs e)
        {
			BooleanModel result = await DrMaxMuscleWebApiClient.DrMaxMuscleRestClient.Instance.IsEmailAlreadyExist(EmailEntry.Text);
			if (result.Result)
			{
				UserDialogs.Instance.Alert("This email is already registered !", "Already registered", null);
			}
			else
			{
				LocalDBManager.Instance.SetDBSetting("email", EmailEntry.Text);
				await ((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMorePasswordPage(), false);
			}
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreFirstnamePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.TellMeMoreFirstnamePage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
      <ScrollView AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
        <StackLayout Orientation="Vertical" VerticalOptions="StartAndExpand" HorizontalOptions="CenterAndExpand">
          <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand" Padding="20,75,20,0">
            <Label Text="What's your first name?" FontSize="20" FontAttributes="Bold" TextColor="White"></Label>
            <Entry x:Name="FirstnameEntry" Placeholder="Tap to enter your first name" Keyboard="Default" Style="{StaticResource entryStyle}"></Entry>
            <Button x:Name="NextButton" Text="Continue" Style="{StaticResource buttonStyle}"></Button>
          </StackLayout>
        </StackLayout>
      </ScrollView>
    </StackLayout>
  </AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreFirstnamePage.xaml.cs
================================================
﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class TellMeMoreFirstnamePage : ContentPage
    {
        SQLiteConnection database = DependencyService.Get<ISQLite>().GetConnection();

        public TellMeMoreFirstnamePage()
        {
            InitializeComponent();
           
            NextButton.Clicked += NextButton_Clicked;
        }

        private async void NextButton_Clicked(object sender, EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("firstname", FirstnameEntry.Text);
            //LocalDBManager.Instance.SetDBSetting("lastname", LastnameEntry.Text);
            await((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreEmailPage(), false);
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreGenderPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.TellMeMoreGenderPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
        <Label Text="Tell me more about yourself" FontSize="20" FontAttributes="Bold" TextColor="White"></Label>
        <Label Text="(choose one) :" TextColor="White"></Label>
        <Button x:Name="ManButton" Text="Man" Style="{StaticResource buttonStyle}"></Button>
        <Button x:Name="WomanButton" Text="Woman" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreGenderPage.xaml.cs
================================================
﻿using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class TellMeMoreGenderPage : ContentPage
    {
        public TellMeMoreGenderPage()
        {
            InitializeComponent();
			NavigationPage.SetHasNavigationBar(this, false);
            ManButton.Clicked += ManButton_Clicked;
            WomanButton.Clicked += WomanButton_Clicked;
        }

        private async void WomanButton_Clicked(object sender, EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("gender", "Woman");
            await((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreMassUnitPage(), false);
        }

        private async void ManButton_Clicked(object sender, EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("gender", "Man");
            await ((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreMassUnitPage(), false);
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreMassUnitPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.TellMeMoreMassUnitPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
        <Label Text="Choose one: lbs or kg?" FontSize="20" FontAttributes="Bold" TextColor="White"></Label>
        <Button x:Name="LbButton" Text="lbs" Style="{StaticResource buttonStyle}"></Button>
        <Button x:Name="KgButton" Text="kg" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMoreMassUnitPage.xaml.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class TellMeMoreMassUnitPage : ContentPage
    {
        public TellMeMoreMassUnitPage()
        {
            InitializeComponent();

            LbButton.Clicked += LbButton_Clicked;
            KgButton.Clicked += KgButton_Clicked;
        }

        private async void KgButton_Clicked(object sender, EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("massunit", "kg");
            await ((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreFirstnamePage(), false);
        }

        private async void LbButton_Clicked(object sender, EventArgs e)
        {
            LocalDBManager.Instance.SetDBSetting("massunit", "lb");
            await((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreFirstnamePage(), false);
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMorePasswordPage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss"
             x:Class="DrMaxMuscle.TellMeMorePasswordPage">
  <ContentPage.Padding>
    <OnPlatform x:TypeArguments="Thickness" iOS="0, 0, 0, 0" />
  </ContentPage.Padding>
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
        <Entry x:Name="PasswordEntry" Placeholder="Tap to enter your password" IsPassword="true" Style="{StaticResource entryStyle}"></Entry>
        <Entry x:Name="ConfirmPasswordEntry" Placeholder="Tap to confirm your password" IsPassword="true" Style="{StaticResource entryStyle}"></Entry>
        <Button x:Name="NextButton" Text="Continue" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">
    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/TellMeMorePasswordPage.xaml.cs
================================================
using Acr.UserDialogs;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class TellMeMorePasswordPage : ContentPage
    {
        public TellMeMorePasswordPage()
        {
            InitializeComponent();

            NextButton.Clicked += NextButton_Clicked;
        }

        private async void NextButton_Clicked(object sender, EventArgs e)
        {
			try
			{
				if (PasswordEntry.Text != ConfirmPasswordEntry.Text)
				{
					UserDialogs.Instance.Alert("Password confirmation doesn't match !");
					return;
				}

				LocalDBManager.Instance.SetDBSetting("password", PasswordEntry.Text);
				RegisterModel registerModel = new RegisterModel();

				registerModel.Firstname = LocalDBManager.Instance.GetDBSetting("firstname").Value;
				//registerModel.Lastname = LocalDBManager.Instance.GetDBSetting("lastname").Value;
				registerModel.EmailAddress = LocalDBManager.Instance.GetDBSetting("email").Value;
				registerModel.SelectedGender = LocalDBManager.Instance.GetDBSetting("gender").Value;
				registerModel.MassUnit = LocalDBManager.Instance.GetDBSetting("massunit").Value;
				registerModel.Password = PasswordEntry.Text;
				registerModel.ConfirmPassword = ConfirmPasswordEntry.Text;

				BooleanModel registerResponse = await DrMaxMuscleRestClient.Instance.RegisterUser(registerModel);

				if (registerResponse.Result)
				{
					LoginSuccessResult lr = await DrMaxMuscleRestClient.Instance.Login(new LoginModel()
					{
						Username = LocalDBManager.Instance.GetDBSetting("email").Value,
						Password = ConfirmPasswordEntry.Text
					});
					LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
					LocalDBManager.Instance.SetDBSetting("token_expires_date", DateTime.Now.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());

					string welcomeMsg = string.Format("Hello {0} !",
						LocalDBManager.Instance.GetDBSetting("firstname").Value,
						LocalDBManager.Instance.GetDBSetting("email").Value);
					UserDialogs.Instance.ShowSuccess(welcomeMsg, 8000);
					UserInfosModel uim = await DrMaxMuscleRestClient.Instance.GetUserInfo();

					LocalDBManager.Instance.SetDBSetting("email", uim.Email);
					LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
					LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
					LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
					LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
					LocalDBManager.Instance.SetDBSetting("password", PasswordEntry.Text);
					LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());

					await ((NavigationPage)App.Current.MainPage).PushAsync(new ChooseYourExercicePage(), false);
				}
			}
			catch (Exception ex)
			{
				await UserDialogs.Instance.AlertAsync("Please check your Internet connection and try again. If this problem persists, please contact support.", "Error !");
			}
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Timer.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    internal delegate void TimerCallback(object state);

    internal sealed class Timer : CancellationTokenSource, IDisposable
    {
        public Timer(TimerCallback callback, object state, int dueTime, int period)
        {
            Task.Delay(dueTime, Token).ContinueWith(async (t, s) =>
            {
                var tuple = (Tuple<TimerCallback, object>)s;

                while (true)
                {
                    if (IsCancellationRequested)
                        break;
                    Task.Run(() => tuple.Item1(tuple.Item2));
                    await Task.Delay(period);
                }

            }, Tuple.Create(callback, state), CancellationToken.None,
                TaskContinuationOptions.ExecuteSynchronously | TaskContinuationOptions.OnlyOnRanToCompletion,
                TaskScheduler.Default);
        }

        public new void Dispose() { base.Cancel(); }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/TODO.txt
================================================
﻿- Création du compte gratuite, essai de 14 jours sans restrictions

- Réinitialisation du mot de passe par email

- Background sur les pages aérées

- fond noir texte blanc


================================================
FILE: DrMaxMuscle/DrMaxMuscle/WelcomePage.xaml
================================================
<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.WelcomePage"
            xmlns:xfg="clr-namespace:XFGloss;assembly=XFGloss">
  <AbsoluteLayout>
    <Image AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" Source="Background2.png" Aspect="AspectFill"/>
    <StackLayout HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand" Padding="20,0,20,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1">
    <StackLayout VerticalOptions="FillAndExpand">
      <StackLayout Orientation="Vertical" VerticalOptions="CenterAndExpand" HorizontalOptions="CenterAndExpand">
        <Label Text="Welcome to" FontAttributes="Bold" FontSize="20" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
        <Label Text="Dr. Muscle" FontAttributes="Bold" FontSize="36" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
        <Label Text="This app helps you build muscle" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
        <Button x:Name="GetStartedButton" Text="Create new account" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
        <StackLayout Orientation="Horizontal" Padding="20,20,20,20">
          <StackLayout Orientation="Vertical" VerticalOptions="Center" HorizontalOptions="FillAndExpand">
            <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand"></BoxView>
          </StackLayout>
          <StackLayout Orientation="Vertical" VerticalOptions="Center">
            <Label Text="Or"  TextColor="White"></Label>
          </StackLayout>
          <StackLayout Orientation="Vertical" VerticalOptions="Center" HorizontalOptions="FillAndExpand">
            <BoxView Color="Gray" HeightRequest="1" Opacity="0.5" HorizontalOptions="FillAndExpand"></BoxView>
          </StackLayout>
        </StackLayout>
        <Label Text="Log in with your email address" HorizontalOptions="CenterAndExpand" TextColor="White"></Label>
        <Entry x:Name="EmailEntry" Placeholder="Tap to enter your email" Keyboard="Email" Style="{StaticResource entryStyle}"></Entry>
        <Entry x:Name="PasswordEntry" Placeholder="Tap to enter your password" IsPassword="True" Style="{StaticResource entryStyle}"></Entry>
        <Button x:Name="LoginButton" Text="Login" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></Button>
      </StackLayout>
    </StackLayout>
    <StackLayout Orientation="Horizontal" VerticalOptions="EndAndExpand" Padding="0,0,0,20">

    </StackLayout>
  </StackLayout></AbsoluteLayout>
</ContentPage>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/WelcomePage.xaml.cs
================================================
﻿using System;
using Acr.UserDialogs;
using DrMaxMuscleWebApiClient;
using DrMaxMuscleWebApiSharedModel;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Xamarin.Forms;

namespace DrMaxMuscle
{
    public partial class WelcomePage : ContentPage
    {
        public WelcomePage()
        {
            InitializeComponent();
            //LearnMoreButton.Clicked += LearnMoreButton_Clicked;
            GetStartedButton.Clicked += GetStartedButton_Clicked;
            LoginButton.Clicked += LoginButton_Clicked;
            NavigationPage.SetHasNavigationBar(this, false);
			DependencyService.Get<IDrMuscleSubcription>().Init();
        }

        private async void LoginButton_Clicked(object sender, EventArgs e)
        {
			LoginSuccessResult lr = await DrMaxMuscleRestClient.Instance.Login(new LoginModel()
			{
				Username = EmailEntry.Text,
				Password = PasswordEntry.Text
			});

			if (lr != null)
			{
				DateTime current = DateTime.Now;


				UserInfosModel uim = await DrMaxMuscleRestClient.Instance.GetUserInfo();

				LocalDBManager.Instance.SetDBSetting("email", uim.Email);
				LocalDBManager.Instance.SetDBSetting("firstname", uim.Firstname);
				LocalDBManager.Instance.SetDBSetting("lastname", uim.Lastname);
				LocalDBManager.Instance.SetDBSetting("gender", uim.Gender);
				LocalDBManager.Instance.SetDBSetting("massunit", uim.MassUnit);
				LocalDBManager.Instance.SetDBSetting("password", PasswordEntry.Text);
				LocalDBManager.Instance.SetDBSetting("token", lr.access_token);
				LocalDBManager.Instance.SetDBSetting("token_expires_date", current.Add(TimeSpan.FromSeconds((double)lr.expires_in + 1)).Ticks.ToString());
				LocalDBManager.Instance.SetDBSetting("creation_date", uim.CreationDate.Ticks.ToString());

				await ((NavigationPage)Application.Current.MainPage).PushAsync(new ChooseYourExercicePage());
			}
			else
			{
				UserDialogs.Instance.Alert(new AlertConfig() { Message = "Login error occur, please check your email and password", Title = "Unable to login" });
			}
        }

        private async void GetStartedButton_Clicked(object sender, EventArgs e)
        {
            await ((NavigationPage)Application.Current.MainPage).PushAsync(new TellMeMoreGenderPage());
			//Navigation.PushModalAsync(new TellMeMoreGenderPage(), false);
        }

        private async void LearnMoreButton_Clicked(object sender, EventArgs e)
        {
            await((NavigationPage)Application.Current.MainPage).PushAsync(new LearnMorePage());
			//Navigation.PushModalAsync(new LearnMorePage(), false);
		}
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Models/InAppProduct.cs
================================================
﻿using System;
using System.Collections.Generic;

namespace DrMaxMuscle.Models
{
	public class InAppProduct
	{
		/// <summary>
		/// The product ID for the product.
		/// </summary>
		public string ProductId { get; set; }

		/// <summary>
		/// Value must be “inapp” for an in-app product or "subs" for subscriptions.
		/// </summary>
		public string Type { get; set; }

		/// <summary>
		/// 	Formatted price of the item, including its currency sign. The price does not include tax.
		/// </summary>
		public string Price { get; set; }

		public string IconSource { get; set; }

		/// <summary>
		/// Price in micro-units, where 1,000,000 micro-units equal one unit of the currency. 
		/// 
		/// For example, if price is "€7.99", price_amount_micros is "7990000".
		/// </summary>
		public int PriceAmountMicros { get; set; }

		/// <summary>
		/// ISO 4217 currency code for price. 
		/// 
		/// For example, if price is specified in British pounds sterling, price_currency_code is "GBP".
		/// </summary>
		public string PriceCurrencyCode { get; set; }

		/// <summary>
		///	Title of the product.
		/// </summary>
		public string Title { get; set; }

		/// <summary>
		/// Description of the product.
		/// </summary>
		public string Description { get; set; }
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Models/InAppPurchase.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace DrMaxMuscle.Models
{
	[DataContract]
	public class InAppPurchase
	{
		/// <summary>
		/// A unique order identifier for the transaction. 
		/// 
		/// This identifier corresponds to the Google payments order ID.
		/// </summary>
		public string OrderId { get; set; }

		/// <summary>
		/// Indicates whether the subscription renews automatically. 
		/// 
		/// If true, the subscription is active, and will automatically renew on the 
		/// next billing date. If false, indicates that the user has canceled the subscription. 
		/// The user has access to subscription content until the next billing date and will 
		/// lose access at that time unless they re-enable automatic renewal (or manually renew, 
		/// as described in Manual Renewal). If you offer a grace period, this value remains set 
		/// to true for all subscriptions, as long as the grace period has not lapsed. 
		/// The next billing date is extended dynamically every day until the end of the grace 
		/// period or until the user fixes their payment method.
		/// </summary>
		public bool AutoRenewing { get; set; }

		/// <summary>
		///	The application package from which the purchase originated.
		/// </summary>
		public string PackageName { get; set; }

		/// <summary>
		/// The item's product identifier. 
		/// 
		/// Every item has a product ID, which you must specify in the application's product list 
		/// on the Google Play Developer Console.
		/// </summary>
		[DataMember]
		public string ProductId { get; set; }

		/// <summary>
		/// The time the product was purchased, in milliseconds since the epoch (Jan 1, 1970).
		/// </summary>
		public DateTime PurchaseTime { get; set; }

		/// <summary>
		/// The purchase state of the order. 
		/// 
		/// Possible values are 0 (purchased), 1 (canceled), or 2 (refunded).
		/// </summary>
		public int PurchaseState { get; set; }

		/// <summary>
		/// A developer-specified string that contains supplemental information about an order. 
		/// 
		/// You can specify a value for this field when you make a getBuyIntent request.
		/// </summary>
		public string DeveloperPayload { get; set; }

		/// <summary>
		/// A token that uniquely identifies a purchase for a given item and user pair.
		/// </summary>
		public string PurchaseToken { get; set; }
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Models/InAppPurchaseList.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace DrMaxMuscle.Models
{
	[DataContract]
	public class InAppPurchaseList
	{
		[DataMember]
		public List<InAppPurchase> Purchases { get; set; }
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Properties/AssemblyInfo.cs
================================================
﻿using System.Resources;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

// General Information about an assembly is controlled through the following 
// set of attributes. Change these attribute values to modify the information
// associated with an assembly.
[assembly: AssemblyTitle("DrMaxMuscle")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("")]
[assembly: AssemblyProduct("DrMaxMuscle")]
[assembly: AssemblyCopyright("Copyright ©  2014")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]
[assembly: NeutralResourcesLanguage("en")]

// Version information for an assembly consists of the following four values:
//
//      Major Version
//      Minor Version 
//      Build Number
//      Revision
//
// You can specify all the values or you can default the Build and Revision Numbers 
// by using the '*' as shown below:
// [assembly: AssemblyVersion("1.0.*")]
[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Properties/Resources.Designer.cs
================================================
﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DrMaxMuscle.Properties {
    using System;
    using System.Reflection;
    
    
    /// <summary>
    ///   Une classe de ressource fortement typée destinée, entre autres, à la consultation des chaînes localisées.
    /// </summary>
    // Cette classe a été générée automatiquement par la classe StronglyTypedResourceBuilder
    // à l'aide d'un outil, tel que ResGen ou Visual Studio.
    // Pour ajouter ou supprimer un membre, modifiez votre fichier .ResX, puis réexécutez ResGen
    // avec l'option /str ou régénérez votre projet VS.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Retourne l'instance ResourceManager mise en cache utilisée par cette classe.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DrMaxMuscle.Properties.Resources", typeof(Resources).GetTypeInfo().Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Remplace la propriété CurrentUICulture du thread actuel pour toutes
        ///   les recherches de ressources à l'aide de cette classe de ressource fortement typée.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
    }
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/Properties/Resources.resx
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
</root>


================================================
FILE: DrMaxMuscle/DrMaxMuscle/Services/IInAppService.cs
================================================
﻿using System.Collections.Generic;
using DrMaxMuscle.Models;

namespace DrMaxMuscle.Services
{
	public delegate void OnQueryInventoryDelegate();

	public delegate void OnPurchaseProductDelegate();

	public delegate void OnRestoreProductsDelegate();

	public delegate void OnQueryInventoryErrorDelegate(int responseCode, IDictionary<string, object> skuDetails);

	public delegate void OnPurchaseProductErrorDelegate(int responseCode, string sku);

	public delegate void OnRestoreProductsErrorDelegate(int responseCode, IDictionary<string, object> skuDetails);

	public delegate void OnUserCanceledDelegate();

	public delegate void OnInAppBillingProcessingErrorDelegate(string message);

	public delegate void OnInvalidOwnedItemsBundleReturnedDelegate(IDictionary<string, object> ownedItems);

	public delegate void OnPurchaseFailedValidationDelegate(InAppPurchase purchase, string purchaseData, string purchaseSignature);

	public interface IInAppService
	{
		string PracticeModeProductId { get; }

		/// <summary>
		/// Starts the setup of this Android application by connection to the Google Play Service
		/// to handle In-App purchases.
		/// </summary>
		void Initialize();

		/// <summary>
		/// Queries the inventory asynchronously and returns a list of Xamarin.Android.InAppBilling.Products
		/// matching the given list of SKU numbers.
		/// </summary>
		/// <param name="skuList">Sku list.</param>
		/// <param name="itemType">The Xamarin.Android.InAppBilling.ItemType of product being queried.</param>
		/// <returns>List of Xamarin.Android.InAppBilling.Products matching the given list of SKUs.
		/// </returns>
		void QueryInventory();

		/// <summary>
		/// Buys the given Xamarin.Android.InAppBilling.Product
		/// 
		/// This method automatically generates a unique GUID and attaches it as the
		/// developer payload for this purchase.
		/// </summary>
		/// <param name="product">The Xamarin.Android.InAppBilling.Product representing the item the users
		/// wants to purchase.</param>
		void PurchaseProduct(string productId);

		void RestoreProducts();

		/// <summary>
		/// For testing purposes only.
		/// </summary>
		void RefundProduct();

		/// <summary>
		/// Occurs when a query inventory transactions completes successfully with Google Play Services.
		/// </summary>
		event OnQueryInventoryDelegate OnQueryInventory;

		/// <summary>
		/// Occurs after a product has been successfully purchased Google Play.
		/// 
		/// This event is fired after a OnProductPurchased which is raised when the user
		/// successfully logs an intent to purchase with Google Play.
		/// </summary>
		event OnPurchaseProductDelegate OnPurchaseProduct;

		/// <summary>
		/// Occurs after a successful products restored transactions with Google Play.
		/// </summary>
		event OnRestoreProductsDelegate OnRestoreProducts;

		/// <summary>
		/// Occurs when there is an error querying inventory from Google Play Services.
		/// </summary>
		event OnQueryInventoryErrorDelegate OnQueryInventoryError;

		/// <summary>
		/// Occurs when the user attempts to buy a product and there is an error.
		/// </summary>
		event OnPurchaseProductErrorDelegate OnPurchaseProductError;

		/// <summary>
		/// Occurs when the user attempts to restore products and there is an error.
		/// </summary>
		event OnRestoreProductsErrorDelegate OnRestoreProductsError;

		/// <summary>
		/// Occurs when on user canceled.
		/// </summary>
		event OnUserCanceledDelegate OnUserCanceled;

		/// <summary>
		/// Occurs when there is an in app billing procesing error.
		/// </summary>
		event OnInAppBillingProcessingErrorDelegate OnInAppBillingProcesingError;

		/// <summary>
		/// Raised when Google Play Services returns an invalid bundle from previously
		/// purchased items
		/// </summary>
		event OnInvalidOwnedItemsBundleReturnedDelegate OnInvalidOwnedItemsBundleReturned;

		/// <summary>
		/// Occurs when a previously purchased product fails to validate.
		/// </summary>
		event OnPurchaseFailedValidationDelegate OnPurchaseFailedValidation;
	}
}


================================================
FILE: DrMaxMuscle/DrMaxMuscle/ViewModels/InAppViewModel.cs
================================================
﻿using DrMaxMuscle.Models;
using DrMaxMuscle.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Windows.Input;
using Xamarin.Forms;

namespace DrMaxMuscle.ViewModels
{
	public class InAppViewModel : ViewModelBase
	{
		ObservableCollection<InAppProduct> _products;
		ObservableCollection<InAppPurchase> _purchases;
		InAppPurchaseList _purchaseList;

		public InAppViewModel()
		{
			TheInAppService = DependencyService.Get<IInAppService>();
			TheInAppService.OnQueryInventory += OnQueryInventory;
			TheInAppService.OnPurchaseProduct += OnPurchaseProduct;
			TheInAppService.OnRestoreProducts += OnRestoreProducts;
			TheInAppService.Initialize();

			_purchases = new ObservableCollection<InAppPurchase>();
			_purchaseList = new InAppPurchaseList();

			InitializeProducts();

			QueryCommand = new Command<InAppProduct>(
				execute: (product) =>
				{
					TheInAppService.QueryInventory();
				});

			PurchaseCommand = new Command<InAppProduct>(
				execute: (product) =>
				{
					TheInAppService.PurchaseProduct(product.ProductId);
				});

			RestoreCommand = new Command<InAppProduct>(
				execute: (product) =>
				{
					TheInAppService.RestoreProducts();
				});
		}

		public IInAppService TheInAppService { get; private set; }

		void OnQueryInventory()
		{
			throw new System.NotImplementedException();
		}

		void OnPurchaseProduct()
		{
			throw new System.NotImplementedException();
		}
		void OnRestoreProducts()
		{
			throw new System.NotImplementedException();
		}

		public ObservableCollection<InAppProduct> Products
		{
			private set { SetProperty(ref _products, value); }
			get { return _products; }
		}

		public ObservableCollection<InAppPurchase> Purchases
		{
			private set { SetProperty(ref _purchases, value); }
			get { return _purchases; }
		}

		public ICommand QueryCommand { private set; get; }

		public ICommand PurchaseCommand { private set; get; }

		public ICommand RestoreCommand { private set; get; }

		public void SaveState(IDictionary<string, object> dictionary)
		{
			_purchaseList.Purchases = new List<InAppPurchase>(_purchases);

			using (MemoryStream ms = new MemoryStream())
			{
				DataContractJsonSerializer ser = new DataContractJsonSerializer(typeof(InAppPurchaseList));
				ser.WriteObject(ms, _purchaseList);
				ms.Position = 0;
				using (var sr = new StreamReader(ms))
				{
					var purchases = sr.ReadToEnd();
					dictionary["Purchases"] = purchases;
				}
			}
		}

		public void RestoreState(IDictionary<string, object> dictionary)
		{
			string purchases = GetDictionaryEntry(dictionary, "Purchases", string.Empty);
			if (purchases != string.Empty)
			{
				DataContractJsonSerializer ser = new DataContractJsonSerializer(typeof(InAppPurchaseList));
				using (MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(purchases)))
				{
					InAppPurchaseList purchaseList = (InAppPurchaseList)ser.ReadObject(ms);
					Purchases = new ObservableCollection<InAppPurchase>(purchaseList.Purchases);
				}
			}
		}

		public T GetDictionaryEntry<T>(IDictionary<string, object> dictionary,
										string key, T defaultValue)
		{
			if (dictionary.ContainsKey(key))
				return (T)dictionary[key];

			return defaultValue;
		}

		private void InitializeProducts()
		{
			_products = new ObservableCollection<InAppProduct>();

			_products.Add(new InAppProduct
			{
				Title = "Small Monkey",
				Description = "Keyboard companion",
				IconSource = "monkey1.png"
			});
			_products.Add(new InAppProduct
			{
				Title = "Medium Monkey",
				Description = "Plush and cuddly",
				IconSource = "monkey2.png"
			});
			_products.Add(new InAppProduct
			{
				Title = "Large Monkey",
				Description = "Monitor buddy",
				IconSource = "monkey3.png"
			});
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle/ViewModels/ViewModelBase.cs
================================================
﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DrMaxMuscle.ViewModels
{
	public class ViewModelBase : INotifyPropertyChanged
	{
		public event PropertyChangedEventHandler PropertyChanged;

		protected bool SetProperty<T>(ref T storage, T value,
									  [CallerMemberName] string propertyName = null)
		{
			if (Object.Equals(storage, value))
				return false;

			storage = value;
			OnPropertyChanged(propertyName);
			return true;
		}

		protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
		{
			PropertyChangedEventHandler handler = PropertyChanged;
			if (handler != null)
			{
				handler(this, new PropertyChangedEventArgs(propertyName));
			}
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/app.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Primitives" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="XLabs.Serialization" publicKeyToken="d65109b36e5040e4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.5782.15703" newVersion="2.0.5782.15703" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>


================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/DrMaxMuscle.Droid.csproj
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{26267A07-7D2A-4608-9D4B-1E432B5D6FA6}</ProjectGuid>
    <ProjectTypeGuids>{EFBA0AD7-5A72-4C68-AF49-83D382785DCF};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DrMaxMuscle.Droid</RootNamespace>
    <AssemblyName>DrMaxMuscle.Droid</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <AndroidApplication>true</AndroidApplication>
    <AndroidResgenFile>Resources\Resource.Designer.cs</AndroidResgenFile>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <AndroidManifest>Properties\AndroidManifest.xml</AndroidManifest>
    <AndroidUseLatestPlatformSdk>true</AndroidUseLatestPlatformSdk>
    <TargetFrameworkVersion>v6.0</TargetFrameworkVersion>
    <AndroidSupportedAbis>armeabi;armeabi-v7a;x86</AndroidSupportedAbis>
    <AndroidStoreUncompressedFileExtensions />
    <MandroidI18n />
    <JavaMaximumHeapSize />
    <JavaOptions />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <ReleaseVersion>2.0</ReleaseVersion>
    <SynchReleaseVersion>false</SynchReleaseVersion>
    <AndroidUseSharedRuntime>false</AndroidUseSharedRuntime>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>True</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>None</AndroidLinkMode>
    <EmbedAssembliesIntoApk>True</EmbedAssembliesIntoApk>
    <Debugger>Xamarin</Debugger>
    <AndroidSupportedAbis>armeabi-v7a;x86</AndroidSupportedAbis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>
    </DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AndroidLinkMode>SdkOnly</AndroidLinkMode>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Acr.Support.Android, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Acr.Support.2.1.0\lib\MonoAndroid10\Acr.Support.Android.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Acr.UserDialogs, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Acr.UserDialogs.6.2.3\lib\MonoAndroid10\Acr.UserDialogs.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Acr.UserDialogs.Interface, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Acr.UserDialogs.6.2.3\lib\MonoAndroid10\Acr.UserDialogs.Interface.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="AndHUD, Version=1.2.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AndHUD.1.2.0\lib\MonoAndroid\AndHUD.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ExifLib, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ExifLib.PCL.1.0.1\lib\portable-net45+sl50+win+WindowsPhoneApp81+wp80+Xamarin.iOS10+MonoAndroid10+MonoTouch10\ExifLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FormsViewGroup, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.*********\lib\MonoAndroid10\FormsViewGroup.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Mono.Android" />
    <Reference Include="Mono.Android.Export" />
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=9.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.9.0.1\lib\portable-net45+wp80+win8+wpa81\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot, Version=*******, Culture=neutral, PublicKeyToken=638079a8f0bd61e9, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Core.1.0.0-unstable1983\lib\portable-net45+netcore45+wpa81+wp8+MonoAndroid1+MonoTouch1+Xamarin.iOS10\OxyPlot.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Android, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Android.1.0.0-unstable1983\lib\MonoAndroid10\OxyPlot.Xamarin.Android.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Forms, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.1.0.0-unstable1983\lib\MonoAndroid10\OxyPlot.Xamarin.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="OxyPlot.Xamarin.Forms.Platform.Android, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\OxyPlot.Xamarin.Forms.1.0.0-unstable1983\lib\MonoAndroid10\OxyPlot.Xamarin.Forms.Platform.Android.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Plugin.CurrentActivity, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Plugin.CurrentActivity.1.0.1\lib\MonoAndroid10\Plugin.CurrentActivity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SlideOverKit, Version=1.0.6135.18790, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SlideOverKit.2.1.4\lib\MonoAndroid10\SlideOverKit.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SlideOverKit.Droid, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SlideOverKit.2.1.4\lib\MonoAndroid10\SlideOverKit.Droid.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Splat, Version=1.6.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Splat.1.6.2\lib\monoandroid\Splat.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLite-net, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\sqlite-net-pcl.1.1.2\lib\portable-net45+wp8+wpa81+win8+MonoAndroid10+MonoTouch10+Xamarin.iOS10\SQLite-net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLitePCL.batteries, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCL.bundle_green.0.9.2\lib\MonoAndroid\SQLitePCL.batteries.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLitePCL.raw, Version=0.9.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCL.raw.0.9.2\lib\MonoAndroid\SQLitePCL.raw.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SQLitePCLPlugin_esqlite3, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SQLitePCL.plugin.sqlite3.android.0.9.2\lib\MonoAndroid\SQLitePCLPlugin_esqlite3.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
    <Reference Include="Xamarin.Android.Support.Animated.Vector.Drawable, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.Animated.Vector.Drawable.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.Animated.Vector.Drawable.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Design, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.Design.23.3.0\lib\MonoAndroid43\Xamarin.Android.Support.Design.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v4, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.v4.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.v4.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.AppCompat, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.AppCompat.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.v7.AppCompat.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.CardView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.CardView.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.v7.CardView.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.MediaRouter, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.MediaRouter.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.v7.MediaRouter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.v7.RecyclerView, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.v7.RecyclerView.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.v7.RecyclerView.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Android.Support.Vector.Drawable, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Android.Support.Vector.Drawable.23.3.0\lib\MonoAndroid403\Xamarin.Android.Support.Vector.Drawable.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Core, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.*********\lib\MonoAndroid10\Xamarin.Forms.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Platform, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.*********\lib\MonoAndroid10\Xamarin.Forms.Platform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Platform.Android, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.*********\lib\MonoAndroid10\Xamarin.Forms.Platform.Android.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.Forms.Xaml, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Xamarin.Forms.*********\lib\MonoAndroid10\Xamarin.Forms.Xaml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XFGloss, Version=1.0.3.35, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Ansuria.XFGloss.1.0.3.35\lib\MonoAndroid10\XFGloss.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XFGloss.Droid, Version=1.0.3.35, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Ansuria.XFGloss.1.0.3.35\lib\MonoAndroid10\XFGloss.Droid.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Core, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Core.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Forms, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Forms.2.3.0-pre02\lib\monoandroid\XLabs.Forms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Forms.Droid, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Forms.2.3.0-pre02\lib\monoandroid\XLabs.Forms.Droid.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.IOC, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.IoC.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.IOC.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Platform, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Platform.2.3.0-pre02\lib\MonoAndroid\XLabs.Platform.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Platform.Droid, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Platform.2.3.0-pre02\lib\MonoAndroid\XLabs.Platform.Droid.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="XLabs.Serialization, Version=2.3.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\XLabs.Serialization.2.3.0-pre02\lib\portable-net45+netcore45+wpa81+wp8+monoandroid+monotouch+xamarinios10+xamarinmac\XLabs.Serialization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Xamarin.InAppBilling">
      <HintPath>..\..\DrMaxMuscle2_AppOnly\Components\xamarin.inappbilling-2.2\lib\android\Xamarin.InAppBilling.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="GenericButtonRenderer.cs" />
    <Compile Include="MainActivity.cs" />
    <Compile Include="Resource.cs" />
    <Compile Include="Resources\Resource.Designer.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SplashActivity.cs" />
    <Compile Include="SQLite_Android.cs" />
    <Compile Include="SubscriptionPageRenderer.cs" />
    <Compile Include="DrMuscleSubscription_Droid.cs" />
    <Compile Include="PurchaseManager.cs" />
    <Compile Include="MainApplication.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Resources\AboutResources.txt" />
    <None Include="Assets\AboutAssets.txt" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\icon.png" />
    <AndroidResource Include="Resources\drawable-hdpi\icon.png" />
    <AndroidResource Include="Resources\drawable-xhdpi\icon.png" />
    <AndroidResource Include="Resources\drawable-xxhdpi\icon.png" />
    <AndroidResource Include="Resources\drawable\Background2.png" />
    <AndroidResource Include="Resources\drawable\splash_screen.xml" />
    <AndroidResource Include="Resources\values\colors.xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\AndroidManifest.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\layout\Tabbar.axml" />
    <AndroidResource Include="Resources\layout\Toolbar.axml" />
    <AndroidResource Include="Resources\values\styles.xml" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\Background.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\red.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\orange.png" />
  </ItemGroup>
  <ItemGroup>
    <AndroidResource Include="Resources\drawable\green.png" />
  </ItemGroup>
  <ItemGroup>
    <XamarinComponentReference Include="xamarin.inappbilling">
      <Version>2.2</Version>
      <Visible>False</Visible>
    </XamarinComponentReference>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Android\Xamarin.Android.CSharp.targets" />
  <Import Project="..\..\packages\Xamarin.Android.Support.Vector.Drawable.23.3.0\build\Xamarin.Android.Support.Vector.Drawable.targets" Condition="Exists('..\..\packages\Xamarin.Android.Support.Vector.Drawable.23.3.0\build\Xamarin.Android.Support.Vector.Drawable.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Ce projet fait référence à des packages NuGet qui sont manquants sur cet ordinateur. Utilisez l'option de restauration des packages NuGet pour les télécharger. Pour plus d'informations, consultez http://go.microsoft.com/fwlink/?LinkID=322105. Le fichier manquant est : {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Xamarin.Android.Support.Vector.Drawable.23.3.0\build\Xamarin.Android.Support.Vector.Drawable.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Android.Support.Vector.Drawable.23.3.0\build\Xamarin.Android.Support.Vector.Drawable.targets'))" />
    <Error Condition="!Exists('..\..\packages\Xamarin.Forms.*********\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Xamarin.Forms.*********\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets'))" />
  </Target>
  <Import Project="..\..\packages\Xamarin.Forms.*********\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets" Condition="Exists('..\..\packages\Xamarin.Forms.*********\build\portable-win+net45+wp80+win81+wpa81+MonoAndroid10+MonoTouch10+Xamarin.iOS10\Xamarin.Forms.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
     Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>


================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/DrMuscleSubscription_Droid.cs
================================================
﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Android.App;
using Android.Content;
using Android.Content.PM;
using DrMaxMuscle;
using DrMaxMuscle.Droid;
using Plugin.CurrentActivity;
using Xamarin.Forms;

[assembly: Dependency(typeof(DrMuscleSubscription_Droid))]
namespace DrMaxMuscle.Droid
{
	public class DrMuscleSubscription_Droid : IDrMuscleSubcription
	{
		public DrMuscleSubscription_Droid()
		{
			PurchaseManager.Instance.OnLifetimeAccessPurchased += Instance_OnLifetimeAccessPurchased; 
		}
		public event LifetimeAccessPurchased OnLifetimeAccessPurchased;

		void Instance_OnLifetimeAccessPurchased()
		{
			if (OnLifetimeAccessPurchased != null)
				OnLifetimeAccessPurchased();
		}

		public async Task BuyLifetimeAccess()
		{
			PurchaseManager.Instance.BuyLifetimeAccess();
		}

		public string GetButtonLabel()
		{
			if (PurchaseManager.Instance.Products != null)
			{
				return string.Format("Buy lifetime access ({0})", PurchaseManager.Instance.Products.First(l => l.ProductId == "drmuscle.lifetimeaccess.nonconsumable").Price);
			}

			return "";
		}

		public bool IsLifetimeAccessPuchased()
		{
			if (PurchaseManager.Instance.Purchases != null)
				return PurchaseManager.Instance.Purchases.Any(l => l.ProductId == "drmuscle.lifetimeaccess.nonconsumable");


			return false;
		}

		public void Init()
		{
			PurchaseManager i = PurchaseManager.Instance;
		}

		public void RestorePurchases()
		{
			throw new NotImplementedException();
		}

		public string GetBuildVersion()
		{
			Context context = Forms.Context;
			PackageManager manager = context.PackageManager;
			PackageInfo i = manager.GetPackageInfo(context.PackageName, 0);
			return i.VersionName;
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/GenericButtonRenderer.cs
================================================
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DrMaxMuscle.Droid;
using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Xamarin.Forms;
using Android.Graphics.Drawables;
using System.ComponentModel;
using Xamarin.Forms.Platform.Android;

[assembly: ExportRenderer(typeof(Xamarin.Forms.Button), typeof(GenericButtonRenderer))]
namespace DrMaxMuscle.Droid
{
    public class GenericButtonRenderer : Xamarin.Forms.Platform.Android.ButtonRenderer
    {
		private GradientDrawable _normal, _pressed;

		protected override void OnElementChanged(Xamarin.Forms.Platform.Android.ElementChangedEventArgs<Xamarin.Forms.Button> e)
		{
			base.OnElementChanged(e);

			if (Control != null)
			{
				var button = e.NewElement;

				// Create a drawable for the button's normal state
				_normal = new Android.Graphics.Drawables.GradientDrawable();

				if (button.BackgroundColor.R == -1.0 && button.BackgroundColor.G == -1.0 && button.BackgroundColor.B == -1.0)
					_normal.SetColor(Android.Graphics.Color.ParseColor("#ff2c2e2f"));
				else
					_normal.SetColor(button.BackgroundColor.ToAndroid());

				_normal.SetStroke((int)button.BorderWidth, button.BorderColor.ToAndroid());
				_normal.SetCornerRadius(button.BorderRadius);

				// Create a drawable for the button's pressed state
				_pressed = new Android.Graphics.Drawables.GradientDrawable();
				var highlight = Context.ObtainStyledAttributes(new int[] { Android.Resource.Attribute.ColorActivatedHighlight }).GetColor(0, Android.Graphics.Color.Gray);
				_pressed.SetColor(highlight);
				_pressed.SetStroke((int)button.BorderWidth, button.BorderColor.ToAndroid());
				_pressed.SetCornerRadius(button.BorderRadius);

				// Add the drawables to a state list and assign the state list to the button
				var sld = new StateListDrawable();
				sld.AddState(new int[] { Android.Resource.Attribute.StatePressed }, _pressed);
				sld.AddState(new int[] { }, _normal);
				Control.SetBackgroundDrawable(sld);
			}
		}

		protected override void OnElementPropertyChanged(object sender, PropertyChangedEventArgs e)
		{
			base.OnElementPropertyChanged(sender, e);
			var button = (Xamarin.Forms.Button)sender;

			if (_normal != null && _pressed != null)
			{
				if (e.PropertyName == "BorderRadius")
				{
					_normal.SetCornerRadius(button.BorderRadius);
					_pressed.SetCornerRadius(button.BorderRadius);
				}
				if (e.PropertyName == "BorderWidth" || e.PropertyName == "BorderColor")
				{
					_normal.SetStroke((int)button.BorderWidth, button.BorderColor.ToAndroid());
					_pressed.SetStroke((int)button.BorderWidth, button.BorderColor.ToAndroid());
				}
			}
		}
    }
}


================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/MainActivity.cs
================================================
﻿using System;

using Android.App;
using Android.Content.PM;
using Android.Runtime;
using Android.Views;
using Android.Widget;
using Android.OS;
using Xamarin.Forms;
using Acr.UserDialogs;

namespace DrMaxMuscle.Droid
{
    [Activity(Label = "Dr. Muscle", Icon = "@drawable/icon", Theme = "@style/MainTheme", MainLauncher = false, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation)]
    public class MainActivity : global::Xamarin.Forms.Platform.Android.FormsAppCompatActivity
    {
        protected override void OnCreate(Bundle bundle)
        {
            TabLayoutResource = Resource.Layout.Tabbar;
            ToolbarResource = Resource.Layout.Toolbar;

            base.OnCreate(bundle);
            
            global::Xamarin.Forms.Forms.Init(this, bundle);
            OxyPlot.Xamarin.Forms.Platform.Android.PlotViewRenderer.Init();
            UserDialogs.Init(() => (Activity)Forms.Context);
			PurchaseManager i = PurchaseManager.Instance;
            LoadApplication(new App());
        }
    }
}




================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/MainApplication.cs
================================================
using System;

using Android.App;
using Android.OS;
using Android.Runtime;
using Plugin.CurrentActivity;

namespace DrMaxMuscle.Droid
{
	//You can specify additional application information in this attribute
    [Application(Icon = "@drawable/icon")]
    public class MainApplication : Application, Application.IActivityLifecycleCallbacks
    {
        public MainApplication(IntPtr handle, JniHandleOwnership transer)
          :base(handle, transer)
        {
        }

        public override void OnCreate()
        {
            base.OnCreate();
            RegisterActivityLifecycleCallbacks(this);
            //A great place to initialize Xamarin.Insights and Dependency Services!
        }

        public override void OnTerminate()
        {
            base.OnTerminate();
            UnregisterActivityLifecycleCallbacks(this);
        }

        public void OnActivityCreated(Activity activity, Bundle savedInstanceState)
        {
            CrossCurrentActivity.Current.Activity = activity;
        }

        public void OnActivityDestroyed(Activity activity)
        {
        }

        public void OnActivityPaused(Activity activity)
        {
        }

        public void OnActivityResumed(Activity activity)
        {
            CrossCurrentActivity.Current.Activity = activity;
        }

        public void OnActivitySaveInstanceState(Activity activity, Bundle outState)
        {
        }

        public void OnActivityStarted(Activity activity)
        {
            CrossCurrentActivity.Current.Activity = activity;
        }

        public void OnActivityStopped(Activity activity)
        {
        }
    }
}


================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/packages.config
================================================
﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Acr.Support" version="2.1.0" targetFramework="monoandroid60" />
  <package id="Acr.UserDialogs" version="6.2.3" targetFramework="monoandroid60" />
  <package id="AndHUD" version="1.2.0" targetFramework="monoandroid60" />
  <package id="Ansuria.XFGloss" version="1.0.3.35" targetFramework="monoandroid60" />
  <package id="ExifLib.PCL" version="1.0.1" targetFramework="monoandroid60" />
  <package id="Newtonsoft.Json" version="9.0.1" targetFramework="monoandroid60" />
  <package id="OxyPlot.Core" version="1.0.0-unstable1983" targetFramework="monoandroid60" />
  <package id="OxyPlot.Xamarin.Android" version="1.0.0-unstable1983" targetFramework="monoandroid60" />
  <package id="OxyPlot.Xamarin.Forms" version="1.0.0-unstable1983" targetFramework="monoandroid60" />
  <package id="Plugin.CurrentActivity" version="1.0.1" targetFramework="monoandroid60" />
  <package id="SlideOverKit" version="2.1.4" targetFramework="monoandroid60" />
  <package id="Splat" version="1.6.2" targetFramework="monoandroid60" />
  <package id="sqlite-net-pcl" version="1.1.2" targetFramework="monoandroid60" />
  <package id="SQLitePCL.bundle_green" version="0.9.2" targetFramework="monoandroid60" />
  <package id="SQLitePCL.plugin.sqlite3.android" version="0.9.2" targetFramework="monoandroid60" />
  <package id="SQLitePCL.raw" version="0.9.2" targetFramework="monoandroid60" />
  <package id="XLabs.Core" version="2.3.0-pre02" targetFramework="monoandroid60" />
  <package id="XLabs.Forms" version="2.3.0-pre02" targetFramework="monoandroid60" />
  <package id="XLabs.IoC" version="2.3.0-pre02" targetFramework="monoandroid60" />
  <package id="XLabs.Platform" version="2.3.0-pre02" targetFramework="monoandroid60" />
  <package id="XLabs.Serialization" version="2.3.0-pre02" targetFramework="monoandroid60" />
</packages>


================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/PurchaseManager.cs
================================================
﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Plugin.CurrentActivity;
using Xamarin.InAppBilling;
using Xamarin.InAppBilling.Utilities;

namespace DrMaxMuscle.Droid
{
	public class PurchaseManager
	{
		private InAppBillingServiceConnection _serviceConnection;
		private IList<Product> _products;
		private IList<Purchase> _purchases;
		private static PurchaseManager _instance;
		public event LifetimeAccessPurchased OnLifetimeAccessPurchased;
		public delegate void LifetimeAccessPurchased();

		public static PurchaseManager Instance
		{
			get
			{
				if (_instance == null)
					_instance = new PurchaseManager();
				return _instance;
			}
		}

		private PurchaseManager()
		{
			try
			{
				Console.WriteLine("1");
				_serviceConnection = new InAppBillingServiceConnection(CrossCurrentActivity.Current.Activity, "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAr5uQwFLKZ+2YYs+6tVUrT9u4mVF+X5Ht3GW4C7B8LJ9iBb6rC1+4ib7t1UFejbt4+Pio816/fDvdTrMuuSeIhT/3TjCiaLX1FEJ3/QFSKDPrufbqGaB01Hg/2cVTZ+RKCIaZrLmdunUnTspOtx5I0Ai19oTwINjfRcIgz7zLkHz+Ga9HMXzcDWGVULAJf7o4Zux1uiQygS+WRGhL7qG2d42ZrkgXJp9SyzFtEYSkdLOHxAHxEJtisUSWjVHvE+j74cIiQxONlMyWqAgM6qRGjPPEFnENOOvpGvX1W8vMC5ZADRaDTRbZ5fk2Q+DL4bGsd2pb7KgbKNzwcKQnRyWQxQIDAQAB");
				Console.WriteLine("2");
				_serviceConnection.OnConnected += SrviceConnection_OnConnected;
				Console.WriteLine("3");
				_serviceConnection.OnInAppBillingError += (error, message) =>
				{
					Console.WriteLine("Error in app billing : " + message);
				};
				Console.WriteLine("4");

				_serviceConnection.Connect();
			}
			catch (Exception e)
			{
				Console.WriteLine(e.Message);
				Console.Write(e.StackTrace);
			}
		}

		public void BuyLifetimeAccess()
		{
			foreach (Product p in _products)
			{
				if (p.ProductId == "drmuscle.lifetimeaccess.nonconsumable")
				{
					_serviceConnection.BillingHandler.BuyProduct(p);
					if (OnLifetimeAccessPurchased != null)
						OnLifetimeAccessPurchased();
					return;
				}
			}
		}

		private void  SrviceConnection_OnConnected()
		{
			_serviceConnection.BillingHandler.OnProductPurchased += (response, purchase, purchaseData, purchaseSignature) =>
				{
					if (purchase.ProductId == "drmuscle.lifetimeaccess.nonconsumable")
						if (OnLifetimeAccessPurchased != null)
							OnLifetimeAccessPurchased();
				};
			Console.WriteLine("5");
			// Load inventory or available products
			GetInventory();
			// Load any items already purchased
			LoadPurchasedItems();
		}

		public IList<Product> Products
		{
			get
			{
				return _products;
			}
		}

		public IList<Purchase> Purchases
		{
			get
			{
				return _purchases;
			}
		}

		private async Task GetInventory()
		{
			// Ask the open connection's billing handler to return a list of available products for the 
			// given list of items.
			// NOTE: We are asking for the Reserved Test Product IDs that allow you to test In-App
			// Billing without actually making a purchase.
			_products = await _serviceConnection.BillingHandler.QueryInventoryAsync(new List<string>   {
				"drmuscle.lifetimeaccess.nonconsumable"
			}, ItemType.Product);
		}

		private void LoadPurchasedItems()
		{
			// Ask the open connection's billing handler to get any purchases
			_purchases = _serviceConnection.BillingHandler.GetPurchases(ItemType.Product);
		}

		public void Disconnect()
		{
			_serviceConnection.Disconnect();
		}
	}
}



================================================
FILE: DrMaxMuscle/DrMaxMuscle.Droid/Resource.cs
================================================
﻿using System;

namespace DrMaxMuscle.Droid
{
    public partial class Resource
    {
        public partial class Attribute
        {
            //            public const int mediaRoutePlayDrawable = -1;
            public const int mediaRouteSettingsDrawable = -2;
        }

        public partial class Color
        {
            public const int design_textinput_error_color = -1;
        }

        public partial class Dimension
        {
            public const int design_fab_content_size = -1;
            public const int design_navigation_padding_top_default = -1;

            public const int design_tab_min_width = -1;
            public const int dialog_fixed_height_major = -1;
            public const int dialog_fixed_height_minor = -1;
            public const int dialog_fixed_width_major = -1;
            public const int dialog_fixed_width_minor = -1;
            public const int mr_media_route_controller_art_max_height = -1;
        }

        public partial class Drawable
        {
            public const int ic_setting_dark = -1;
            public const int ic_setting_light = -1;
            public const int mr_ic_settings_dark = -1;
            public const int mr_ic_settings_light = -1;
        }

        public partial class Id
        {
            public const int art = -1;
            public const int buttons = -1;
            public const int default_control_frame = -1;
            public const int media_route_control_frame = -1;
  