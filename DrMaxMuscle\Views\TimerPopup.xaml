﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
               xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               x:Class="DrMaxMuscle.Views.TimerPopup"
               xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:effects="clr-namespace:DrMaxMuscle.Effects"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
               xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
               xmlns:progress="clr-namespace:MPowerKit.ProgressRing;assembly=MPowerKit.ProgressRing"
               xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
               Color="Transparent"
               VerticalOptions="Fill"
               HorizontalOptions="Fill">

    <Grid RowDefinitions="*, Auto, Auto, Auto"
          Padding="0"
          IgnoreSafeArea="True"
          BackgroundColor="Transparent"
          VerticalOptions="FillAndExpand"
          HorizontalOptions="FillAndExpand">

        <!-- Background Image -->
        <ffimageloading:CachedImage Source="nav.png"
                                     Aspect="Fill"
                                     Grid.RowSpan="4" x:Name="BackImage"
                                     HorizontalOptions="FillAndExpand"
                                     VerticalOptions="FillAndExpand" />

        <!-- Main Timer Content -->
        <StackLayout Grid.Row="0"
                     VerticalOptions="Center"
                     HorizontalOptions="Center"
                     Spacing="15">

            <Label x:Name="LblRestFor"
                   Text="Rest for"
                   FontSize="22"
                   TextColor="White"
                   HorizontalOptions="Center" />

            <Grid>
                <progress:ProgressRing x:Name="ProgressCircle"
                                       WidthRequest="180"
                                       HeightRequest="180"
                                       Color="White"
                                       Thickness="5"
                                       VerticalOptions="Center"
                                       HorizontalOptions="Center" />

                <StackLayout VerticalOptions="Center"
                             HorizontalOptions="Center">
                    <Label x:Name="LblProgressSeconds"
                           FontSize="40"
                           TextColor="White"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
                    <Label x:Name="LblSecondsText"
                           Text="seconds"
                           IsVisible="false"
                           TextColor="White"
                           FontSize="22"
                           HorizontalOptions="Center" />
                </StackLayout>
            </Grid>

            <Label x:Name="LblLastTimeData"
                   FontSize="22"
                   Text=""
                   TextColor="White"
                   HorizontalOptions="Center" />

            <StackLayout Orientation="Horizontal"
                         HorizontalOptions="Center"
                         Spacing="5">
                <Label x:Name="LblGetReadyFor"
                       Text="Get ready for"
                       FontSize="22"
                       TextColor="White" />
                <Label x:Name="LblLastTime"
                       Text=""
                       FontSize="22"
                       TextColor="White" />
            </StackLayout>

            <Label x:Name="LblUpNextRepsSet"
                   FontSize="34"
                   Text=""
                   TextColor="White"
                   HorizontalOptions="Center"
                   HorizontalTextAlignment="Center"
                   Style="{StaticResource LabelStyle}" />

            <Label x:Name="LblPerHand"
                   FontSize="22"
                   Text="per hand"
                   TextColor="White"
                   HorizontalOptions="Center" />

            <StackLayout x:Name="PlateStack"
                         Orientation="Horizontal"
                         HorizontalOptions="Center"
                         VerticalOptions="Start"
                         Spacing="1" />
        </StackLayout>

        <!-- Hide Button Area -->
        <StackLayout Grid.Row="3"
                     VerticalOptions="End"
                     HorizontalOptions="Center"
                     Spacing="5"
                     Padding="35,25">
            <Label Text="Hide"
                   TextColor="White" x:Name="HideButton"
                   FontSize="17"
                   HorizontalOptions="Center">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
                </Label.GestureRecognizers>
            </Label>

            <ffimageloading:CachedImage Source="hide"
                                         WidthRequest="35"
                                         HeightRequest="35"
                                         HorizontalOptions="Center">
                <ffimageloading:CachedImage.GestureRecognizers>
                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped" />
                </ffimageloading:CachedImage.GestureRecognizers>
            </ffimageloading:CachedImage>
        </StackLayout>

    </Grid>
</toolkit:Popup>
