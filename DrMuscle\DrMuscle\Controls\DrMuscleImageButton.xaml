﻿<?xml version="1.0" encoding="UTF-8" ?>
<Frame
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="DrMuscle.Controls.DrMuscleImageButton"
    HeightRequest="60"
    BackgroundColor="#195377"
    Margin="0"
    Padding="2"
    BorderColor="#195377"    
    CornerRadius="0"
    HasShadow="False">
    <Frame.GestureRecognizers>
        <TapGestureRecognizer
            NumberOfTapsRequired="1"
            Tapped="TapGestureRecognizer_Tapped"/>
    </Frame.GestureRecognizers>
    <Grid
        BackgroundColor="White"
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--Horizontal Stacklayout (row 0; col 0)-->
        <StackLayout
            Grid.Row="0"
            Grid.Column="0"
            HorizontalOptions="Center"
            VerticalOptions="FillAndExpand"
            Orientation="Horizontal"
            Spacing="8"
            BackgroundColor="Transparent">

            <!--Image-->
            <Image               
                x:Name="Img"
                Source="{Binding Source}"
                VerticalOptions="FillAndExpand"
                HorizontalOptions="Center"
                Margin="0,18"/>

            <!--Text-->
            <Label                
                x:Name="LblText"
                Text="{Binding Text}"
                HorizontalOptions="Center"
                VerticalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                TextColor="#195377"
                FontAttributes="Bold"
                BackgroundColor="Transparent"/>
        </StackLayout>
    </Grid>
</Frame>