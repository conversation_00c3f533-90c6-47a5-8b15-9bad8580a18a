﻿using Android.Content;
using DrMaxMuscle.Dependencies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Android.Provider;
using Android.Net;
using AndroidUri = Android.Net.Uri;  // Alias for Android's Uri
using SystemUri = System.Uri;        // Alias for System's Uri


// Created by : inam
// purpose : This interface is implemented to open app settings.

namespace DrMaxMuscle.Platforms.Android.Dependencies
{
    public class AppSettingsInterface : IAppSettingsHelper
    {
        public void OpenAppSettings()
        {
            try
            {
                var context = Platform.CurrentActivity?.ApplicationContext;
                var intent = new Intent(global::Android.Provider.Settings.ActionApplicationDetailsSettings);
                intent.AddFlags(ActivityFlags.NewTask);
                string package_name = "com.drmaxmuscle.dr_max_muscle";

                var uri = global::Android.Net.Uri.FromParts("package", package_name, null);
                intent.SetData(uri);

                // Add flags and start the activity
                intent.AddFlags(ActivityFlags.NewTask);
                context.StartActivity(intent);
                //var uri = Android.Net.Uri.FromParts("package", package_name, null);
                //intent.SetData(uri);
                //Application.Context.StartActivity(intent);

            }
            catch (Exception ex)
            {

            }
        }

        public void RateApp()
        {
            
        }
    }
}
