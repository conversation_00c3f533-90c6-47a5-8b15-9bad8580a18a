﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell xmlns="http://xamarin.com/schemas/2014/forms" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
          xmlns:app="clr-namespace:DrMuscle.Constants"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"
    xmlns:fftransformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Transformations"
    xmlns:xfShapeView="clr-namespace:XFShapeView;assembly=XFShapeView"
    xmlns:controls="clr-namespace:DrMuscle.Controls"
    xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
             x:Class="DrMuscle.Cells.UserOutgoingCell">
    <Grid
        Rotation="180"
        FlowDirection="LeftToRight"
        ColumnSpacing="5"
        RowSpacing="0"
        Padding="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
                Width="20">
            </ColumnDefinition>
            <ColumnDefinition
                Width="*">
            </ColumnDefinition>
            <ColumnDefinition
                Width="40">
            </ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
                Height="auto">
            </RowDefinition>
            <RowDefinition
                Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <ffimageloading:CachedImage
            Grid.Row="0"
            IsVisible="false"
            Grid.Column="2"
            Grid.RowSpan="2"
            x:Name="imgOutProfilePic"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            WidthRequest="35"
            HeightRequest="35"
            DownsampleToViewSize="true"
            LoadingPlaceholder="Backgroundblack.png"
            Source="{Binding ProfileUrl}">
        </ffimageloading:CachedImage>
        <!--<StackLayout
            IsVisible="false"
            Orientation="Horizontal"
            Grid.Row="0"
            VerticalOptions="End"
            HorizontalOptions="End"
            Grid.Column="1">
            <Label
                x:Name="timeStampLabel"
                FontSize="Micro"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                VerticalTextAlignment="End"
                VerticalOptions="End"
                Text="{Binding TImeAgo}"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}"/>
            <Label
                x:Name="nameLabel"
                VerticalOptions="Start"
                VerticalTextAlignment="Start"
                FontAttributes="Bold"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                Text="{Binding Nickname}"
                TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">
            </Label>
        </StackLayout>-->

        <pancakeView:PancakeView 
            VerticalOptions="Start"
            Grid.Row="1"
            Grid.Column="1"
            IsClippedToBounds="False"
            HorizontalOptions="End"
            OffsetAngle="225"
            Margin="0,0,-40,0"
            Padding="15"
            CornerRadius="12,0,12,12"  >
            <pancakeView:PancakeView.BackgroundGradientStops>
                <pancakeView:GradientStopCollection>
                    <pancakeView:GradientStop Color="White" Offset="0" />
                    <pancakeView:GradientStop Color="White" Offset="1" />
                </pancakeView:GradientStopCollection>
            </pancakeView:PancakeView.BackgroundGradientStops>
            <StackLayout
                Padding="0"
                Spacing="4"
                Margin="0">
                <controls:ExtendedLabel
                    Grid.Row="1"
                    Grid.Column="1"
                    x:Name="lblInMessage"
                    HorizontalOptions="Start"
                    HorizontalTextAlignment="Start"
                    VerticalOptions="End"
                    VerticalTextAlignment="End"
                    TextColor="Black"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    Text="{Binding Message}" >
                    <controls:ExtendedLabel.HorizontalTextAlignment>
                        <OnPlatform x:TypeArguments="TextAlignment" Android="Start" iOS="End" x:Key="TitleTextAlignment" />
                    </controls:ExtendedLabel.HorizontalTextAlignment>
                </controls:ExtendedLabel>
                <Label
                    x:Name="timeStampLabel"
                    FontSize="Micro"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="End"
                    VerticalTextAlignment="End"
                    VerticalOptions="End"
                    Text="{Binding TImeAgo}"
                    TextColor="Gray"/>
            </StackLayout>
            
            <pancakeView:PancakeView.Shadow>
                <pancakeView:DropShadow Color="{OnPlatform Android='#D1D5D8',iOS='Gray'}" Opacity="0.5" BlurRadius="{x:OnPlatform Android='3',iOS='3'}" />
            </pancakeView:PancakeView.Shadow>
        </pancakeView:PancakeView>

    </Grid>
</ViewCell>
