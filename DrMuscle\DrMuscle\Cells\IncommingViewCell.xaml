﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:DrMuscle.Constants;"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Forms;assembly=FFImageLoading.Forms"
    xmlns:fftransformations="clr-namespace:FFImageLoading.Transformations;assembly=FFImageLoading.Transformations"
    xmlns:xfShapeView="clr-namespace:XFShapeView;assembly=XFShapeView"
    xmlns:controls="clr-namespace:DrMuscle.Controls" xmlns:pancakeView="clr-namespace:Xamarin.Forms.PancakeView;assembly=Xamarin.Forms.PancakeView"
    x:Class="DrMuscle.Cells.IncommingViewCell">
    <Grid
        Rotation="180"
        FlowDirection="LeftToRight"
        ColumnSpacing="5"
        RowSpacing="0"
        Padding="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition
                Width="40">
            </ColumnDefinition>
            <ColumnDefinition
                Width="*">
            </ColumnDefinition>
            <ColumnDefinition
                Width="20">
            </ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition
                Height="auto">
            </RowDefinition>
            <RowDefinition
                Height="*">
            </RowDefinition>
        </Grid.RowDefinitions>
        <ffimageloading:CachedImage
            Grid.Row="0"
            Grid.Column="0"
            Grid.RowSpan="2"
            x:Name="imgInProfilePic"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            WidthRequest="35"
            HeightRequest="35"
            DownsampleToViewSize="true"
            LoadingPlaceholder="Backgroundblack.png"
            Source="{Binding ProfileUrl}">
        </ffimageloading:CachedImage>
        <Frame
            Padding="0"
            Grid.Row="0"
            CornerRadius="{OnPlatform Android='50',iOS= '17'}"
            Grid.Column="0"
            Grid.RowSpan="2"
            HorizontalOptions="Center"
            VerticalOptions="Start"
            WidthRequest="33"
            HeightRequest="33"
            HasShadow="false"
            x:Name="FrmProfile">
            <Label
                x:Name="LblProfileText"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center"
                Text=""
                TextColor="White"
                FontSize="20" />
        </Frame>

        <StackLayout
            Grid.Row="1"
            Margin="-40,10,0,0"
            Grid.Column="1"
            VerticalOptions="End"
            Orientation="Horizontal"
            IsClippedToBounds="true"
            HorizontalOptions="Start">
            <pancakeView:PancakeView 
                VerticalOptions="Start"
                Grid.Row="1"
                Grid.Column="1"
                IsClippedToBounds="False"
                HorizontalOptions="End"
                OffsetAngle="225"
                Margin="0,5,15,5" 
                Padding="15"
                Style="{StaticResource PancakeViewStyleBlue}"
                CornerRadius="0,12,12,12" >
                

                <StackLayout
                     Spacing="4"
                     Padding="0"
                     Margin="0">
                    <controls:ExtendedLightBlueLabel
                    VerticalTextAlignment="End"
                    x:Name="lblOutMessage"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="Start"
                    TextColor="White"
                    FontSize="17"
                    LineHeight="{OnPlatform Android='1.3',iOS='1.2'}"
                    Text="{Binding Message}">

                    <controls:ExtendedLightBlueLabel.HorizontalTextAlignment>
                        <OnPlatform
                            x:TypeArguments="TextAlignment"
                            Android="Start"
                            iOS="Start"
                            x:Key="TitleTextAlignment" />
                    </controls:ExtendedLightBlueLabel.HorizontalTextAlignment>
                </controls:ExtendedLightBlueLabel>
                    <Label
                        FontSize="Micro"
                        HorizontalOptions="End"
                        HorizontalTextAlignment="End"
                        Text="{Binding TImeAgo}"
                        VerticalOptions="End"
                        VerticalTextAlignment="End"
                        TextColor="LightGray">
                    </Label>
                </StackLayout>
                <pancakeView:PancakeView.Shadow>
                    <pancakeView:DropShadow Color="{OnPlatform Android='#D1D5D8',iOS='Gray'}" Opacity="0.5" BlurRadius="{x:OnPlatform Android='3',iOS='3'}" />
                </pancakeView:PancakeView.Shadow>
            </pancakeView:PancakeView>
            
        </StackLayout>
        <StackLayout
            Grid.Row="0"
            Margin="0,8,0,0"
            Grid.Column="1"
            VerticalOptions="Center"
            Orientation="Horizontal"
            HorizontalOptions="Start">
            <Label
                x:Name="nameLabel"
                FontAttributes="Bold"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                Text="{Binding Nickname}"
                TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer
                        Tapped="Username_Tapped" />
                </Label.GestureRecognizers>
            </Label>

        </StackLayout>
        <!--<StackLayout
            Grid.Row="1"
            Grid.Column="1"
            VerticalOptions="End"
            Orientation="Horizontal"
            IsClippedToBounds="true"
            HorizontalOptions="Start">

            <Frame
                Margin="5,5,15,5"
                CornerRadius="12"
                Padding="20,12,20,12"
                Grid.Row="1"
                Grid.Column="1"
                VerticalOptions="End"
                IsClippedToBounds="true"
                HorizontalOptions="Start"
                BorderColor="#ffffff"
                OutlineColor="#ffffff"
                HasShadow="False"
                BackgroundColor="#ffffff">
                <controls:ExtendedLabel
                    VerticalTextAlignment="End"
                    x:Name="lblOutMessage"
                    HorizontalOptions="End"
                    HorizontalTextAlignment="Start"
                    TextColor="#23253a"
                    FontSize="16"
                    Text="{Binding Message}">
                    
                    <controls:ExtendedLabel.HorizontalTextAlignment>
                        <OnPlatform
                            x:TypeArguments="TextAlignment"
                            Android="Start"
                            iOS="Start"
                            x:Key="TitleTextAlignment" />
                    </controls:ExtendedLabel.HorizontalTextAlignment>
                </controls:ExtendedLabel>
            </Frame>
        </StackLayout>
        <StackLayout
            Grid.Row="0"
            Grid.Column="1"
            VerticalOptions="Start"
            Orientation="Horizontal"
            HorizontalOptions="Start">
            <Label
                FontAttributes="Bold"
                HorizontalOptions="End"
                HorizontalTextAlignment="End"
                x:Name="nameLabel"
                Text="{Binding Nickname}"
                TextColor="{x:Static app:AppThemeConstants.OffBlackColor}">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer
                        Tapped="Username_Tapped" />
                </Label.GestureRecognizers>
            </Label>
            <Label
                FontSize="Micro"
                HorizontalOptions="Start"
                HorizontalTextAlignment="Start"
                Text="{Binding TImeAgo}"
                VerticalOptions="End"
                VerticalTextAlignment="End"
                TextColor="{x:Static app:AppThemeConstants.BlueColor}">
            </Label>
        </StackLayout>-->
        <!-- <Label
            Grid.Row="1"
            FontSize="Micro"
            Grid.Column="1"
            HorizontalOptions="End"
            HorizontalTextAlignment="End"
            Text="{Binding CreatedOn,StringFormat='{0:dd-MM-yyyy hh:mm}'}"
            TextColor="Gray">
        </Label>-->
    </Grid>
</ViewCell>