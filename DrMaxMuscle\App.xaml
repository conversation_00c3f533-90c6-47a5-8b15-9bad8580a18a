﻿<?xml version="1.0" encoding="UTF-8" ?>
<Application
    x:Class="DrMaxMuscle.App"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
    xmlns:converter="clr-namespace:DrMaxMuscle.Convertors"
    xmlns:local="clr-namespace:DrMaxMuscle"
    xmlns:localize="clr-namespace:DrMaxMuscle.Resx">
    <Application.Resources>
        <ResourceDictionary>

            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <ResourceDictionary>

                <!--  Define a gradient brush for the navigation bar background  -->
                <LinearGradientBrush x:Key="NavigationBarGradientBrush" StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Offset="0.0" Color="#0C2432" />
                    <GradientStop Offset="1.0" Color="#195276" />
                </LinearGradientBrush>

                <!--  Apply the gradient to the NavigationPage  -->
                <Style TargetType="NavigationPage">
                    <Setter Property="BarBackground" Value="{StaticResource NavigationBarGradientBrush}" />
                    <Setter Property="BarTextColor" Value="White" />
                </Style>

            </ResourceDictionary>
            <converter:InttoBoolConvertor x:Key="IntBoolConverter" />
            <converter:StringToBoolConvertor x:Key="StringBoolConverter" />
            <converter:SurveySelectionEnumToVisibilityConverter x:Key="SurveySelectionEnumToVisibilityConverter" />
            <converter:MonthNameConverter x:Key="MonthNameConverter" />
            <Style x:Key="infoText" TargetType="Label">
                <Setter Property="FontSize" Value="10" />
            </Style>
            <Style TargetType="Switch"
                   ApplyToDerivedTypes="True">
                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>
                        <VisualStateGroup x:Name="CommonStates">
                            <VisualState x:Name="Off">
                                <VisualState.Setters>
                                    <Setter Property="ThumbColor"
                                            Value="{OnPlatform Android='#F5F5F5', iOS='Default'}" />
                                    <Setter Property="OnColor"
                                             Value="{OnPlatform Android='#BCBCBC', iOS='Default'}" />
                                </VisualState.Setters>
                            </VisualState>
                            <VisualState x:Name="On">
                                <VisualState.Setters>
                                    <Setter Property="ThumbColor"
                                            Value="{OnPlatform Android='#61e845', iOS='Default'}"/>
                                    <Setter Property="OnColor"
                                            Value="{OnPlatform Android='LightGray', iOS='Default'}" />
                                </VisualState.Setters>
                            </VisualState>
                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>
            </Style>

            <!--<Style ApplyToDerivedTypes="True" TargetType="Switch">
                <Setter Property="OnColor" Value="#34c85a" />
            </Style>-->
            <!--<Style ApplyToDerivedTypes="True"
                   TargetType="Switch">
                <Setter Property="OnColor"
                        Value="LightGray" />
                <Setter Property="ThumbColor"
                        Value="#61e845" />
            </Style>-->
            <Style x:Key="LearnMoreText" TargetType="Label">
                <Setter Property="FontSize" Value="13" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
            </Style>
            <Style x:Key="ItemContextCancelButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Padding" Value="2" />
                <Setter Property="Text" Value="{localize:Translate Cancel}" />
                <Setter Property="WidthRequest" Value="55" />
            </Style>
            <Style x:Key="ItemContextRenameButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='10', iOS='10'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate Rename}" />
                <Setter Property="WidthRequest" Value="65" />
            </Style>
            <Style x:Key="ItemContextSwapButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate Swap}" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <Style x:Key="ItemContextVideoButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <!--<Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />-->
                <Setter Property="FontSize" Value="{OnPlatform Android='10', iOS='10'}" />
                <Setter Property="Padding" Value="{OnPlatform Android='0,7', iOS='0'}" />
                <Setter Property="Margin" Value="0,0" />
                <Setter Property="Text" Value="{localize:Translate Video}" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <!--    -->
            <Style x:Key="ItemContextDeloadButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>

            <Style x:Key="ItemContextChallengeButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate Video}" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <Style x:Key="ItemContextEditButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate Edit}" />
                <Setter Property="WidthRequest" Value="55" />
            </Style>
            <Style x:Key="ItemContextRestoreButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate Restore}" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <Style x:Key="ItemContextDeleteButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Padding" Value="2" />
                <Setter Property="Text" Value="{localize:Translate Delete}" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="WidthRequest" Value="55" />
            </Style>
            <Style x:Key="ItemContextResetButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate More}" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <Style x:Key="ItemContextSettingsButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value="{localize:Translate More}" />
                <Setter Property="WidthRequest" Value="60" />
            </Style>
            <Style x:Key="ItemContextMoreButton" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="{OnPlatform Android='11', iOS='11'}" />
                <Setter Property="Margin" Value="1,5" />
                <Setter Property="Text" Value=" ... " />
                <Setter Property="WidthRequest" Value="51" />
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>
            <Style x:Key="timerButtonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="BorderWidth" Value="2" />
                <!--<Setter Property="BorderRadius" Value="5" />-->
                <Setter Property="CornerRadius" Value="0" />
                <Setter Property="BorderColor" Value="{x:Static constnats:AppThemeConstants.TextColorBlack}" />
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>
            <Style x:Key="timerEntryStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
            </Style>

            <Style x:Key="buttonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="BorderWidth" Value="2" />
                <Setter Property="CornerRadius" Value="0" />
                <Setter Property="BorderColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="HeightRequest" Value="55" />

            </Style>
            <Style x:Key="highEmphasisButtonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="BorderWidth" Value="0" />
                <Setter Property="CornerRadius" Value="0" />
                <Setter Property="HeightRequest" Value="55" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="BorderColor" Value="Transparent" />
            </Style>
            <Style x:Key="buttonLinkStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="BorderWidth" Value="0" />
                <Setter Property="HeightRequest" Value="50" />
            </Style>
            <Style x:Key="repsbuttonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="TextColor" Value="Gray" />
                <Setter Property="BorderWidth" Value="2" />
                <!--<Setter Property="BorderRadius" Value="5" />-->
                <Setter Property="BorderColor" Value="Gray" />
                <Setter Property="CornerRadius" Value="0" />
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>
            <Style x:Key="menubuttonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="BorderColor" Value="#195377" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="BorderWidth" Value="2" />
                <!--<Setter Property="BorderRadius" Value="5" />-->
                <Setter Property="CornerRadius" Value="0" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="HeightRequest" Value="70" />
                <Setter Property="Margin" Value="40" />
            </Style>
            <Style x:Key="fbButtonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="#f4f4f4" />
                <Setter Property="TextColor" Value="#3b5998" />
                <Setter Property="Padding" Value="10,0,10,0" />
                <Setter Property="BorderWidth" Value="2" />
                <!--<Setter Property="BorderRadius" Value="0" />-->
                <Setter Property="BorderColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>
            <Style x:Key="GoogleButtonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="#E74B37" />
                <Setter Property="TextColor" Value="White" />
                <Setter Property="Padding" Value="10,0,10,0" />
                <Setter Property="BorderWidth" Value="0" />
                <!--<Setter Property="BorderRadius" Value="0" />-->
                <Setter Property="BorderColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="FontAttributes" Value="Bold" />
            </Style>

            <Style x:Key="entryStylebackup" TargetType="Button">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="Opacity" Value="0.80" />
            </Style>
            <Style x:Key="entryStyle" TargetType="Entry">
                <Setter Property="BackgroundColor" Value="#f9f9f9" />
                <Setter Property="Opacity" Value="0.85" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.TextColorBlack}" />
            </Style>

            <Style x:Key="slideMenuButtonStyle" TargetType="Button">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.TextColorBlack}" />
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="CornerRadius" Value="0" />
            </Style>
            <Style x:Key="buttonTransparent" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
            </Style>
            <Style x:Key="mainButtonStyle" TargetType="Button">
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
                <Setter Property="BorderWidth" Value="0" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="CornerRadius" Value="0" />
            </Style>
            <Style x:Key="GradientFrameStyleBlue" TargetType="Frame">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush EndPoint="1,0">
                            <GradientStop Offset="0.0" Color="#0C2432" />
                            <GradientStop Offset="1.0" Color="#195276" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
                <Setter Property="BorderColor" Value="Transparent" />
            </Style>
            <Style x:Key="GradientStackStyleBlue" TargetType="StackLayout">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush EndPoint="0.9,0.1">
                            <GradientStop Offset="0.0" Color="#0C2432" />
                            <GradientStop Offset="1.0" Color="#195276" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="GradientBorderStyleBlue" TargetType="Border">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush EndPoint="1,0">
                            <GradientStop Offset="0.0" Color="#0C2432" />
                            <GradientStop Offset="1.0" Color="#195276" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="GradientFrameStyleGreen" TargetType="Frame">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush>
                            <GradientStop Offset="0.0" Color="#DFFF69" />
                            <GradientStop Offset="1.0" Color="#E9FF97" />
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Style>
            <!--<Style x:Key="PancakeViewStyleBlue" TargetType="pancakeView:PancakeView">
                <Setter Property="BackgroundGradientStops">
                    <Setter.Value>
                        <pancakeView:GradientStopCollection>
                            <pancakeView:GradientStop Color="#0C2432" Offset="0" />
                            <pancakeView:GradientStop Color="#195276" Offset="1" />
                        </pancakeView:GradientStopCollection>
                    </Setter.Value>
                </Setter>
            </Style>
            <Style x:Key="PancakeViewStyleGreen" TargetType="pancakeView:PancakeView">
                <Setter Property="BackgroundGradientStops">
                    <Setter.Value>
                        <pancakeView:GradientStopCollection>
                            <pancakeView:GradientStop Color="#DFFF69" Offset="0" />
                            <pancakeView:GradientStop Color="#E9FF97" Offset="1" />
                        </pancakeView:GradientStopCollection>
                    </Setter.Value>
                </Setter>
            </Style>-->
            <!--  Labels  -->
            <Style x:Key="LabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
            </Style>
            <Style x:Key="LabelStyle1" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="17" />
                <Setter Property="LineHeight" Value="{OnPlatform Android='1.3', iOS='1.2'}" />
            </Style>
            <Style x:Key="LabelStyle2" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />

                <Setter Property="LineBreakMode" Value="WordWrap" />
            </Style>

            <Style x:Key="NormalLabelStyle" TargetType="Label">
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
            </Style>
            <Style x:Key="NormalLabelStyle1" TargetType="Label">
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="17" />
                <Setter Property="LineHeight" Value="{OnPlatform Android='1.3', iOS='1.2'}" />
            </Style>

            <Style x:Key="BoldLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.TextColorBlack}" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="StartAndExpand" />
            </Style>
            <Style x:Key="BoldLabelStyle1" TargetType="Label">
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.TextColorBlack}" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="StartAndExpand" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="17" />
                <Setter Property="LineHeight" Value="{OnPlatform Android='1.3', iOS='1.2'}" />
            </Style>
            <Style x:Key="WorkoutLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
                <Setter Property="FontSize" Value="Medium" />
                <Setter Property="BackgroundColor" Value="Transparent" />
            </Style>

            <Style x:Key="OnBoardingLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
                <Setter Property="FontSize" Value="15" />
                <Setter Property="Opacity" Value="0.95" />
            </Style>

            <Style x:Key="OnBoardingLabelStyleBig" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.OffBlackColor}" />
                <Setter Property="FontSize" Value="{x:Static constnats:AppThemeConstants.DescriptionFontSize}" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="FontSize" Value="20" />
            </Style>

            <Style x:Key="PickerStyle" TargetType="Picker">
                <Setter Property="TextColor" Value="{OnPlatform Android='#26262b', iOS='#26262B'}" />
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.DefaultColor}" />
            </Style>

            <Style x:Key="PasscodeLabelStyle" TargetType="Label">
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="HeightRequest" Value="50" />
                <Setter Property="HorizontalOptions" Value="Center" />
                <Setter Property="WidthRequest" Value="16" />
                <Setter Property="FontSize" Value="Large" />
                <Setter Property="FontAttributes" Value="Bold" />
                <Setter Property="VerticalTextAlignment" Value="Center" />
                <Setter Property="HorizontalTextAlignment" Value="Start" />
            </Style>
            <Style x:Key="PasscodeButtonStyle" TargetType="Button">
                <Setter Property="HeightRequest" Value="60" />
                <Setter Property="WidthRequest" Value="60" />
                <Setter Property="HorizontalOptions" Value="Center" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="CornerRadius" Value="30" />
                <Setter Property="BackgroundColor" Value="Transparent" />
                <Setter Property="TextColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="FontSize" Value="24" />
            </Style>

            <Style x:Key="PasscodeBoxviewStyle" TargetType="BoxView">
                <Setter Property="BackgroundColor" Value="{x:Static constnats:AppThemeConstants.BlueColor}" />
                <Setter Property="HeightRequest" Value="24" />
                <Setter Property="WidthRequest" Value="24" />
                <Setter Property="CornerRadius" Value="12" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="HorizontalOptions" Value="Center" />
            </Style>
            <Style x:Key="RippleStyle" TargetType="Button">

                <Setter Property="VisualStateManager.VisualStateGroups">
                    <VisualStateGroupList>

                        <VisualStateGroup x:Name="CommonStates">

                            <VisualState x:Name="Normal">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="#DFFF69" />
                                </VisualState.Setters>
                            </VisualState>

                            <VisualState x:Name="Pressed">
                                <VisualState.Setters>
                                    <Setter Property="BackgroundColor" Value="#AADFFF69" />
                                </VisualState.Setters>
                            </VisualState>

                        </VisualStateGroup>
                    </VisualStateGroupList>
                </Setter>

            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
