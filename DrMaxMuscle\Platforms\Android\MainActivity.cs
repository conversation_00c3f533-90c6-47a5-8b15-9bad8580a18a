﻿using Acr.UserDialogs;
using Android;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Util;
using Android.Views;
using Android.Widget;
using AndroidX.LocalBroadcastManager.Content;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Screens.Eve;
using DrMuscleWebApiSharedModel;
using Firebase.Analytics;
using Google.Android.Material.BottomNavigation;
using Microsoft.Maui.Controls.Compatibility;
using Newtonsoft.Json;
using Plugin.Firebase.Auth.Google;
// using Plugin.GoogleClient.MAUI;
using static AndroidX.ViewPager.Widget.ViewPager;
using static Microsoft.Maui.ApplicationModel.Permissions;
using Platform = Microsoft.Maui.ApplicationModel.Platform;

namespace DrMaxMuscle
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        // Uncomment code please
        //public static ICallbackManager CallbackManager { get; private set; }
        public static Context Instance { get; private set; }

        FirebaseAnalytics firebaseAnalytics;
        public static Activity _currentActivity;
        public static DisplayMetrics displayMetrics;
        // Uncomment code please
        //GoogleApiClient _client;
        const int RequestStorageId = 0;
        protected override void OnCreate(Bundle bundle)
        {
            base.OnCreate(bundle);
            Instance = this;
            UserDialogs.Init(this);
            _currentActivity = this;
            App.ScreenHeight = (int)(Resources.DisplayMetrics.HeightPixels / Resources.DisplayMetrics.Density);
            App.ScreenWidth = (int)(Resources.DisplayMetrics.WidthPixels / Resources.DisplayMetrics.Density);

            firebaseAnalytics = FirebaseAnalytics.GetInstance(this);
            Android.Views.View decorView = Window.DecorView;

            if ((int)Android.OS.Build.VERSION.SdkInt >= 19)// Check build version for Nav bar
            {
                var uiOptions = (int)decorView.SystemUiVisibility;
                var newUiOptions = (int)uiOptions;

                newUiOptions |= (int)SystemUiFlags.Fullscreen;
                newUiOptions |= (int)SystemUiFlags.HideNavigation;
                newUiOptions |= (int)SystemUiFlags.Immersive;
                newUiOptions |= (int)SystemUiFlags.ImmersiveSticky;

                decorView.SystemUiVisibility = (StatusBarVisibility)newUiOptions;
                decorView.SystemUiVisibilityChange += (o, e) =>
                {
                    if (App.IsNUX)//|| App.IsNewUser
                        decorView.SystemUiVisibility = (StatusBarVisibility)newUiOptions;
                };
            }
            this.Window.AddFlags(WindowManagerFlags.Fullscreen);
            this.Window.AddFlags(WindowManagerFlags.KeepScreenOn);
            Window.SetFlags(WindowManagerFlags.HardwareAccelerated, WindowManagerFlags.HardwareAccelerated);
            App.StatusBarHeight = 0;

            Window.DecorView.LayoutDirection = Android.Views.LayoutDirection.Ltr;


            // Delay notification handling until after UI setup
            Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(500); // Add a small delay to ensure UI elements are fully initialized
                    HandleNotificationTapped(Intent);
                }
                catch (Exception ex)
                {
                    // Handle the exception (log it or take any necessary action)
                }
            });


            // Run UI modifications after the layout is set
            Window.DecorView.Post(() =>
            {
                IncreaseNavBarHeight();
                IncreaseTabIconSize();
            });
            

            //var bottomNavView = FindViewById<BottomNavigationView>(Resource.Id.navigation_bar_item_icon_view);

            //if (bottomNavView != null)
            //{
            //    // Loop through the menu items and resize icons
            //    for (int i = 0; i < bottomNavView.Menu.Size(); i++)
            //    {
            //        var menuItem = bottomNavView.Menu.GetItem(i);
            //        var icon = menuItem.Icon;

            //        if (icon != null)
            //        {
            //            int iconSize = (int)ConvertDpToPx(this, 36); // Change 36 to your desired icon size
            //            icon.SetBounds(0, 0, iconSize, iconSize);
            //            menuItem.SetIcon(icon);
            //        }
            //    }
            //}

            IntentFilter filter = new IntentFilter(Intent.ActionSend);
            MessageReciever receiver = new MessageReciever(this);
            LocalBroadcastManager.GetInstance(this).RegisterReceiver(receiver, filter);

            GetNotificationPermission();
            GetAlarmPermission();



            
        }
        public override void OnWindowFocusChanged(bool hasFocus)
        {
            base.OnWindowFocusChanged(hasFocus);
            //ContextCompat.CheckSelfPermission(this, "android.permission.POST_NOTIFICATIONS");
            Android.Views.View decorView = Window.DecorView;
            if ((int)Android.OS.Build.VERSION.SdkInt >= 19 && hasFocus)// Check build version for Nav bar
            {
                var uiOptions = (int)decorView.SystemUiVisibility;
                var newUiOptions = (int)uiOptions;

                newUiOptions |= (int)SystemUiFlags.Fullscreen;
                newUiOptions |= (int)SystemUiFlags.HideNavigation;
                newUiOptions |= (int)SystemUiFlags.Immersive;
                newUiOptions |= (int)SystemUiFlags.ImmersiveSticky;

                decorView.SystemUiVisibility = (StatusBarVisibility)newUiOptions;

            }
        }
        protected override void OnActivityResult(int requestCode, Result resultCode, Intent data)
        {
            try
            {

                base.OnActivityResult(requestCode, resultCode, data);
                //Plugin.InAppBilling.InAppBillingImplementation.HandleActivityResult(requestCode, resultCode, data);

                // GoogleClientManager.OnAuthCompleted(requestCode, resultCode, data);
                FirebaseAuthGoogleImplementation.HandleActivityResultAsync(requestCode, resultCode, data);
            }
            catch (Exception ex)
            {

            }
        }
        protected override void OnPause()
        {
            try
            {
                var currentPage = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault();

                if (currentPage is MainTabbedPage tabbedPage)
                {
                    var currentPage1 = tabbedPage.CurrentPage;

                    if (currentPage1 is NavigationPage navigationPage)
                    {
                        var currentContentPage = navigationPage.CurrentPage;

                        if (currentContentPage is MealInfoPage)
                        {
                            App.IsAppGoesBackground = true;
                        }
                        else
                        {
                            App.IsAppGoesBackground = false;
                        }
                    }
                }

                base.OnPause();
                if (Timer.Instance.Remaining > 0 && Timer.Instance.State == "RUNNING")
                {
                    LocalDBManager.Instance.SetDBSetting("LastBackgroundTimee", DateTime.Now.Ticks.ToString());
                    LocalDBManager.Instance.SetDBSetting("LastBackgroundTimerTime", Timer.Instance.Remaining.ToString());
                    Timer.Instance.StopAllTimer();
                    Timer.Instance.TimerDone();
                }
            }
            catch (Exception ex)
            {

            }
        }
        protected async override void OnResume()
        {
            base.OnResume();
            firebaseAnalytics = FirebaseAnalytics.GetInstance(this);
            try
            {
                var ticks = LocalDBManager.Instance.GetDBSetting("LastBackgroundTimee")?.Value;
                //
                var timerTime = LocalDBManager.Instance.GetDBSetting("LastBackgroundTimerTime")?.Value;
                timerTime = timerTime == null ? "0" : timerTime;

                var date = new DateTime(long.Parse(ticks == null ? "0" : ticks));

                var seconds = (DateTime.Now - date).TotalSeconds;
                var remaininTime = Convert.ToDouble(LocalDBManager.Instance.GetDBSetting("timer_remaining").Value);
                MessagingCenter.Send<EnterForegroundMessage>(new EnterForegroundMessage(), "EnterForegroundMessage");
                if (seconds > double.Parse(timerTime))
                    Timer.Instance.Remaining = 0;
                else
                    Timer.Instance.Remaining = (int)(double.Parse(timerTime) - seconds);
                var remaining = Timer.Instance.Remaining;
                if (remaining > 0)
                {
                    Timer.Instance.stopRequest = true;
                    var val = LocalDBManager.Instance.GetDBSetting("timer_remaining").Value;
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", remaining.ToString());
                    await Timer.Instance.StartTimer();
                    Timer.Instance.Remaining = remaining;
                    LocalDBManager.Instance.SetDBSetting("timer_remaining", val);
                }
            }
            catch (Exception ex)
            {

            }
        }
        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, Permission[] grantResults)
        {
            Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }

        protected override void OnNewIntent(Android.Content.Intent intent)
        {
            base.OnNewIntent(intent);
            firebaseAnalytics = FirebaseAnalytics.GetInstance(this);

            string pageKey = intent.GetStringExtra("PageKey");
            //FirebasePushNotificationManager.ProcessIntent(this, intent);
            System.Diagnostics.Debug.WriteLine("#######Notification Tapped :");
            //HandleNotificationTapped(intent);
            if (!string.IsNullOrEmpty(pageKey))
            {
                if (pageKey.ToLower().Contains("chat"))
                {
                    MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage(""), "NavigationOnNotificationTappedMessage");
                }
                else if (pageKey.ToLower().Contains("local5th"))
                {
                    CurrentLog.Instance.IsRecoveredWorkout = true;
                    MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Local"), "NavigationOnNotificationTappedMessage");
                }
                else if (pageKey.ToLower().Contains("workout"))
                {
                    CurrentLog.Instance.IsRecoveredWorkout = true;
                    MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Workout", intent.GetStringExtra("Extra")), "NavigationOnNotificationTappedMessage");
                }
            }
        }
        private async void HandleNotificationTapped(Android.Content.Intent intent)
        {
            string pageKey = intent.GetStringExtra("PageKey");
            if (!string.IsNullOrEmpty(pageKey))
            {
                if (pageKey.ToLower().Contains("chat"))
                {
                    await Task.Delay(16000);
                    MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage(""), "NavigationOnNotificationTappedMessage");
                }
                else if (pageKey.ToLower().Contains("local5th"))
                {
                    CurrentLog.Instance.IsRecoveredWorkout = true;
                    await Task.Delay(10000);
                    MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Local"), "NavigationOnNotificationTappedMessage");
                }
                else if (pageKey.ToLower().Contains("workout"))
                {
                    CurrentLog.Instance.IsRecoveredWorkout = true;
                    CurrentLog.Instance.IsWelcomePopup = true;
                    CurrentLog.Instance.IsWelcomeMessage = true;
                    await Task.Delay(10000); MessagingCenter.Send<NavigationOnNotificationTappedMessage>(new NavigationOnNotificationTappedMessage("Workout", intent.GetStringExtra("Extra")), "NavigationOnNotificationTappedMessage");
                }
            }
            //}
        }
        public void ProcessMessage(Intent intent)
        {
            //_txtMsg.Text =
            try
            {


                var message = intent.GetStringExtra("WearMessage");
                var phoneModel = JsonConvert.DeserializeObject<PhoneToWatchModel>(message);
                if (phoneModel != null && phoneModel.SenderPlatform == DrMuscleWebApiSharedModel.Platform.Watch)
                {
                    App.IsConnectedToWatch = true;
                    MessagingCenter.Send<ReceivedWatchMessage>(new ReceivedWatchMessage() { PhoneToWatchModel = phoneModel }, "ReceivedWatchMessage");
                }

            }
            catch (Exception ex)
            {

            }
        }
        private async Task<bool> GetNotificationPermission()
        {
            try
            {
                // Tiramisu is Android v13
                if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu)
                {
                    var status = await CheckStatusAsync<PostNotificationsPermission>();
                    if (status == PermissionStatus.Granted)
                    {
                        return true;
                    }

                    status = await RequestAsync<PostNotificationsPermission>();

                    return status == PermissionStatus.Granted;
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        private async Task<bool> GetAlarmPermission()
        {
            try
            {
                //for Android 14
                if (Build.VERSION.SdkInt > BuildVersionCodes.Tiramisu)
                {
                    var exactAlarmStatus = await CheckStatusAsync<ScheduleExactAlarmPermission>();
                    if (exactAlarmStatus != PermissionStatus.Granted)
                    {
                        exactAlarmStatus = await RequestAsync<ScheduleExactAlarmPermission>();

                        return exactAlarmStatus == PermissionStatus.Granted;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        private double GetStatusBarHeight()
        {
            double statusBarHeight = -1;
            int resourceId = this.Resources.GetIdentifier("status_bar_height", "dimen", "android");

            if (resourceId > 0)
            {
                statusBarHeight = this.Resources.GetDimensionPixelSize(resourceId) / Resources.DisplayMetrics.Density;
            }

            return statusBarHeight;
        }
        internal class PostNotificationsPermission : BasePlatformPermission
        {
            public override (string androidPermission, bool isRuntime)[] RequiredPermissions =>
                new List<(string androidPermission, bool isRuntime)>
                {
             (Manifest.Permission.PostNotifications, true)
                }.ToArray();
        }
        internal class ScheduleExactAlarmPermission : BasePlatformPermission
        {
            public override (string androidPermission, bool isRuntime)[] RequiredPermissions =>
                new List<(string androidPermission, bool isRuntime)>
                {
            (Manifest.Permission.ScheduleExactAlarm, true)
                }.ToArray();
        }
        internal class MessageReciever : Android.Content.BroadcastReceiver
        {
            MainActivity _main;
            public MessageReciever(MainActivity owner) { this._main = owner; }
            public override void OnReceive(Context context, Intent intent)
            {
                _main.ProcessMessage(intent);
                // Toast.MakeText(MainActivity._currentActivity, "Meesage received", ToastLength.Long).Show();
            }
        }

        public static void OpenGmailApp(string subject, string body)
        {
            try
            {
                Intent intent = new Intent(Intent.ActionSend);
                intent.SetType("message/rfc822");
                intent.PutExtra(Intent.ExtraEmail, new string[] { "<EMAIL>" });
                intent.PutExtra(Intent.ExtraSubject, subject);
                intent.PutExtra(Intent.ExtraText, body);
                intent.SetPackage("com.google.android.gm"); // Force Gmail App

                Platform.CurrentActivity.StartActivity(intent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error opening Gmail: {ex.Message}");
            }
        }

        private void IncreaseNavBarHeight()
        {
            var rootView = Window.DecorView.RootView as ViewGroup;
            if (rootView == null) return;

            var bottomNavView = FindBottomNavigationView(rootView);
            if (bottomNavView == null) return;

            int newHeight = (int)ConvertDpToPx(this, 65); // Adjust to preferred height

            var layoutParams = bottomNavView.LayoutParameters;
            layoutParams.Height = newHeight;
            bottomNavView.LayoutParameters = layoutParams;
            bottomNavView.RequestLayout();


            for (int i = 0; i < bottomNavView.ChildCount; i++)
            {
                var itemView = bottomNavView.GetChildAt(i);

                if (itemView is ViewGroup itemGroup)
                {
                    var textViews = FindAllTextViews(itemGroup);

                    foreach (var textView in textViews)
                    {
                        textView.SetTextSize(Android.Util.ComplexUnitType.Sp, 14); // Adjust text size
                        //textView.SetTypeface(Android.Graphics.Typeface.DefaultBold, TypefaceStyle.Bold); // Make text bold
                    }
                }
            }

        }

        private void IncreaseTabIconSize()
        {
            // Get the root view and find the BottomNavigationView
            var rootView = Window.DecorView.RootView as ViewGroup;
            if (rootView == null) return;

            var bottomNavView = FindBottomNavigationView(rootView);
            if (bottomNavView == null) return;

            //if (bottomNavView != null)
            //{
            //    // Loop through the menu items and resize icons
            //    for (int i = 0; i < bottomNavView.Menu.Size(); i++)
            //    {
            //        var menuItem = bottomNavView.Menu.GetItem(i);
            //        var icon = menuItem.Icon;

            //        if (icon != null)
            //        {
            //            int iconSize = (int)ConvertDpToPx(this, 60); // Change 36 to your desired icon size
            //            icon.SetBounds(0, 0, iconSize, iconSize);
            //            menuItem.SetIcon(icon);
            //        }
            //    }
            //}

            for (int i = 0; i < bottomNavView.ChildCount; i++)
            {
                var itemView = bottomNavView.GetChildAt(i);

                if (itemView is ViewGroup itemGroup)
                {
                    // Get all ImageViews (icons) inside this item
                    var icons = FindAllImageViews(itemGroup);

                    foreach (var iconView in icons)
                    {
                        int iconSize = (int)ConvertDpToPx(this, 32); // Adjust icon size

                        iconView.LayoutParameters.Width = iconSize;
                        iconView.LayoutParameters.Height = iconSize;
                        iconView.RequestLayout();
                    }
                }
            }
        }


        // Recursively find BottomNavigationView in the view hierarchy
        private BottomNavigationView? FindBottomNavigationView(ViewGroup viewGroup)
        {
            for (int i = 0; i < viewGroup.ChildCount; i++)
            {
                var child = viewGroup.GetChildAt(i);
                if (child is BottomNavigationView bottomNavView)
                    return bottomNavView;

                if (child is ViewGroup childGroup)
                {
                    var result = FindBottomNavigationView(childGroup);
                    if (result != null)
                        return result;
                }
            }
            return null;
        }

        private float ConvertDpToPx(Android.Content.Context context, float dp)
        {
            return dp * context.Resources.DisplayMetrics.Density;
        }

        // Recursively find ALL ImageView (icons) inside a ViewGroup
        private List<ImageView> FindAllImageViews(ViewGroup viewGroup)
        {
            List<ImageView> imageViews = new List<ImageView>();

            for (int i = 0; i < viewGroup.ChildCount; i++)
            {
                var child = viewGroup.GetChildAt(i);
                if (child is ImageView imageView)
                    imageViews.Add(imageView); // Add ImageView to list

                if (child is ViewGroup childGroup)
                {
                    imageViews.AddRange(FindAllImageViews(childGroup));
                }
            }
            return imageViews;
        }

        private List<TextView> FindAllTextViews(ViewGroup viewGroup)
        {
            List<TextView> textViews = new List<TextView>();

            for (int i = 0; i < viewGroup.ChildCount; i++)
            {
                var child = viewGroup.GetChildAt(i);
                if (child is TextView textView)
                    textViews.Add(textView);

                if (child is ViewGroup childGroup)
                {
                    textViews.AddRange(FindAllTextViews(childGroup));
                }
            }
            return textViews;
        }
    }
}