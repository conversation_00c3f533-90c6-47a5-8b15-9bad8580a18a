<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>Dr. Muscle</string>
	<key>CFBundleIdentifier</key>
	<string>com.drmaxmuscle.max</string>
	<key>CFBundleName</key>
	<string>Dr. Muscle</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>audio</string>
		<string>remote-notification</string>
	</array>
	<key>UISupportedArchitectures</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/iosicon</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoadsForMedia</key>
		<true/>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSUserTrackingUsageDescription</key>
		<string>This data will be used to provide you with relevant and personalized content.</string>
	</dict>
	<key>GIDClientID</key>
	<string>707210235326-ldcslmjtnjib5bklf23efrhp8u9qrpq3.apps.googleusercontent.com</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1865252523754972</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.707210235326-ldcslmjtnjib5bklf23efrhp8u9qrpq3</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>drmuscle</string>
			</array>
		</dict>
	</array>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<string>BackgroundWorkTimerInBackground</string>
	</array>
	<key>NSHealthShareUsageDescription</key>
	<string>To save total workout duration </string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>To give total workout duration </string>
	<key>NSCalendarsUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSContactsUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSAppleMusicUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSSiriUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>To share workout or exercise redords</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>14.0</string>
	<!--<key>FirebaseCrashlyticsCollectionEnabled</key>
	<true/>-->
</dict>
</plist>
