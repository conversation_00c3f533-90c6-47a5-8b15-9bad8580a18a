# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: CICD_Common_Bhautik

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:  # Manual trigger only

jobs:
  build-ios:
    name: iOS Build (macOS)
    if: github.ref == 'refs/heads/Firebase_Common_Auth_Google_Code'
    runs-on: macos-15

    steps:
    - uses: actions/checkout@v4

    - name: Install Sentry CLI
      run: |
          brew install getsentry/tools/sentry-cli
          echo "Sentry CLI installed successfully"

    - name: Verify SENTRY_AUTH_TOKEN is Set
      run: echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" | sed 's/./& /g'

    - name: Authenticate with Sentry
      env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      run: |
          if [ -z "$SENTRY_AUTH_TOKEN" ]; then
            echo "❌ SENTRY_AUTH_TOKEN is not set!"
            exit 1
          else
            echo "✅ SENTRY_AUTH_TOKEN is set, proceeding with authentication..."
          fi
          sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"
    - name: Cache .NET SDK and MAUI Workloads
      id: dotnet-cache
      uses: actions/cache@v3
      with:
          path: ~/.dotnet
          key: dotnet-${{ runner.os }}-${{ hashFiles('global.json') }}
          restore-keys: |
            dotnet-${{ runner.os }}-

    - name: Setup .NET
      if: steps.dotnet-cache.outputs.cache-hit != 'true'
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x
    - name: Verify .NET SDK Version
      run: dotnet --version

    - name: Install .NET MAUI (Only if not cached)
      if: steps.dotnet-cache.outputs.cache-hit != 'true'
      run: dotnet workload install maui

    - name: Cache NuGet Packages
      id: nuget-cache
      uses: actions/cache@v3
      with:
          path: ~/.nuget/packages
          key: nuget-${{ runner.os }}-${{ hashFiles('**/packages.lock.json') }}
          restore-keys: |
            nuget-${{ runner.os }}-
    - name: Restore dependencies
      if: steps.nuget-cache.outputs.cache-hit != 'true'
      run: dotnet restore DrMaxMuscle/DrMaxMuscle.csproj -p:BuildPlatform=ios

    - name: Import Code-Signing Certificates
      uses: apple-actions/import-codesign-certs@v3
      with:
        p12-file-base64: "${{ secrets.P12_CERTIFICATE }}"
        p12-password: "${{ secrets.P12_CERTIFICATE_PASSWORD }}"
    - name: Decode and import iOS certificate
      run: |
        echo "${{ secrets.P12_CERTIFICATE }}" | base64 --decode > /tmp/certificate.p12
        ls -l /tmp/certificate.p12
        security create-keychain -p "" build.keychain
        security unlock-keychain -p "" build.keychain
        security import /tmp/certificate.p12 -k ~/Library/Keychains/build.keychain -P "${{ secrets.P12_CERTIFICATE_PASSWORD }}" -T /usr/bin/codesign
        security find-identity -v -p codesigning
        rm /tmp/certificate.p12

    - name: Apple Provisioning Profile Action
      uses: nickwph/apple-provisioning-profile-action@v1.0.0
      with:
        profile-file: iOS_Certificates/2024_06_10_Jignesh.mobileprovision

    - name: Build ios
      run:  dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-ios -c Release -p:BuildPlatform=ios -p:GenerateIpa=true -p:RuntimeIdentifier=ios-arm64

    - name: Check if IPA file exists
      run: |
        if [ -f "DrMaxMuscle/bin/Release/net8.0-ios/ios-arm64/publish/DrMaxMuscle.ipa" ]; then
          echo "✅ IPA file exists!"
        else
          echo "❌ IPA file is missing!"
          exit 1
        fi

    - name: Install private API key P8
      env:
        PRIVATE_API_KEY_BASE64: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
      run: |
        mkdir -p ~/private_keys
        echo -n "$PRIVATE_API_KEY_BASE64" | base64 --decode --output AuthKey_$API_KEY.p8
        cp *.p8 ~/private_keys

    - name: Upload app to TestFlight
      env:
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
        API_ISSUER : ${{ secrets.APPSTORE_ISSUER_ID  }}
        IPA_PATH: DrMaxMuscle/bin/Release/net8.0-ios/ios-arm64/publish/DrMaxMuscle.ipa
      run: xcrun altool --upload-app -f $IPA_PATH -t ios --apiKey $API_KEY --apiIssuer $API_ISSUER


  build-android:
    name: Android Build (Linux)
    if: github.ref == 'refs/heads/Firebase_Common_Auth_Google_Code'
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Install Sentry CLI
      run: |
          curl -sL https://sentry.io/get-cli/ | bash
          echo "Sentry CLI installed successfully"
    - name: Verify SENTRY_AUTH_TOKEN is Set
      run: echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" | sed 's/./& /g'

    - name: Authenticate with Sentry
      env:
        SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      run: |
        if [ -z "$SENTRY_AUTH_TOKEN" ]; then
          echo "❌ SENTRY_AUTH_TOKEN is not set!"
          exit 1
        else
          echo "✅ SENTRY_AUTH_TOKEN is set, proceeding with authentication..."
        fi
        sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"
    - name: Setup .NET 8 (If Cache Miss)
      if: steps.cache-dotnet.outputs.cache-hit != 'true'
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: 8.0.x

    - name: Verify .NET SDK Version
      run: dotnet --version

    - name: Install .NET MAUI Workload for Android (If Cache Miss)
      if: steps.cache-dotnet.outputs.cache-hit != 'true'
      run: dotnet workload install maui-android

    - name: Restore Dependencies (If Cache Miss)
      if: steps.cache-nuget.outputs.cache-hit != 'true'
      run: dotnet restore DrMaxMuscle/DrMaxMuscle.csproj -p:BuildPlatform=android

    - name: Install Android Dependencies (If Cache Miss)
      if: steps.cache-android.outputs.cache-hit != 'true'
      run: |
        sudo apt update
        sudo apt install -y openjdk-17-jdk unzip
        export ANDROID_HOME=$HOME/Android/Sdk
        export PATH=$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$PATH
        mkdir -p $ANDROID_HOME/cmdline-tools
        cd $ANDROID_HOME/cmdline-tools
        wget https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip -O commandlinetools.zip
        unzip commandlinetools.zip
        mv cmdline-tools latest
        export PATH=$ANDROID_HOME/cmdline-tools/latest/bin:$PATH
        sdkmanager --licenses
        sdkmanager "platform-tools" "platforms;android-34" "build-tools;34.0.0"
    - name: 🔐 Decode and save service-account.json
      run: |
        echo "${{ secrets.GOOGLE_SERVICE_JSON }}" | base64 -d > service-account.json

    - name: Build Android App
      run: dotnet publish DrMaxMuscle/DrMaxMuscle.csproj -f net8.0-android -c Release --no-restore -p:BuildPlatform=android -p:AndroidSigningKeystore=keystoreFile.keystore -p:AndroidSigningKeyAlias=publishingdoc -p:AndroidSigningKeyPass='${{ secrets.NEW_PASSWORD_ALIAS }}' -p:AndroidSigningStorePass='${{ secrets.KEYSTORE_PASSWORD }}'

    - name: Ensure Directories Exist
      run: mkdir -p app-release

    - name: Move AAB to app-release
      run: |
        if [ -f "DrMaxMuscle/bin/Release/net8.0-android/publish/com.drmaxmuscle.dr_max_muscle-Signed.aab" ]; then
          mv DrMaxMuscle/bin/Release/net8.0-android/publish/com.drmaxmuscle.dr_max_muscle-Signed.aab app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab
        else
          echo "Error: AAB file not found!"
          exit 1
        fi
    - name: Setup Keystore File
      run: echo "${{ secrets.KEYSTORE_FILE_LINUX }}" | base64 --decode > keystoreFile.keystore

    - name: Verify Keystore File
      run: |
        ls -l keystoreFile.keystore
        file keystoreFile.keystore
        echo "Keystore file size:"
          stat --format="%s bytes" keystoreFile.keystore  # Linux

    - name: Verify SHA1 Signature of Keystore
      run: keytool -list -v -keystore keystoreFile.keystore -alias publishingdoc -storepass ${{ secrets.KEYSTORE_PASSWORD }}

    - name: Remove Debug Signature
      run: |
        zip -d app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab 'META-INF/*'
    - name: Sign AAB
      run: |
        jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 \
          -keystore keystoreFile.keystore \
          -storepass "${{ secrets.KEYSTORE_PASSWORD }}" \
          -keypass "${{ secrets.KEYSTORE_PASSWORD }}" \
          app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab \
          publishingdoc

    - name: Verify SHA1 Signature of AAB
      run: keytool -printcert -jarfile app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab

    - name: Decode Google Play JSON Key
      run: echo "${{ secrets.GOOGLE_SERVICE_JSON }}" | base64 --decode > google-play-key.json

    - name: Upload to Play Store Internal Testing
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJson: google-play-key.json
        packageName: com.drmaxmuscle.dr_max_muscle  # Change to your package name
        releaseFiles: app-release/com.drmaxmuscle.dr_max_muscle-Signed.aab
        track: internal
        status: completed  # Change from "draft" to "completed"
