<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Views.EndCheckinPopup"
                   CloseWhenBackgroundIsClicked="false"
               xmlns:app="clr-namespace:DrMaxMuscle.Constants"
            xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
            xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:convertors="clr-namespace:DrMaxMuscle.Convertors"
                xmlns:local="clr-namespace:DrMaxMuscle.Views"
                   xmlns:oxy="clr-namespace:OxyPlot.Maui.Skia;assembly=OxyPlot.Maui.Skia"
                   xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui">
    <Frame
    Padding="0"
    CornerRadius="4"
    HasShadow="False"
    IsClippedToBounds="True"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="CenterAndExpand"
    BackgroundColor="White"
    Margin="20">
        <Grid>
            <StackLayout
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            Padding="20,0,20,10"
            BackgroundColor="White"
            AbsoluteLayout.LayoutFlags="All"
            AbsoluteLayout.LayoutBounds="0, 0, 1, 1">

                <StackLayout
                x:Name="ScrollContentToShare"
                BackgroundColor="White"
                VerticalOptions="FillAndExpand">
                    <StackLayout
                    VerticalOptions="FillAndExpand">
                        <StackLayout
                        x:Name="ResultStackLayout"
                        VerticalOptions="FillAndExpand"
                        Padding="0,20,0,0">
                            <ffimageloading:CachedImage
                                ErrorPlaceholder="backgroundblack.png"
                            IsVisible="false"
                            Margin="0,0,0,0"
                            x:Name="ImgName"
                            WidthRequest="100"
                            HeightRequest="100"
                            HorizontalOptions="Center"
                            Aspect="AspectFit"
                            VerticalOptions="Start" />
                            <Label
                            x:Name="lblPercentage"
                            Text=""
                            IsVisible="true"
                            HorizontalOptions="Center"
                            FontSize="43"
                                FontAttributes="Bold"
                            Style="{StaticResource LabelStyle}"
                            TextColor="Black" />
                            <StackLayout
                                Padding="0"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"
                                Orientation="Horizontal">
                                <Label
                            Margin="0,-5,0,0"
                            Text=""
                            x:Name="lblResult1"
                            HorizontalOptions="Center"
                            FontSize="24"
                            FontAttributes="Bold"
                            TextColor="Black"
                            HorizontalTextAlignment="Center"
                            MaxLines="1" />

                                <Image
                                    Source="infoicon.png"
                                    Aspect="AspectFit"
                                    Margin="0,-2,0,0"
                                    HeightRequest="24"
                                    WidthRequest="30"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                     >
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="InfoIcon_Tapped2"/>
                                    </Image.GestureRecognizers>
                                </Image>

                            </StackLayout>
                            <!--<Label
                            x:Name="lblExerciseName"
                            HorizontalOptions="Center"
                            MaxLines="2"
                            LineBreakMode="TailTruncation"
                            Style="{StaticResource LabelStyle}"
                            FontSize="18"
                            TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />-->
                            <Grid Margin="0,17,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition
                                    Width="10" />
                                    <ColumnDefinition
                                    Width="*" />
                                    <ColumnDefinition
                                    Width="*" />
                                    <ColumnDefinition
                                    Width="10" />
                                </Grid.ColumnDefinitions>
                                <StackLayout
                                Grid.Column="1"
                                HorizontalOptions="FillAndExpand">
                                    <Image
                                    x:Name="IconHistory"
                                    Source="history.png"
                                    Aspect="AspectFit"
                                    HeightRequest="32"
                                    HorizontalOptions="CenterAndExpand" />
                                    <Label
                                    x:Name="lblLastTimeWeightTrend"
                                    Text=""
                                    IsVisible="true"
                                    HorizontalOptions="Center"
                                    FontSize="22"
                                        FontAttributes="Bold"
                                    Style="{StaticResource LabelStyle}"
                                    TextColor="Black" />
                                    <Label
                                    x:Name="lblResult33"
                                    Text="Last check-in"
                                    IsVisible="true"
                                    HorizontalOptions="Center"
                                    Style="{StaticResource LabelStyle}"
                                    FontSize="18"
                                    TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                                </StackLayout>
                                <StackLayout
                                Grid.Column="2"
                                HorizontalOptions="FillAndExpand">
                                    <Image
                                    x:Name="IconCalander"
                                    Source="calander.png"
                                    Aspect="AspectFit"
                                    HeightRequest="32"
                                    HorizontalOptions="CenterAndExpand" />
                                    <Label
                                    x:Name="lblTodayWeightTrend"
                                    Text=""
                                    IsVisible="true"
                                    HorizontalOptions="Center"
                                    Style="{StaticResource LabelStyle}"
                                     FontSize="22"
     FontAttributes="Bold"
                                    TextColor="Black" />
                                    <Label
                                    x:Name="lblResult211"
                                    Text="Today"
                                    IsVisible="true"
                                    HorizontalOptions="Center"
                                    Style="{StaticResource LabelStyle}"
                                    FontSize="18"
                                    TextColor="{x:Static app:AppThemeConstants.OffBlackColor}" />
                                </StackLayout>

                            </Grid>

                            <Grid
                            Margin="0,15,0,0"
                            HeightRequest="200">
                                <StackLayout>

                                    <!--<Label
                                x:Name="LblWeightGoal2"
                                Margin="29,0,0,0"
                                HorizontalOptions="StartAndExpand"
                                VerticalOptions="Center"
                                LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
                                FontSize="20"
                                TextColor="Black"
                                FontAttributes="Bold"
                                Text="Body weight trend" />-->


                                    <oxy:PlotView x:Name="plotView" VerticalOptions="FillAndExpand" HeightRequest="160" />



                                    <Label
                        Margin="29,0,20,0"
                        x:Name="WeightArrowText"
                        FontSize="17"
                        HorizontalOptions="Start"
                        HorizontalTextAlignment="Start"
                            Text=""
                        TextColor="#AA000000" />

                                </StackLayout>
                                <!--<oxy:PlotView
                                x:Name="plotView"
                                IsVisible="true"
                                VerticalOptions="Start"
                                HeightRequest="170">
                            </oxy:PlotView>-->
                            </Grid>

                            <Label
                            VerticalOptions="FillAndExpand" />
                        </StackLayout>
                    </StackLayout>
                </StackLayout>

                <StackLayout
            Orientation="Vertical"
            VerticalOptions="EndAndExpand"
            HorizontalOptions="FillAndExpand"
            Padding="0,0,0,25">

                    <!--<t:DrMuscleButton x:Name="ShareWithFBButton" Padding="10,0,0,0" Image="facebook_f_white.png" VerticalOptions="EndAndExpand" HorizontalOptions="FillAndExpand" Text="Share record" IsVisible="true" Style="{StaticResource highEmphasisButtonStyle}" BackgroundColor="#7f3c5a99" BorderColor="#7f3c5a99" BorderWidth="2" TextColor="White" />-->

                    <!--Share exercise button -->
                    <!--<t:DrMuscleButton
                Style="{StaticResource menubuttonStyle}"
                Text="Share"
                BackgroundColor="Transparent"
                Margin="0"
                Padding="0"
                HeightRequest="60"
                HorizontalOptions="FillAndExpand"
                Clicked="ShareExerciseButton_Clicked"/>-->

                    <!--New Custom Share Exercise Button-->
                    <controls:DrMuscleImageButton
                Text="Share"
                Source="ic_share_exercise"
                        Margin="1,0"
                Clicked="ShareExerciseButton_Clicked"
                HeightRequest="60"/>

                    <!--Continue Option PancakeView-->
                    <Border
                Padding="0"
                Margin="0,8,0,0"
                HorizontalOptions="FillAndExpand" 
                Style="{StaticResource GradientBorderStyleBlue}"
                HeightRequest="72">
                        <Border.StrokeShape>
                            <Rectangle/>
                        </Border.StrokeShape>
                        <t:DrMuscleButton
                    x:Name="NextExerciseButton"
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="true"
                    Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White"/>
                    </Border>
                </StackLayout>
            </StackLayout>

            <!--<forms:ParticleView
        x:Name="MyParticleCanvas"
        FallingParticlesPerSecond="25.0"
        IsActive="False"
        IsRunning="False"
        HasFallingParticles="True"
        VerticalOptions="FillAndExpand"
        HorizontalOptions="FillAndExpand"
        InputTransparent="True"/>-->
        </Grid>
    </Frame>
</toolkit:PopupPage>