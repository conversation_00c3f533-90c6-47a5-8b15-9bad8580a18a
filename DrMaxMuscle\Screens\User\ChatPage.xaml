﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.User.ChatPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    Title="ChatPage"
    Shell.BackgroundColor="{StaticResource NavigationBarGradientBrush}">
    <!--<ContentPage.ToolbarItems>
        <ToolbarItem
            x:Name="SupportToolbarItem"
            Clicked="Support_Tapped"
            IsEnabled="False"
            Text="1:1 support" />
    </ContentPage.ToolbarItems>-->

    <Grid>
        <!--  Gradient Background  -->
        <Grid.Background>
            <LinearGradientBrush EndPoint="1,0">
                <GradientStop Offset="0.0" Color="#0C2432" />
                <GradientStop Offset="1.0" Color="#195276" />
            </LinearGradientBrush>
        </Grid.Background>

        <!--  Page content goes here  -->
        <!--  Add your UI elements here  -->
    </Grid>
    <NavigationPage.TitleView>
        <StackLayout
            Margin="{OnPlatform iOS='0,0,0,8'}"
            Orientation="Horizontal"
            Spacing="3">

            <Image HeightRequest="{OnPlatform iOS='30', Android='26'}" Source="dr_icon_white" />
            <Label
                x:Name="lblTitle"
                Margin="5,0,0,0"
                FontAttributes="Bold"
                FontSize="{OnPlatform Android='15',
                                      iOS='15'}"
                TextColor="White"
                VerticalTextAlignment="Center" />
        </StackLayout>
    </NavigationPage.TitleView>

</ContentPage>