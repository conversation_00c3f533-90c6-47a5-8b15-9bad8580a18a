﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Controls
{
    public class ExtendedEditorControl : Editor
    {
        //public static BindableProperty PlaceholderProperty
        //  = BindableProperty.Create(nameof(Placeholder), typeof(string), typeof(ExtendedEditorControl));

        //public static BindableProperty PlaceholderColorProperty
        //   = BindableProperty.Create(nameof(PlaceholderColor), typeof(Color), typeof(ExtendedEditorControl), Color.LightGray);

        public static BindableProperty HasRoundedCornerProperty
        = BindableProperty.Create(nameof(HasRoundedCorner), typeof(bool), typeof(ExtendedEditorControl), false);

        public static BindableProperty IsExpandableProperty
        = BindableProperty.Create(nameof(IsExpandable), typeof(bool), typeof(ExtendedEditorControl), false);

        public bool IsExpandable
        {
            get { return (bool)GetValue(IsExpandableProperty); }
            set { SetValue(IsExpandableProperty, value); }
        }
        public bool HasRoundedCorner
        {
            get { return (bool)GetValue(HasRoundedCornerProperty); }
            set { SetValue(HasRoundedCornerProperty, value); }
        }

        //public string Placeholder
        //{
        //    get { return (string)GetValue(PlaceholderProperty); }
        //    set { SetValue(PlaceholderProperty, value); }
        //}

        //public Color PlaceholderColor
        //{
        //    get { return (Color)GetValue(PlaceholderColorProperty); }
        //    set { SetValue(PlaceholderColorProperty, value); }
        //}

        public ExtendedEditorControl()
        {
            TextChanged += OnTextChanged;

        }

        ~ExtendedEditorControl()
        {
            TextChanged -= OnTextChanged;
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (IsExpandable)
                    InvalidateMeasure();

            }
            catch (Exception ex)
            {

            }

        }

    }
}