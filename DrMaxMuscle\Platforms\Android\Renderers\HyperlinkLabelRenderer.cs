﻿using Android.Text.Method;
using Android.Text.Util;
using Android.Views;
using Android.Widget;
using AndroidX.AppCompat.Widget;
using DrMaxMuscle.Controls;
using Java.Lang;
using Java.Util.Regex;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Method = Android.Text.Method;

namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class HyperlinkLabelRenderer : LabelHandler
    {
        //public static IPropertyMapper<HyperlinkLabel, LabelHandler> CustomMapper = new PropertyMapper<HyperlinkLabel, LabelHandler>(LabelHandler.Mapper)
        //{
        //    [nameof(HyperlinkLabel.RawText)] = MapRawText
        //};

        //public HyperlinkLabelRenderer() : base(CustomMapper)
        //{
        //}

        //private static void MapRawText(LabelHandler handler, HyperlinkLabel view)
        //{
        //    if (handler.PlatformView != null && view != null)
        //    {
        //        var renderer = (HyperlinkLabelRenderer)handler;
        //        renderer.UpdateText();
        //    }
        //}

        //private void UpdateText()
        //{
        //    if (VirtualView is HyperlinkLabel hyperlinkLabelElement)
        //    {
        //        string text = hyperlinkLabelElement.GetText(out List<HyperlinkLabelLink> links);
        //        if (!string.IsNullOrEmpty(text))
        //        {
        //            // Set the text on the platform view
        //            MainThread.BeginInvokeOnMainThread(() =>
        //            {
        //                PlatformView.Text = text;
        //                PlatformView.Visibility = ViewStates.Visible; // Make sure the view is visible
        //                PlatformView.LayoutParameters = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MatchParent, ViewGroup.LayoutParams.WrapContent); // Ensure it can resize correctly
        //                PlatformView.RequestLayout(); // Force redraw
        //                PlatformView.Invalidate(); // Force redraw
        //            });
        //            // Handle links
        //            foreach (var item in links)
        //            {
        //                var pattern = Pattern.Compile(Pattern.Quote(item.Text));
        //                Linkify.AddLinks(PlatformView, pattern, null,
        //                    new CustomMatchFilter(item.Start),
        //                    new CustomTransformFilter(item.Link));
        //            }

        //            PlatformView.MovementMethod = LinkMovementMethod.Instance;
        //            PlatformView.SetLinkTextColor(Constants.AppThemeConstants.BlueLightColor.ToAndroid()); // Set a visible color
        //        }
        //    }
        //}

        //protected override void Dispose(bool disposing)
        //{
        //    if (disposing)
        //    {
        //        // Cleanup if needed
        //    }
        //    base.Dispose(disposing);
        //}
    }

    public class CustomTransformFilter : Java.Lang.Object, Linkify.ITransformFilter
    {
        readonly string url;
        public CustomTransformFilter(string url)
        {
            this.url = url;
        }

        public string TransformUrl(Matcher match, string url) => this.url;
    }

    public class CustomMatchFilter : Java.Lang.Object, Linkify.IMatchFilter
    {
        readonly int start;
        public CustomMatchFilter(int start)
        {
            this.start = start;
        }

        public bool AcceptMatch(ICharSequence s, int start, int end) => start == this.start;
    }
}