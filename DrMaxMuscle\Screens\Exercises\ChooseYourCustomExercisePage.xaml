﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="DrMaxMuscle.Screens.Exercises.ChooseYourCustomExercisePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:DrMaxMuscle.Constants"
    xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
    xmlns:helpers="clr-namespace:DrMaxMuscle.Helpers"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:local="clr-namespace:DrMaxMuscle"
    xmlns:t="clr-namespace:DrMaxMuscle.Layout"
    Title="ChooseYourCustomExercisePage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <helpers:NegateBooleanConverter x:Key="BooleanInverter" />
        </ResourceDictionary>
    </ContentPage.Resources>
    <Grid Margin="20,0">
        <StackLayout HorizontalOptions="FillAndExpand"
                     VerticalOptions="FillAndExpand">
            <Frame x:Name="SearchContainer"
                   Margin="0,10,0,0"
                   Padding="0"
                   BackgroundColor="#f1f1f1"
                   CornerRadius="4"
                   HasShadow="False"
                   IsClippedToBounds="true">
                <StackLayout Orientation="Horizontal"
                             Spacing="0">
                    <Image x:Name="ImgSearch"
                           Margin="10,0,2,0"
                           Aspect="AspectFit"
                           HeightRequest="18"
                           Source="icon_search_gray.png"
                           VerticalOptions="Center"
                           WidthRequest="15" />
                    <t:DrMuscleEntry x:Name="SearchEntry"
                                     Margin="0"
                                     BackgroundColor="Transparent"
                                     HeightRequest="40"
                                     HorizontalOptions="FillAndExpand"
                                     TextChanged="Handle_SearchTextChanged"
                                     TextColor="Black"
                                     Focused="SearchEntry_Focused"
                                     Unfocused="SearchEntry_Unfocused" />
                    <Label x:Name="BtnCancel"
                           Margin="0,0,7,0"
                           IsVisible="false"
                           VerticalOptions="FillAndExpand"
                           VerticalTextAlignment="Center">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Tapped="Handle_CancelTapped" />
                        </Label.GestureRecognizers>
                    </Label>
                </StackLayout>
            </Frame>
            <StackLayout Padding="0"
                         BackgroundColor="Transparent"
                         VerticalOptions="FillAndExpand">
                <t:DrMuscleListView x:Name="ExpandableList"
                                    ios:ListView.SeparatorStyle="FullWidth"
                                    ios:ListView.GroupHeaderStyle="Grouped"
                                    BackgroundColor="Transparent"
                                    HasUnevenRows="True"
                                    IsGroupingEnabled="True"
                                    VerticalOptions="FillAndExpand"
                                    ItemsSource="{Binding ExeList}"
                                    Scrolled="ExerciseListView_Scrolled"
                                    SeparatorColor="#264457"
                                    SeparatorVisibility="Default">
                    <t:DrMuscleListView.ItemTemplate>
                        <DataTemplate>
                            <ViewCell Height="60"
                                      BindingContextChanged="OnBindingContextChanged">
                                <StackLayout Padding="7,0,0,0"
                                             BackgroundColor="Transparent"
                                             Orientation="Horizontal">
                                    <!--                        <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">-->
                                    <Label HorizontalOptions="StartAndExpand"
                                           Style="{StaticResource LabelStyle}"
                                           Text="{Binding Label}"
                                           VerticalOptions="Center"
                                           VerticalTextAlignment="Center" />
                                    <!--</StackLayout>-->
                                    <StackLayout HorizontalOptions="End"
                                                 Orientation="Horizontal">
                                        <t:DrMuscleButton BackgroundColor="Transparent"
                                                          Clicked="OnVideo"
                                                          CommandParameter="{Binding .}"
                                                          ContentLayout="Top,0"
                                                          HorizontalOptions="End"
                                                          ImageSource="play_dark_blue.png"
                                                          IsVisible="false"
                                                          Style="{StaticResource ItemContextVideoButton}"
                                                          Text="{Binding [Video].Value, Mode=OneWay, Source={x:Static helpers:ResourceLoader.Instance}}"
                                                          TextColor="#195377"
                                                          VerticalOptions="Center" />
                                        <t:DrMuscleButton Margin="0,0,-3,0"
                                                          BackgroundColor="Transparent"
                                                          Clicked="OnReset"
                                                          CommandParameter="{Binding .}"
                                                          ContentLayout="Top,0"
                                                          HorizontalOptions="End"
                                                          ImageSource="more_dark_blue.png"
                                                          Style="{StaticResource ItemContextVideoButton}"
                                                          Text="More"
                                                          TextColor="#195377"
                                                          VerticalOptions="Center"
                                                          WidthRequest="50" />

                                    </StackLayout>
                                </StackLayout>
                            </ViewCell>
                        </DataTemplate>
                    </t:DrMuscleListView.ItemTemplate>
                    <t:DrMuscleListView.GroupHeaderTemplate>
                        <DataTemplate>
                            <ViewCell ios:Cell.DefaultBackgroundColor="Transparent">
                                   <StackLayout Orientation="Vertical" 
                                         BackgroundColor="Transparent"
                                         Padding="0">
                                <StackLayout Padding="0,12,9,12"
                                             BackgroundColor="Transparent"
                                             HorizontalOptions="FillAndExpand"
                                             Orientation="Horizontal">
                                    <Label BackgroundColor="Transparent"
                                           HorizontalOptions="StartAndExpand"
                                           Style="{StaticResource BoldLabelStyle}"
                                           Text="{Binding Name}"
                                           TextColor="{x:Static app:AppThemeConstants.OffBlackColor}"
                                           VerticalOptions="CenterAndExpand"
                                           VerticalTextAlignment="Center" />
                                    <Image x:Name="StateImage"
                                           Aspect="AspectFit"
                                           BackgroundColor="Transparent"
                                           HeightRequest="30"
                                           HorizontalOptions="End"
                                           PropertyChanged="StateImage_PropertyChanged"
                                           Source="{Binding StateIcon}"
                                           VerticalOptions="CenterAndExpand"
                                           WidthRequest="25" />
                                    <StackLayout.GestureRecognizers>
                                        <TapGestureRecognizer CommandParameter="{Binding .}"
                                                              NumberOfTapsRequired="1"
                                                              Tapped="Section_Tapped" />
                                    </StackLayout.GestureRecognizers>
                                </StackLayout>
                                     <BoxView HeightRequest="1"
                                         HorizontalOptions="FillAndExpand"
                                         VerticalOptions="End"
                                         BackgroundColor="#264457">       
                                </BoxView>
                            </StackLayout>
                            </ViewCell>
                        </DataTemplate>
                    </t:DrMuscleListView.GroupHeaderTemplate>
                    <t:DrMuscleListView.Footer>
                        <StackLayout HeightRequest="100"
                                     BackgroundColor="Transparent" />
                    </t:DrMuscleListView.Footer>
                </t:DrMuscleListView>
            </StackLayout>
        </StackLayout>

        <Frame Padding="0"
               Margin="0,0,0,20"
               IsClippedToBounds="true"
               CornerRadius="6"
               VerticalOptions="End"
               HorizontalOptions="FillAndExpand"
               HeightRequest="68"
               BorderColor="Transparent"
               Style="{StaticResource GradientStackStyleBlue}">
            <t:DrMuscleButton CornerRadius="6"
                              VerticalOptions="Fill"
                              HeightRequest="68"
                              FontSize="{x:Static constnats:AppThemeConstants.CapitalTitleFontSize}"
                              HorizontalOptions="FillAndExpand"
                              Text="CREATE CUSTOM EXERCISE"
                              Style="{StaticResource highEmphasisButtonStyle}"
                              BackgroundColor="Transparent"
                              BorderColor="Transparent"
                              TextColor="White"
                              Clicked="NewExerciseTapped" />
        </Frame>

    </Grid>
</ContentPage>
