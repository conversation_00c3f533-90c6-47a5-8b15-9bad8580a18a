﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
            xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
            xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
            CanBeDismissedByTappingOutsideOfPopup="True"
               Color="Transparent"
            x:Class="DrMaxMuscle.Views.MealRecipePopup"
                  >
    <Grid x:Name="MainFrame">
        
        <Frame
        
        Margin="0"
        Padding="{OnPlatform Android='12', iOS='2,18,2,18'}"
        BorderColor="Transparent"
        CornerRadius="6"
        HasShadow="False"
        IsClippedToBounds="True"
        VerticalOptions="Center"    
        HorizontalOptions="FillAndExpand"
        >

        <Grid RowSpacing="2" Padding="{OnPlatform Android='8,0,8,8',iOS='17,8,17,4'}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <!-- Add elements in respective rows -->
            <Grid Grid.Row="0">
                <StackLayout
                    Orientation="Vertical" Spacing="4">

                    <ffimageloading:CachedImage  Margin="0,0,0,0" x:Name="ImgName" WidthRequest="66" HeightRequest="70" ErrorPlaceholder="backgroundblack.png" HorizontalOptions="Center" VerticalOptions="Start" Source="cooking_recipe" />
                    <Label x:Name="LblHeading" Margin="0,8,0,0"  HorizontalOptions="Center" FontSize="22" FontAttributes="Bold" TextColor="Black" HorizontalTextAlignment="Center" />
                </StackLayout>
            </Grid>

            <ScrollView Grid.Row="1"
                x:Name="subHeadScroll" Margin="4,8" 
                HorizontalOptions="FillAndExpand" 
                VerticalOptions="Start" 
                BackgroundColor="Transparent">
                <Grid 
                    HorizontalOptions="FillAndExpand" 
                    Padding="0" 
                    x:Name="mainGrid" 
                    RowSpacing="3">

                    <!--<Label 
                        x:Name="LblSubHead" 
                        HorizontalOptions="StartAndExpand"  
                        FontSize="17" 
                        TextColor="#26262B"   
                        LineHeight="{OnPlatform Android='1',iOS='1.2'}"
                        />-->
                </Grid>
            </ScrollView>

            <!--<StackLayout
                Grid.Row="2"
                Padding="0"
                x:Name="OkAction"
                IsClippedToBounds="true"
                VerticalOptions="EndAndExpand"
                HorizontalOptions="FillAndExpand"
                Margin="4,0,4,0"
                HeightRequest="66">
                <StackLayout.Background>
                    <LinearGradientBrush EndPoint="1,0">
                        <GradientStop Color="#0C2432" Offset="0.0" />
                        <GradientStop Color="#195276" Offset="1.0" />
                    </LinearGradientBrush>
                </StackLayout.Background>
                <Label
                    x:Name="OkButton"
                    Text="OK cool"
                    FontAttributes="Bold"
                    BackgroundColor="Transparent"
                    VerticalTextAlignment="Center"
                    TextColor="White"
                    VerticalOptions="CenterAndExpand"
                    HorizontalTextAlignment="Center"
                    HorizontalOptions="FillAndExpand"
                    Margin="0"
                    />
                <StackLayout.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OkButton_Tapped"/>
                </StackLayout.GestureRecognizers>
            </StackLayout>-->

            <Border
                Grid.Row="2"
                Padding="0"
                Margin="0,4,0,0"
                Stroke="Transparent"
                HorizontalOptions="FillAndExpand" 
                Style="{StaticResource GradientBorderStyleBlue}"
                HeightRequest="66">
                <Button
                    x:Name="OkButton"
                    Text="Back to plan"
                    VerticalOptions="FillAndExpand"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="true"
                    Style="{StaticResource highEmphasisButtonStyle}"
                    BackgroundColor="Transparent"
                    BorderColor="Transparent"
                    TextColor="White"
                    Clicked="BackToPlan_Clicked"/>
            </Border>


        </Grid>

    </Frame>
    </Grid>


</toolkit:Popup>
