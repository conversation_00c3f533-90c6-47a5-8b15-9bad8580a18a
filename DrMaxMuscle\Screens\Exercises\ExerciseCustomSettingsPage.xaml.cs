﻿using System.Text.RegularExpressions;
using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Workouts;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Services;

namespace DrMaxMuscle.Screens.Exercises;

public partial class ExerciseCustomSettingsPage : ContentPage
{
    bool IsLoading = false;
    Dictionary<string, int> DurationToDays = new Dictionary<string, int>();
    Dictionary<string, int> EquipmentsToIds = new Dictionary<string, int>();
    Dictionary<string, int> ExerciseLevels = new Dictionary<string, int>();

    int? style = null;
    int BackOff = 0;

    string SelectedBodyPart = "";
    string SelectedEquipment = "";
    int SelectedIndexExerciseLevel = 0;
    int SelectedIndexForSet = 0;

    public ExerciseCustomSettingsPage()
    {
        InitializeComponent();
        RefreshLocalized();
        DeleteButton.Clicked += DeleteButton_Clicked;
        ResetButton.Clicked += ResetButton_Clicked;
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        RepsMinimumLess.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum > 5)
                currentRepsMinimum = currentRepsMinimum - 1;
            else
            {
                if (currentRepsMinimum == 1)
                {
                    // UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = "Minimum reps must be 1.",
                    //     Title = AppResources.LessThan5Reps
                    // });

                    await HelperClass.DisplayCustomPopupForResult(AppResources.LessThan5Reps,
                            "Minimum reps must be 1.","OK","");
                    return;
                }
                //UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                //    Title = AppResources.LessThan5Reps
                //});
                if (CurrentLog.Instance.IsMinRepsWarning)
                {
                    currentRepsMinimum = currentRepsMinimum - 1;
                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                }
                else
                {
                   var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum - 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                        

                    // UserDialogs.Instance.Confirm(new ConfirmConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //     Title = "Are you sure?",
                    //     OkText = "Confirm",
                    //     CancelText = "Cancel",
                    //     OnAction = (obj) =>
                    //     {
                    //         if (obj)
                    //         {
                    //             CurrentLog.Instance.IsMinRepsWarning = true;
                    //             currentRepsMinimum = currentRepsMinimum - 1;
                    //             RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                    //         }
                    //     }
                    // });
                }
            }
            RepsMinimumLabel.Text = currentRepsMinimum.ToString();
        };

        RepsMinimumMore.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMinimum >= currentRepsMaximum)
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,
                // });
                await HelperClass.DisplayCustomPopupForResult("",
                            AppResources.PleaseIncreaseMaxRepsToIncreaseMinimumRepsFurther,"OK","");
            else
            {
                if (currentRepsMinimum < 30)
                    currentRepsMinimum = currentRepsMinimum + 1;
                else
                {
                    //UserDialogs.Instance.AlertAsync(new AlertConfig()
                    //{
                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //    Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    //    Title = AppResources.MoreThan30Reps
                    //});
                    if (CurrentLog.Instance.IsMaxRepsWarning)
                    {
                        currentRepsMinimum = currentRepsMinimum + 1;
                        RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                    }
                    else
                    {

                     var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum + 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                      

                        // UserDialogs.Instance.Confirm(new ConfirmConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = AppResources.SetsOfMoreThan30RepsAreRatherpainfulTakeALongTime,
                        //     Title = "Are you sure?",
                        //     OkText = "Confirm",
                        //     CancelText = "Cancel",
                        //     OnAction = (obj) =>
                        //     {
                        //         if (obj)
                        //         {
                        //             CurrentLog.Instance.IsMinRepsWarning = true;
                        //             currentRepsMinimum = currentRepsMinimum + 1;
                        //             RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                        //         }
                        //     }
                        // });
                    }
                }
                RepsMinimumLabel.Text = currentRepsMinimum.ToString();
            }
        };

        TimebaseSwitch.Toggled += async (sender, e) =>
        {
            if (TimebaseSwitch.IsToggled)
            {
                CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsTimeBased = true;
            }
            else
            {
                CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsTimeBased = false;
            }
            SaveCustomSettings();
        };
        UnilateralSwitch.Toggled += async (sender, e) =>
        {
            if (UnilateralSwitch.IsToggled)
            {
                CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsUnilateral = true;
            }
            else
            {
                CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsUnilateral = false;
            }
            SaveCustomSettings();
        };

        FavoriteSwitch.Toggled += async (sender, e) => {
            SaveCustomSettings();
            if (!Config.IsFirstFavoritePopup && !IsLoading)
            {
                //
                if (!Config.IsFirstFavoritePopup)
                {
                    Config.IsFirstFavoritePopup = true;
                    var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    var modalPage = new Views.RGGeneralPopup("medal.png", "Success!", "First exercise added to favorites", "View exercise");
                    if (modalPage != null)
                    {
                        modalPage.Closed += (sender2, e2) =>
                        {
                            waitHandle.Set();
                        };
                        await Application.Current.MainPage.ShowPopupAsync(modalPage);

                        await Task.Run(() => waitHandle.WaitOne());
                    }

                }
            }
            CurrentLog.Instance.IsFavouriteUpdated = true;
        };
        CustomRepsSwitch.Toggled += async (sender, e) =>
        {
            if (CustomRepsSwitch.IsToggled)
            {
                LoadCustomReps();
                RepsStack.IsVisible = true;
            }
            else
            {
                RepsStack.IsVisible = false;
            }
            SaveCustomSettings();
        };
        CustomSetsSwitch.Toggled += async (sender, e) =>
        {
            if (CustomSetsSwitch.IsToggled)
            {
                SetsStack.IsVisible = true;
            }
            else
            {
                SetsStack.IsVisible = false;
            }
            SaveCustomSettings();

        };
        IncrementSwitch.Toggled += async (sender, e) =>
        {
            if (IncrementSwitch.IsToggled)
            {
                StackIncrements.IsVisible = true;
            }
            else
            {
                StackIncrements.IsVisible = false;
            }
            SaveCustomSettings();
        };

        WarmupSwitch.Toggled += async (sender, e) =>
        {
            if (WarmupSwitch.IsToggled)
            {
                StackWarmup.IsVisible = true;
            }
            else
            {
                StackWarmup.IsVisible = false;
            }
            SaveCustomSettings();
        };

        RepsMaximumLess.Clicked += async (sender, e) =>
        {

            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum <= currentRepsMinimum)
                // UserDialogs.Instance.AlertAsync(new AlertConfig()
                // {
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     Message = AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,
                // });
                await HelperClass.DisplayCustomPopupForResult("",
                            AppResources.PleaseDecreaseMinimumRepsToDecreaseMaxRepsFurther,"OK","");
            else
            {
                if (currentRepsMaximum > 5)
                    currentRepsMaximum = currentRepsMaximum - 1;
                else
                {
                    //UserDialogs.Instance.AlertAsync(new AlertConfig()
                    //{
                    //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //    Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    //    Title = AppResources.LessThan5Reps
                    //});
                    if (CurrentLog.Instance.IsMinRepsWarning)
                    {
                        currentRepsMaximum = currentRepsMaximum - 1;
                        RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                    }
                    else
                    {
                      var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum - 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                      

                        // UserDialogs.Instance.Confirm(new ConfirmConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = AppResources.SetsOfLessThan5RepsAreVeryHeavyAndDoNotBuildMuscleFasterForYourSafetyMinimumRepsIs5,
                        //     Title = "Are you sure?",
                        //     OkText = "Confirm",
                        //     CancelText = "Cancel",
                        //     OnAction = (obj) =>
                        //     {
                        //         if (obj)
                        //         {
                        //             CurrentLog.Instance.IsMaxRepsWarning = true;
                        //             currentRepsMaximum = currentRepsMaximum - 1;
                        //             RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                        //         }
                        //     }
                        // });
                    }
                }
                RepsMaximumLabel.Text = currentRepsMaximum.ToString();
            }
        };

        RepsMaximumMore.Clicked += async (sender, e) =>
        {
            int currentRepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int currentRepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);

            if (currentRepsMaximum < 30)
                currentRepsMaximum = currentRepsMaximum + 1;
            else
            {
                //UserDialogs.Instance.AlertAsync(new AlertConfig()
                //{
                //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //    Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                //    Title = AppResources.MoreThan30Reps
                //});
                if (CurrentLog.Instance.IsMaxRepsWarning)
                {
                    currentRepsMaximum = currentRepsMaximum + 1;
                    RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                }
                else
                {
                   var ShowPopUp = await HelperClass.DisplayCustomPopup("Are you sure?",AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                    "Confirm","Cancel");
                        ShowPopUp.ActionSelected += async (sender,action) => {

                                if (action == Views.PopupAction.OK)
                                {
                                    CurrentLog.Instance.IsMinRepsWarning = true;
                                    currentRepsMinimum = currentRepsMinimum + 1;
                                    RepsMinimumLabel.Text = currentRepsMinimum.ToString();
                                }
                                
                        };
                      

                    // UserDialogs.Instance.Confirm(new ConfirmConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.SetsOfMoreThan30RepsAreRatherPainfulTakeALongTimeToDoAndDoNotBuildMuscleFasterForBestResultsMaxRepsIs30,
                    //     Title = "Are you sure?",
                    //     OkText = "Confirm",
                    //     CancelText = "Cancel",
                    //     OnAction = (obj) =>
                    //     {
                    //         if (obj)
                    //         {
                    //             CurrentLog.Instance.IsMaxRepsWarning = true;
                    //             currentRepsMaximum = currentRepsMaximum + 1;
                    //             RepsMaximumLabel.Text = currentRepsMaximum.ToString();
                    //         }
                    //     }
                    // });
                }
            }

            RepsMaximumLabel.Text = currentRepsMaximum.ToString();
        };


        SaveSetCountButton.Clicked += async (sender, e) =>
        {
            try
            {
                if (!string.IsNullOrEmpty(SetEntry.Text))
                {
                    var count = int.Parse(SetEntry.Text.ReplaceWithDot());
                    if (count < 2 || count > 99)
                    {
                        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                        // {
                        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                        //     Message = "At least 2 work sets",
                        // });
                        await HelperClass.DisplayCustomPopupForResult("",
                            "At least 2 work sets","OK","");

                        return;
                    }
                    SaveCustomSettings();
                }
                else
                {
                    SaveCustomSettings();
                }

            }
            catch (Exception)
            {

            }
        };
        // Bouton save custom reps
        SaveCustomRepsButton.Clicked += async (sender, e) =>
        {
            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    ConnectionErrorPopup();
                    return;
                }
                SaveCustomSettings();

            }
            catch (Exception)
            {
                ConnectionErrorPopup();
            }
        };

        SaveWarmupButton.Clicked += async (sender, e) =>
        {
            try
            {
                SaveCustomSettings();
            }
            catch (Exception)
            {

            }
        };

        SaveIncrementsButton.Clicked += async (sender, e) =>
        {
            try
            {
                SaveCustomSettings();
                //if (string.IsNullOrEmpty(UnitEntry.Text))
                //{
                //    //await UserDialogs.Instance.AlertAsync("Please enter your increments.", "Error!");
                //    return;
                //}
                //var text = UnitEntry.Text.Replace(",", ".");
                //var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                //if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                //{

                //}
                //else
                //{

                //    //LocalDBManager.Instance.ResetReco();
                //}
            }
            catch (Exception ex)
            {

            }
        };
        //Set style

        //NormalSetsSwitch.Toggled += (sender, e) =>
        //{
        //    if (NormalSetsSwitch.IsToggled)
        //    {
        //        RestPauseSetsSwitch.IsToggled = false;
        //        SaveCustomSettings();
        //    }
        //};

        //RestPauseSetsSwitch.Toggled += (sender, e) =>
        //{
        //    if (RestPauseSetsSwitch.IsToggled)
        //    {
        //        NormalSetsSwitch.IsToggled = false;
        //        SaveCustomSettings();
        //    }
        //};

        UnitEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
        MinEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;
        MaxEntry.Keyboard = Device.RuntimePlatform.Equals(Device.Android) ? Keyboard.Telephone : Keyboard.Numeric;

        DurationToDays = new Dictionary<string, int>();
        DurationToDays.Add("Abs", 7);
        DurationToDays.Add("Back", 4);
        DurationToDays.Add("Biceps", 5);
        DurationToDays.Add("Calves", 9);
        DurationToDays.Add("Chest", 3);
        //DurationToDays.Add("Flexibility & Mobility", 13);
        DurationToDays.Add("Forearm", 11);
        DurationToDays.Add("Legs", 8);
        DurationToDays.Add("Lower back, glutes & hamstrings", 14);
        DurationToDays.Add("Neck", 10);
        DurationToDays.Add("Shoulders", 2);
        DurationToDays.Add("Triceps", 6);
        DurationToDays.Add("Cardio", 12);
        DurationToDays.Add("Undefined", 1);

        EquipmentsToIds = new Dictionary<string, int>();
        EquipmentsToIds.Add("Undefined", 0);
        EquipmentsToIds.Add("Chin-up bar", 1);
        EquipmentsToIds.Add("Pulley", 2);
        EquipmentsToIds.Add("Plate", 3);
        EquipmentsToIds.Add("Dumbbell", 4);

        ExerciseLevels.Add("Hard", 1);
        ExerciseLevels.Add("Medium", 2);
        ExerciseLevels.Add("Easy", 2);

        BodyPartPicker.ItemsSource = DurationToDays.Select(x => x.Key).ToList();
        BodyPartPicker.Unfocused += BodyPartPicker_Unfocused;
        BodyPartPicker.SelectedIndexChanged += BodyPartPicker_SelectedIndexChanged;

        //BackOffPicker.ItemsSource = new List<string>() { "Default", "Yes", "No" };
        //BackOffPicker.Unfocused += BackOffPicker_Unfocused;

        EquipmentPicker.ItemsSource = EquipmentsToIds.Select(x => x.Key).ToList();
        EquipmentPicker.Unfocused += EquipmentPicker_Unfocused;

        ExerciseLevelPicker.ItemsSource = ExerciseLevels.Select(x => x.Key).ToList();
        ExerciseLevelPicker.Unfocused += ExerciseLevelPicker_Unfocused;

        //SetStylePicker.ItemsSource = new List<string>() { AppResources.RestPauseSets, AppResources.NormalSets, "Pyramid" };
        //SetStylePicker.Unfocused += SetStylePicker_Unfocused;
        OnBeforeShow();
    }

    public async void OnBeforeShow()
    {

        try
        {

            if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight)
            {
                List<string> setstyles = new List<string>();
                setstyles.Add("Rest-pause");
                setstyles.Add("Normal");
                SetStylePicker.ItemsSource = setstyles;
                //PyramidGradient.IsVisible = false;
                //BxSaperator2.IsVisible = false;
                MainIncrementStack.IsVisible = false;

                //RPyramidGradient.IsVisible = false;
                //BxSaperator3.IsVisible = false;

            }
            else
            {
                List<string> setstyles = new List<string>();
                setstyles.Add("Rest-pause");
                setstyles.Add("Drop");
                setstyles.Add("Normal");
                setstyles.Add("Pyramid");
                setstyles.Add("Reverse Pyramid");

                SetStylePicker.ItemsSource = setstyles;

                //PyramidGradient.IsVisible = true;
                //BxSaperator2.IsVisible = true;
                MainIncrementStack.IsVisible = true;

                //RPyramidGradient.IsVisible = true;
                //BxSaperator3.IsVisible = true;

            }
            SetStylePicker.SelectedIndex = 0;
            Title = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label;
            IsLoading = true;
            ExerciseSettingsModel exerciseSettingsModel = await DrMuscleRestClient.Instance.GetExerciseSettings(new ExerciseSettingsModel()
            {
                Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id,
            });



            if (exerciseSettingsModel != null)
            {
                NotesEnrty.Text = exerciseSettingsModel.Notes;
                IncrementSwitch.IsToggled = exerciseSettingsModel.IsCustomIncrements;
                if (exerciseSettingsModel.Increments != null)
                {
                    UnitEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Increments.Kg}" : $"{exerciseSettingsModel.Increments.Lb}";
                }
                else
                    UnitEntry.Text = "";
                if (exerciseSettingsModel.Min != null)
                {
                    MinEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Min.Kg}" : $"{exerciseSettingsModel.Min.Lb}";
                }
                else
                    MinEntry.Text = "";
                if (exerciseSettingsModel.Max != null)
                {
                    MaxEntry.Text = LocalDBManager.Instance.GetDBSetting("massunit").Value == "kg" ? $"{exerciseSettingsModel.Max.Kg}" : $"{exerciseSettingsModel.Max.Lb}";
                }
                else
                    MaxEntry.Text = "";
                CustomRepsSwitch.IsToggled = exerciseSettingsModel.IsCustomReps.Value;
                CustomSetsSwitch.IsToggled = exerciseSettingsModel.IsCustomSets.Value;
                if (exerciseSettingsModel.IsCustomReps.Value)
                {
                    RepsMinimumLabel.Text = exerciseSettingsModel.RepsMinValue.ToString();
                    RepsMaximumLabel.Text = exerciseSettingsModel.RepsMaxValue.ToString();
                }
                WarmupSwitch.IsToggled = exerciseSettingsModel.IsCustomWarmups;
                if (exerciseSettingsModel.IsCustomWarmups)
                {
                    WarmupEntry.Text = Convert.ToString(exerciseSettingsModel.WarmupsValue);
                }
                else
                    WarmupEntry.Text = "";
                CustomSetsSwitch.IsToggled = exerciseSettingsModel.IsCustomSets.Value;
                //if (exerciseSettingsModel.IsCustomSets)
                //{
                //    if (exerciseSettingsModel.IsPyramid)
                //        RPyramid();
                //    else if (exerciseSettingsModel.IsNormalSets == null && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                //    {
                //        Pyramid();
                //    }
                //    else if ((bool)exerciseSettingsModel.IsNormalSets == true)
                //    {
                //        NormalStyle();
                //    }
                //    else
                //    {
                //        RestPasue();
                //    }
                //}
                //else
                //    RestPasue();
                if (exerciseSettingsModel.IsCustomSets.Value)
                {
                    if (exerciseSettingsModel.IsDropSet && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        DropStyle();
                        SetStylePicker.SelectedIndex = 4;
                    }
                    else if (exerciseSettingsModel.IsPyramid && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        RPyramid();
                        SetStylePicker.SelectedIndex = 2;
                    }
                    else if (exerciseSettingsModel.IsNormalSets == null && CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsBodyweight == false)
                    {
                        Pyramid();
                        SetStylePicker.SelectedIndex = 3;
                    }
                    else if ((bool)exerciseSettingsModel.IsNormalSets == true)
                    {
                        NormalStyle();
                        SetStylePicker.SelectedIndex = 1;
                    }
                    else
                    {
                        RestPasue();
                        SetStylePicker.SelectedIndex = 0;
                    }
                }
                else
                {
                    RestPasue();
                    SetStylePicker.SelectedIndex = 0;
                }
                SelectedIndexForSet = SetStylePicker.SelectedIndex;
                if (exerciseSettingsModel.IsBackOffSet == null)
                    DefaultClick();
                else if ((bool)exerciseSettingsModel.IsBackOffSet == true)
                    YesClicked();
                else
                    NoClicked();
                if (exerciseSettingsModel.BodyPartId != null)
                {
                    var item = DurationToDays.Where(x => x.Value == (int)exerciseSettingsModel.BodyPartId).FirstOrDefault();
                    BodyPartPicker.SelectedItem = item.Key;
                    SelectedBodyPart = item.Key;
                }
                if (exerciseSettingsModel.EquipmentId != null)
                {
                    var item = EquipmentsToIds.Where(x => x.Value == (int)exerciseSettingsModel.EquipmentId).FirstOrDefault();
                    EquipmentPicker.SelectedItem = item.Key;
                    SelectedEquipment = item.Key;
                }
                else
                {
                    EquipmentPicker.SelectedIndex = 0;
                    SelectedEquipment = "Undefined";
                }
                if (exerciseSettingsModel.IsMedium)
                {
                    ExerciseLevelPicker.SelectedIndex = 1;
                }
                else if (exerciseSettingsModel.IsEasy)
                {
                    ExerciseLevelPicker.SelectedIndex = 2;
                }
                else
                    ExerciseLevelPicker.SelectedIndex = 0;

                SelectedIndexExerciseLevel = ExerciseLevelPicker.SelectedIndex;

                SetEntry.Text = Convert.ToString(exerciseSettingsModel.SetCount);
                UnilateralSwitch.IsToggled = exerciseSettingsModel.IsUnilateral;
                TimebaseSwitch.IsToggled = exerciseSettingsModel.IsTimeBased;
                FavoriteSwitch.IsToggled = exerciseSettingsModel.IsFavorite;
            }
            else
            {
                UnitEntry.Text = "";
                NotesEnrty.Text = "";
                MinEntry.Text = "";
                MaxEntry.Text = "";
                WarmupEntry.Text = "";
                SetEntry.Text = "";
                WarmupSwitch.IsToggled = false;
                CustomRepsSwitch.IsToggled = false;
                CustomSetsSwitch.IsToggled = false;
                FavoriteSwitch.IsToggled = false;
                IncrementSwitch.IsToggled = false;
                UnilateralSwitch.IsToggled = CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsUnilateral;
                TimebaseSwitch.IsToggled = CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsTimeBased;
                RestPasue();
                DefaultClick();
                if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsMedium)
                {
                    ExerciseLevelPicker.SelectedIndex = 1;
                }
                else if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsEasy)
                {
                    ExerciseLevelPicker.SelectedIndex = 2;
                }
                else
                    ExerciseLevelPicker.SelectedIndex = 0;

                SelectedIndexExerciseLevel = ExerciseLevelPicker.SelectedIndex;

                if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.BodyPartId != null)
                {
                    var item = DurationToDays.Where(x => x.Value == CurrentLog.Instance.WorkoutTemplateCurrentExercise.BodyPartId).FirstOrDefault();
                    BodyPartPicker.SelectedItem = item.Key;
                    SelectedBodyPart = item.Key;
                    //BodyPartPicker.SelectedIndex = (int)(CurrentLog.Instance.WorkoutTemplateCurrentExercise.BodyPartId - 1);
                }
                if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.EquipmentId != null)
                {
                    var item = EquipmentsToIds.Where(x => x.Value == CurrentLog.Instance.WorkoutTemplateCurrentExercise.EquipmentId).FirstOrDefault();
                    EquipmentPicker.SelectedItem = item.Key;
                    SelectedEquipment = item.Key;
                    //BodyPartPicker.SelectedIndex = (int)(CurrentLog.Instance.WorkoutTemplateCurrentExercise.BodyPartId - 1);
                }
                else
                {
                    EquipmentPicker.SelectedIndex = 0;
                    SelectedEquipment = "Undefined";
                }
                LoadCustomReps();
            }
            if (CurrentLog.Instance.AutoEnableIncrements)
            {
                CurrentLog.Instance.AutoEnableIncrements = false;
                await scrollView.ScrollToAsync(MainIncrementStack, ScrollToPosition.Start, false);
                IncrementSwitch.IsToggled = true;
            }
            IsLoading = false;

        }
        catch (Exception ex)
        {
            IsLoading = false;
        }
    }

    protected override void OnDisappearing()
    {
        DismissKeyboard();
    }

    protected override void OnAppearing()
    {
        DismissKeyboard();
        DependencyService.Get<IFirebase>().SetScreenName("custom_exercise_settings_page");

    }


    async void BackOffPicker_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }

    

    void BodyPartPicker_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (BodyPartPicker.IsFocused)
        {
            //if (BodyPartPicker.SelectedIndex == 0)
            //    BodyPartPicker.SelectedIndex = 1;
        }
    }

    async void BodyPartPicker_Unfocused(object sender, FocusEventArgs e)
    {
        if (SelectedBodyPart != BodyPartPicker.SelectedItem.ToString())
        {
            CurrentLog.Instance.IsBodyPartUpdated = true;
            SaveCustomSettings();
        }
    }
    async void EquipmentPicker_Unfocused(object sender, FocusEventArgs e)
    {
        if (SelectedEquipment != EquipmentPicker.SelectedItem.ToString())
        {
            SaveCustomSettings();
        }
    }

    async void ExerciseLevelPicker_Unfocused(object sender, FocusEventArgs e)
    {
        //CurrentLog.Instance.IsBodyPartUpdated = true;
        if (SelectedIndexExerciseLevel != ExerciseLevelPicker.SelectedIndex)
        {
            SaveCustomSettings();
        }
    }

    private void RefreshLocalized()
    {

        LblNotes.Text = AppResources.Notes;
        LblBodyParts.Text = "Body part";
        LblEquipment.Text = "Equipment";
        LblSettings.Text = AppResources.SettingsUppercase;

        LblCustomReps.Text = AppResources.UseCustomReps;
        LblCustomSet.Text = AppResources.UseCustomSetStyle;

        LblCustomIncrements.Text = AppResources.UseCustomIncrements;

        LblMin.Text = AppResources.Min;
        LblMax.Text = AppResources.Max;
        SaveCustomRepsButton.Text = AppResources.SaveCustomReps;

        Min.Text = AppResources.MinWeight;
        Max.Text = AppResources.MaxWeight;
        LblRestPauseSets.Text = AppResources.RestPauseSetsAreHarderButTheyHalveWorkoutTime;
        //LblNormalSets.Text = AppResources.NormalSets;
        //LblRestPasue.Text = AppResources.RestPauseSets;

        Increments.Text = AppResources.Increments;
        SaveIncrementsButton.Text = AppResources.SaveIncrements;

        DeleteButton.Text = AppResources.DeleteExercise;
        LblMore.Text = AppResources.MoreUppercase;
        ResetButton.Text = "Delete all history";
        SaveWarmupButton.Text = "Save warm-up sets";
        LblHowManyWarmups.Text = AppResources.WarmUpSets;
        WarmupEntry.Placeholder = AppResources.TapToSet;
        LblCustomWarmUp.Text = AppResources.UseCustomWarmUps;
    }
    public async void BtnRestPauseClicked(object sender, EventArgs args)
    {
        RestPasue();
        SaveCustomSettings();
    }
    void RestPasue()
    {
        style = 0;
        LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
        
    }

    public async void BtnNormalClicked(object sender, EventArgs args)
    {
        NormalStyle();

        SaveCustomSettings();
    }

    void NormalStyle()
    {
        style = 1;
        LblRestPauseSets.Text = "Takes more time, but builds more strength";
        

    }

    void DropStyle()
    {
        style = 4;
        LblRestPauseSets.Text = "Weights decrease from set to set—great for dumbbell, machine, and pulley exercises";
    }

    void BtnPyramid_Clicked(System.Object sender, System.EventArgs e)
    {
        Pyramid();
        SaveCustomSettings();
    }
    void Pyramid()
    {
        LblRestPauseSets.Text = "Reps increase from set to set";
        style = 2;
        
    }

    void BtnRPyramid_Clicked(System.Object sender, System.EventArgs e)
    {
        RPyramid();
        SaveCustomSettings();
    }
    void RPyramid()
    {
        LblRestPauseSets.Text = "Reps decrease from set to set";
        style = 3;
       
    }

    void SetStylePicker_Unfocused(System.Object sender, FocusEventArgs e)
    {
        if (SelectedIndexForSet != SetStylePicker.SelectedIndex)
        {
            switch (SetStylePicker.SelectedIndex)
            {
                case 0:
                    LblRestPauseSets.Text = "Harder, but makes your workouts ~59% faster";
                    style = 0;
                    break;
                case 1:
                    //Drop
                    LblRestPauseSets.Text = "Weights decrease from set to set—great for dumbbell, machine, and pulley exercises";
                    style = 4;
                    break;
                case 2:
                    LblRestPauseSets.Text = "Takes more time, but builds more strength";
                    style = 1;
                    break;
                case 3:
                    //Pyramid
                    LblRestPauseSets.Text = "Reps decrease from set to set";
                    style = 3;
                    break;
                case 4:
                    //Reverse pyramid
                    LblRestPauseSets.Text = "Reps increase from set to set";
                    style = 2;
                    break;

                default:
                    break;
            }
            SelectedIndexForSet = style ?? 0;
            SaveCustomSettings();
        }
    }

    
    void DismissKeyboard()
    {
        try
        {

            UnitEntry.Unfocus();
            MinEntry.Unfocus();
            MaxEntry.Unfocus();
            WarmupEntry.Unfocus();
            if (NotesEnrty.IsFocused)
                NotesEnrty.Unfocus();

        }
        catch (Exception ex)
        {

        }
    }
    void NotesEntry_Unfocused(object sender, FocusEventArgs e)
    {
        SaveCustomSettings();
    }

    async void DeleteButton_Clicked(object sender, EventArgs e)
    {
        ExerciceModel m = CurrentLog.Instance.WorkoutTemplateCurrentExercise;
      var ShowPopUp = await HelperClass.DisplayCustomPopup(AppResources.DeleteExercise,string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
        AppResources.Delete,AppResources.Cancel);
            ShowPopUp.ActionSelected += async (sender,action) => {

                    if (action == Views.PopupAction.OK)
                    {
                        DeleteExercisesAction(m);
                    }
                    
            };
            

        // ConfirmConfig p = new ConfirmConfig()
        // {
        //     Title = AppResources.DeleteExercise,
        //     Message = string.Format("{0} \"{1}\"?", AppResources.PermanentlyDelete, m.Label),
        //     OkText = AppResources.Delete,
        //     CancelText = AppResources.Cancel,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        // };
        // p.OnAction = (obj) =>
        // {
        //     if (obj)
        //     {
        //         DeleteExercisesAction(m);
        //     }
        // };
        // UserDialogs.Instance.Confirm(p);
    }

    public async void DeleteExercisesAction(ExerciceModel model)
    {
        try
        {
            ExerciceModel m = model;
            SwapExerciseContext sec = ((App)Application.Current).SwapExericesContexts.Swaps.First(s => s.WorkoutId == CurrentLog.Instance.CurrentWorkoutTemplate.Id && s.TargetExerciseId == m.Id);
            if (((App)Application.Current).SwapExericesContexts.Swaps.Contains(sec))
            {
                ((App)Application.Current).SwapExericesContexts.Swaps.Remove(sec);
                ((App)Application.Current).SwapExericesContexts.SaveContexts();
            }
        }
        catch (Exception ex)
        {

        }




        BooleanModel result = await DrMuscleRestClient.Instance.DeleteExercise(model);
        if (result.Result)
        {
            try
            {
                if (CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsSwapTarget)
                {
                    CurrentLog.Instance.IsAddingExerciseLocally = true;
                    CurrentLog.Instance.IsAddedExercises = true;
                    if (Navigation?.NavigationStack?.Count > 1)
                    {
                        try
                        {
                            await Navigation.PopAsync();
                        }
                        catch (NotSupportedException ex)
                        {
                        }
                    }
                    return;
                }
            }
            catch (Exception ex)
            {

            }
            MessagingCenter.Send<ExerciseDeleteMessage>(new ExerciseDeleteMessage(), "ExerciseDeleteMessage");

            try
            {
                CurrentLog.Instance.IsFavouriteUpdated = true;
                CurrentLog.Instance.IsExerciseDeleted = true;
                if ((ContentPage)(Navigation.NavigationStack[Navigation.NavigationStack.Count - 2]) is KenkoSingleExercisePage)
                {
                    if ((ContentPage)(Navigation.NavigationStack[Navigation.NavigationStack.Count - 3]) is AllExercisePage)
                    {

                        //if (Device.RuntimePlatform == Device.Android)
                        //{
                        //    await PagesFactory.PopToPageWithoutBefore<AllExercisePage>();
                        //    var page = PagesFactory.GetPage<AllExercisePage>();
                        //    await Task.Delay(300);
                        //    page.OnBeforeShow();
                        //}
                        //else
                        Utility.HelperClass.PopToPage<AllExercisePage>(this.Navigation);
                            
                    }
                    else
                    {
                        if (Navigation?.NavigationStack?.Count > 1)
                        {
                            try
                            {
                                await Navigation.PopAsync();
                            }
                            catch (NotSupportedException ex)
                            {
                            }
                        }
                    }
                }
                else if ((ContentPage)(Navigation.NavigationStack[Navigation.NavigationStack.Count - 2]) is KenkoChooseYourWorkoutExercisePage)
                {
                    if (Navigation?.NavigationStack?.Count > 1)
                    {
                        try
                        {
                            await Navigation.PopAsync();
                            //await Navigation.PopAsync();
                        }
                        catch (NotSupportedException ex)
                        {
                        }
                    }
                }
                else if (CurrentLog.Instance.ExerciseSettingsPage != null && CurrentLog.Instance.ExerciseSettingsPage.FullName.Contains("AllExercisePage"))
                {
                    Utility.HelperClass.PopToPage<AllExercisePage>(this.Navigation);

                }
                //TODO: MAUI
                //else
                //    Utility.HelperClass.PopToPage<ChooseYourCustomExercisePage>(this.Navigation);
            }
            catch (Exception ex)
            {
                if (Navigation?.NavigationStack?.Count > 1)
                {
                    await Navigation.PopAsync();
                }
            }

        }
    }

    async void ResetButton_Clicked(object sender, EventArgs e)
    {
        var mi = ((Button)sender);

        ExerciceModel m = (ExerciceModel)mi.CommandParameter;

        var ShowPopUp = await HelperClass.DisplayCustomPopup("Delete history",$"Delete ALL {CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label} history for ALL workouts? This cannot be undone.",
        "Delete all",AppResources.Cancel);
            ShowPopUp.ActionSelected += async (sender,action) => {

                    if (action == Views.PopupAction.OK)
                    {
                        ResetExercisesAction(m);
                    }
                    
            };
           

        // ConfirmConfig p = new ConfirmConfig()
        // {
        //     Title = "Delete history",//AppResources.ResetExercise,
        //     Message = $"Delete ALL {CurrentLog.Instance.WorkoutTemplateCurrentExercise.Label} history for ALL workouts? This cannot be undone.",//AppResources.AreYouSureYouWantToResetThisExerciseAndDeleteAllItsHistoryThisCannotBeUndone,// string.Format("Are you sure you want to reset this exercise and delete all its history? This cannot be undone.", m.Label),
        //     OkText = "Delete all",
        //     CancelText = AppResources.Cancel,
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        // };
        // p.OnAction = (obj) =>
        // {
        //     if (obj)
        //     {
        //         ResetExercisesAction(m);
        //     }
        // };
        // UserDialogs.Instance.Confirm(p);
    }
    public async void ResetExercisesAction(ExerciceModel model)
    {
        BooleanModel result = await DrMuscleRestClient.Instance.ResetExercise(new ExerciceModel()
        {
            Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id
        });
        ResetReco();
    }
    void ResetReco()
    {
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "Normal", DateTime.Now.AddDays(-1).ToString());
        LocalDBManager.Instance.SetDBReco("NbRepsGeneratedTime" + CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id + "RestPause", DateTime.Now.AddDays(-1).ToString());
    }

    public void LoadCustomReps()
    {
        try
        {
            RepsMinimumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsminimum").Value;
            RepsMaximumLabel.Text = LocalDBManager.Instance.GetDBSetting("repsmaximum").Value;
        }
        catch (Exception)
        {
            ConnectionErrorPopup();

        }

    }
    void UnitEntry_TextChanged(object sender, TextChangedEventArgs e)
    {
        const string textRegex = @"^\d+(?:[\.,]\d{0,2})?$";
        var text = e.NewTextValue.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        {
            ((Entry)sender).Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        }
    }

    private async void SaveCustomSettings()
    {
        if (IsLoading)
            return;

        var exerciseSettings = new ExerciseSettingsModel();
        exerciseSettings.Id = CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id;
        exerciseSettings.Notes = NotesEnrty.Text;
        exerciseSettings.IsCustomIncrements = IncrementSwitch.IsToggled;
        ResetReco();
        if (WarmupSwitch.IsToggled)
        {
            if (!string.IsNullOrEmpty(WarmupEntry.Text))
            {
                var text = WarmupEntry.Text.Replace(",", ".");
                var result = Convert.ToInt32(text, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(Convert.ToString(result)))
                {

                }
                else
                {
                    exerciseSettings.IsCustomWarmups = true;
                    exerciseSettings.WarmupsValue = result;
                }
            }
        }
        if (IncrementSwitch.IsToggled)
        {
            if (!string.IsNullOrEmpty(UnitEntry.Text))
            {
                var text = UnitEntry.Text.Replace(",", ".");
                var result = Convert.ToDecimal(text, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(result.ToString()) || result.ToString().Equals("0"))
                {

                }
                else
                {
                    var IncrementsUnit = new MultiUnityWeight((decimal)result, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                    exerciseSettings.Increments = IncrementsUnit;
                }
            }
            if (!string.IsNullOrEmpty(MinEntry.Text))
            {
                var minValue = MinEntry.Text.Replace(",", ".");
                var minResult = Convert.ToDecimal(minValue, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(minResult.ToString()))
                {

                }
                else
                {
                    var MinVal = new MultiUnityWeight((decimal)minResult, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                    exerciseSettings.Min = MinVal;
                }
            }
            if (!string.IsNullOrEmpty(MaxEntry.Text))
            {
                var maxValue = MaxEntry.Text.Replace(",", ".");
                var maxResult = Convert.ToDecimal(maxValue, System.Globalization.CultureInfo.InvariantCulture);
                if (string.IsNullOrEmpty(maxResult.ToString()) || maxResult.ToString().Equals("0"))
                {

                }
                else
                {
                    var MaxVal = new MultiUnityWeight((decimal)maxResult, LocalDBManager.Instance.GetDBSetting("massunit").Value);
                    exerciseSettings.Max = MaxVal;
                }
            }
            if (exerciseSettings.Max != null && exerciseSettings.Min != null)
            {
                if (exerciseSettings.Min.Kg > exerciseSettings.Max.Kg)
                {
                    // await UserDialogs.Instance.AlertAsync(new AlertConfig()
                    // {
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     Message = AppResources.MinValueShouldNotGreaterThenMax,
                    //     Title = AppResources.Error
                    // });
                    await HelperClass.DisplayCustomPopupForResult(AppResources.Error,
                            AppResources.MinValueShouldNotGreaterThenMax,"OK","");
                    return;
                }
            }
        }
        if (BodyPartPicker.SelectedIndex != -1)
        {
            var item = DurationToDays.Where(x => x.Key.Equals(BodyPartPicker.SelectedItem)).FirstOrDefault();
            SelectedBodyPart = BodyPartPicker.SelectedItem.ToString();
            exerciseSettings.BodyPartId = item.Value;
            try
            {
                if (CurrentLog.Instance.CurrentExercise != null && CurrentLog.Instance.WorkoutTemplateCurrentExercise.Id == CurrentLog.Instance.CurrentExercise.Id)
                {
                    CurrentLog.Instance.CurrentExercise.BodyPartId = item.Value;
                }
            }
            catch (Exception ex)
            {

            }
            CurrentLog.Instance.WorkoutTemplateCurrentExercise.BodyPartId = item.Value;
        }
        exerciseSettings.IsCustomReps = CustomRepsSwitch.IsToggled;
        exerciseSettings.IsCustomSets = CustomSetsSwitch.IsToggled;
        if (CustomRepsSwitch.IsToggled)
        {
            int RepsMinimum = Convert.ToInt32(RepsMinimumLabel.Text);
            int RepsMaximum = Convert.ToInt32(RepsMaximumLabel.Text);
            exerciseSettings.RepsMinValue = RepsMinimum;
            exerciseSettings.RepsMaxValue = RepsMaximum;
            RepsStack.IsVisible = true;
        }
        if (CustomSetsSwitch.IsToggled)
        {
            if (style == 4)
            {
                exerciseSettings.IsDropSet = true;
                exerciseSettings.IsNormalSets = null;
            }
            else if (style == 0)
            {
                exerciseSettings.IsNormalSets = false;
            }
            else if (style == 1)
            {
                exerciseSettings.IsNormalSets = true;
            }
            else if (style == 3)
            {
                exerciseSettings.IsNormalSets = false;
                exerciseSettings.IsPyramid = true;
            }
            else
            {
                exerciseSettings.IsNormalSets = null;
            }
        }
        if (BackOff == 0)
            exerciseSettings.IsBackOffSet = null;
        else if (BackOff == 1)
            exerciseSettings.IsBackOffSet = true;
        else
            exerciseSettings.IsBackOffSet = false;
        if (EquipmentPicker.SelectedIndex == -1 || EquipmentPicker.SelectedIndex == 0)
        {
            exerciseSettings.EquipmentId = null;
            SelectedEquipment = "Undefined";
        }
        else
        {
            var item = EquipmentsToIds.Where(x => x.Key.Equals(EquipmentPicker.SelectedItem)).FirstOrDefault();
            SelectedEquipment = EquipmentPicker.SelectedItem.ToString();
            exerciseSettings.EquipmentId = item.Value;
            CurrentLog.Instance.WorkoutTemplateCurrentExercise.IsPlate = item.Value == 3;
        }

        if (ExerciseLevelPicker.SelectedIndex == 1)
        {
            SelectedIndexExerciseLevel = ExerciseLevelPicker.SelectedIndex;
            exerciseSettings.IsEasy = false;
            exerciseSettings.IsMedium = true;
        }
        else if (ExerciseLevelPicker.SelectedIndex == 2)
        {
            SelectedIndexExerciseLevel = ExerciseLevelPicker.SelectedIndex;
            exerciseSettings.IsEasy = true;
            exerciseSettings.IsMedium = false;
        }
        else
        {
            SelectedIndexExerciseLevel = ExerciseLevelPicker.SelectedIndex;
            exerciseSettings.IsEasy = false;
            exerciseSettings.IsMedium = false;
        }


        if (!string.IsNullOrEmpty(SetEntry.Text))
        {
            var count = int.Parse(SetEntry.Text.ReplaceWithDot());
            if (count >= 2 || count <= 99)
            {
                exerciseSettings.SetCount = count;
            }
        }
        else
        {
            exerciseSettings.SetCount = null;
        }
        exerciseSettings.IsUnilateral = UnilateralSwitch.IsToggled;
        exerciseSettings.IsTimeBased = TimebaseSwitch.IsToggled;
        exerciseSettings.IsFavorite = FavoriteSwitch.IsToggled;
        await DrMuscleRestClient.Instance.AddUpdateExerciseSettingsV2(exerciseSettings);
    }

    void BtnDefault_Clicked(System.Object sender, System.EventArgs e)
    {
        DefaultClick();
        SaveCustomSettings();

    }

    void BtnYes_Clicked(System.Object sender, System.EventArgs e)
    {
        YesClicked();
        SaveCustomSettings();
    }

    void BtnNo_Clicked(System.Object sender, System.EventArgs e)
    {
        NoClicked();
        SaveCustomSettings();
    }

    public Color GetTransparentGradient()
    {
        return Colors.Transparent;
    }

    public Color GetBlueGradient()
    {
        return Constants.AppThemeConstants.BlueColor;
    }

    void DefaultClick()
    {
        BackOff = 0;
        BtnYes.BackgroundColor = Colors.Transparent;
        YesGradient.BackgroundColor = GetTransparentGradient();
        DefaultGradient.BackgroundColor = GetBlueGradient();
        BtnYes.TextColor = Color.FromHex("#0C2432");
        BtnDefault.TextColor = Colors.White;

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = GetTransparentGradient();
        BtnNo.BackgroundColor = Colors.Transparent;
    }


    void YesClicked()
    {
        BackOff = 1;
        BtnYes.BackgroundColor = Colors.Transparent;
        //BtnKg.BackgroundColor = Color.FromHex("#5CD196");
        BtnYes.TextColor = Colors.White;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = GetBlueGradient();
        DefaultGradient.BackgroundColor = GetTransparentGradient();

        BtnNo.TextColor = Color.FromHex("#0C2432");
        NoGradient.BackgroundColor = GetTransparentGradient();

    }


    void NoClicked()
    {
        BackOff = 2;
        BtnDefault.BackgroundColor = Colors.Transparent;
        BtnDefault.TextColor = Color.FromHex("#0C2432");
        BtnYes.TextColor = Color.FromHex("#0C2432");
        YesGradient.BackgroundColor = GetTransparentGradient();
        DefaultGradient.BackgroundColor = GetTransparentGradient();

        BtnNo.TextColor = Colors.White; ;
        NoGradient.BackgroundColor = GetBlueGradient();
    }

    void SetEntry_TextChanged(System.Object sender, TextChangedEventArgs e)
    {
        const string textRegex = @"^\d+(?:\d{0,2})?$";
        var text = e.NewTextValue.Replace(",", ".");
        bool IsValid = Regex.IsMatch(text, textRegex, RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
        if (IsValid == false && !string.IsNullOrEmpty(e.NewTextValue))
        {
            ((Entry)sender).Text = e.NewTextValue.Substring(0, e.NewTextValue.Length - 1);
        }
    }
    async Task ConnectionErrorPopup()
    {

        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });
        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                            AppResources.PleaseCheckInternetConnection,"Try again","");

    }
}

