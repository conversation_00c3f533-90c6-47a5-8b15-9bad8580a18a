﻿using DrMaxMuscle.Controls;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UIKit;

namespace DrMaxMuscle.Platforms.iOS.Renderer
{
    public class DropDownPickerViewRenderer : PickerHandler
    {
        public DropDownPickerViewRenderer()
        {

            Mapper.AppendToMapping(nameof(DropDownPicker), (handler, view) =>
            {
                if (view is DropDownPicker && ((DropDownPicker)view).Image != null)
                {
                    var downarrow = UIImage.FromBundle(((DropDownPicker)view).Image);
                    if(handler.PlatformView != null)
                    {
                        handler.PlatformView.RightViewMode = UITextFieldViewMode.Always;
                        handler.PlatformView.RightView = new UIImageView(downarrow);
                    }
                }
            });
        }
    }
}
