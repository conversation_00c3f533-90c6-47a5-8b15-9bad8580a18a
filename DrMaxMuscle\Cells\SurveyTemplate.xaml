<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
xmlns:app="clr-namespace:DrMaxMuscle.Constants"
xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             x:Class="DrMaxMuscle.Cells.SurveyTemplate">
    <controls:CustomFrame
    x:Name="SurveyCardFrame"
    Margin="10,11,10,10"
    Padding="0,10,10,10"
        
    CornerRadius="12"
        BorderColor="Transparent"
HasShadow="True">
<controls:CustomFrame.Shadow>
    <Shadow Brush="Black"
              Offset="0,0"
              Radius="5"
              Opacity="0.2" />
</controls:CustomFrame.Shadow>
        <controls:CustomFrame.Triggers>
            <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="True" TargetType="Frame">
                <Setter Property="Margin" Value="10,11,10,0" />
            </DataTrigger>
            <DataTrigger Binding="{Binding IsNewRecordAvailable}" Value="False" TargetType="Frame">
                <Setter Property="Margin" Value="10,1,10,10" />
            </DataTrigger>
        </controls:CustomFrame.Triggers>
        <Grid                
        Padding="10,15,10,15"
        Margin="0,0,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <!--Icon for survey-->
            <Image
            Source="{Binding StrengthImage}"
            Margin="{OnPlatform Android='0,-6,0,0', iOS='0,-8,0,0'}"
            Grid.Row="0"
            Grid.Column="0"
            WidthRequest="27"
            VerticalOptions="Start"
            HeightRequest="27" />

            <!--Label for message-->
            <Label                    
            Grid.Row="0"
            Grid.Column="1"
            Text="{Binding Question}"
            Margin="0,-8,0,9"
            TextColor="Black"
            FontAttributes="Bold"
            FontSize="19" />

            <!--grid to show emojis for review-->
            <Grid
            x:Name="EmojiGrid"
            Grid.Row="1"
            Grid.Column="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Frame
                    BorderColor="Transparent"
                x:Name="PancakeSadReview"
                Grid.Column="0"
                CornerRadius="26"
                    Padding="0"
                VerticalOptions="Center"
                HorizontalOptions="Start"
                BackgroundColor="Transparent">
                    <Frame.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding SelectedSurveyOption}" Value="Sad">
                            <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                        </DataTrigger>
                    </Frame.Triggers>
                    <ImageButton
                    x:Name="SadReviewButton"
                    Source="ic_sad.png"
                    WidthRequest="48"
                    HeightRequest="48"
                    BackgroundColor="Transparent"
                    Clicked="SadReviewButton_Clicked">
                    </ImageButton>
                </Frame>
                <Frame
                    BorderColor="Transparent"
                x:Name="PancakeNeutralReview"
                Grid.Column="1"
                    Padding="0"
                CornerRadius="26"
                VerticalOptions="Center"
                HorizontalOptions="Start"
                BackgroundColor="Transparent">
                    <Frame.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding SelectedSurveyOption}" Value="Neutral">
                            <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                        </DataTrigger>
                    </Frame.Triggers>
                    <ImageButton
                    x:Name="NeutralReviewButton"
                    Source="ic_neutral.png"
                    WidthRequest="48"
                    HeightRequest="48"
                    BackgroundColor="Transparent"
                    Clicked="NeutralReviewButton_Clicked"/>
                </Frame>
                <Frame
                    BorderColor="Transparent"
                x:Name="PancakeHappyReview"
                Grid.Column="2"
                CornerRadius="26"
                    Padding="0"
                VerticalOptions="Center"
                HorizontalOptions="Start"
                BackgroundColor="Transparent">
                    <Frame.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                            <Setter Property="BackgroundColor" Value="{x:Static app:AppThemeConstants.ReysBlueColor}" />
                        </DataTrigger>
                    </Frame.Triggers>
                    
                    <ImageButton
                    x:Name="HappyReviewButton"
                    Source="ic_happy.png"
                    WidthRequest="48"
                    HeightRequest="48"
                    BackgroundColor="Transparent"
                    Clicked="HappyReviewButton_Clicked">
                        
                    </ImageButton>
                </Frame>
            </Grid>

            <!--Stacks to show as per button clicked in grid-->
            <!--Stack for response (row 2)-->
            <StackLayout
            x:Name="ResponseStack"
            Grid.Row="2"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            IsVisible="{Binding SelectedSurveyOption, Converter={StaticResource SurveySelectionEnumToVisibilityConverter}}"
            Margin="1,20,0,0">
                <!--Label for response message-->
                <!-- We're always trying to improve. Please email us feedback. We reply in 1 day. -->
                <!-- Tell us what you thought about Dr. Muscle  -->
                <Label
                x:Name="LabelMessage"
                Margin="45,0,0,0"
                TextColor="#AA000000"
                Text="So sorry. Email feedback? 1-day reply."
                Style="{StaticResource LabelStyle}"
                VerticalOptions="Center"
                VerticalTextAlignment="Center"
                HorizontalOptions="Start"
                HorizontalTextAlignment="Start"
                IsVisible="true">
                    <Label.Triggers>
                        <DataTrigger TargetType="Label" Binding="{Binding SelectedSurveyOption}" Value="Neutral">
                            <Setter Property="Text" Value="Oops! Email feedback? 1-day reply." />
                        </DataTrigger>
                        <DataTrigger TargetType="Label" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                            <Setter Property="Text" Value="Thank you for your feedback!" />
                        </DataTrigger>
                    </Label.Triggers>
                </Label>

                <Grid
                Margin="1,20,0,0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--Pancake Rate 5 Star-->
                    <t:DrMuscleButton
                    x:Name="BtnRate5Stars"
                    Grid.Row="0"
                    Grid.Column="0"
                    Text="RATE 5 STARS"
                    FontSize="13"
                    HeightRequest="45"
                    FontAttributes="Bold"
                    VerticalOptions="Center"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="False"
                    Style="{StaticResource buttonLinkStyle}"
                    TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                    Clicked="BtnRate5Stars_Clicked">
                        <t:DrMuscleButton.Triggers>
                            <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="IsVisible" Value="True" />
                            </DataTrigger>
                        </t:DrMuscleButton.Triggers>
                    </t:DrMuscleButton>

                    <!--Chat with us button-->
                    <t:DrMuscleButton
                    x:Name="BtnChatWithUs"
                    Grid.Row="0"
                    Grid.Column="0"
                    Text="CHAT WITH US"
                    FontSize="13"
                    HeightRequest="45"
                    FontAttributes="Bold"
                    VerticalOptions="Center"
                    HorizontalOptions="FillAndExpand"
                    IsVisible="True"
                    Style="{StaticResource buttonLinkStyle}"
                    TextColor="{x:Static app:AppThemeConstants.BlueColor}"
                    Clicked="BtnChatWithUs_Clicked">
                        <t:DrMuscleButton.Triggers>
                            <DataTrigger TargetType="t:DrMuscleButton" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="IsVisible" Value="False" />
                            </DataTrigger>
                        </t:DrMuscleButton.Triggers>
                    </t:DrMuscleButton>

                    <!--Pancake Share Free Trial-->
                    <Frame
                    x:Name="PanecakeBtnShareFreeTrial"
                    Grid.Row="0"
                    Grid.Column="1"
                    Padding="0,0,0,0"
                    Margin="10,0,0,0"
                    IsClippedToBounds="true"
                    CornerRadius="6"
                    VerticalOptions="Center"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="45"
                    IsVisible="False">
                        <Frame.Triggers>
                            <DataTrigger TargetType="Frame" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="IsVisible" Value="True" />
                            </DataTrigger>
                        </Frame.Triggers>
                        <StackLayout
                            HeightRequest="45"
                            Padding="0"
                            Margin="0"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="FillAndExpand">
                            <StackLayout.Background>
                                <LinearGradientBrush EndPoint="1,0">
                                    <GradientStop Color="#0C2432" Offset="0.0" />
                                    <GradientStop Color="#195276" Offset="1.0" />
                                </LinearGradientBrush>
                            </StackLayout.Background>
                            <t:DrMuscleButton
                        x:Name="BtnShareFreeTrial"
                        VerticalOptions="Center"
                        HeightRequest="45"
                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                        CornerRadius="6"
                        HorizontalOptions="FillAndExpand"
                        Text="SHARE FREE TRIAL"
                        Style="{StaticResource highEmphasisButtonStyle}"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"
                        Clicked="BtnShareFreeTrial_Clicked"/>
                        </StackLayout>
                    </Frame>

                    <!--Pancake Email Us-->
                    <Frame
                    x:Name="PanecakeBtnEmailUs"
                    Grid.Row="0"
                    Grid.Column="1"
                    Padding="0,0,0,0"
                    Margin="10,0,0,0"
                    IsClippedToBounds="true"
                    CornerRadius="6"
                    VerticalOptions="Center"
                    HorizontalOptions="FillAndExpand"
                    HeightRequest="45">
                        <Frame.Triggers>
                            <DataTrigger TargetType="Frame" Binding="{Binding SelectedSurveyOption}" Value="Happy">
                                <Setter Property="IsVisible" Value="False" />
                            </DataTrigger>
                        </Frame.Triggers>
                        <StackLayout
    HeightRequest="45"
    Padding="0"
    Margin="0"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand">
                            <StackLayout.Background>
                                <LinearGradientBrush EndPoint="1,0">
                                    <GradientStop Color="#0C2432" Offset="0.0" />
                                    <GradientStop Color="#195276" Offset="1.0" />
                                </LinearGradientBrush>
                            </StackLayout.Background>
                            <t:DrMuscleButton
                        x:Name="BtnEmailUs"
                        VerticalOptions="Center"
                        HeightRequest="45"
                        FontSize="{x:Static app:AppThemeConstants.CapitalTitleFontSize}"
                        CornerRadius="6"
                        HorizontalOptions="FillAndExpand"
                        Text="EMAIL FEEDBACK"
                        Style="{StaticResource highEmphasisButtonStyle}"
                        BackgroundColor="Transparent"
                        BorderColor="Transparent"
                        TextColor="White"
                        Clicked="BtnEmailUs_Clicked"/>
                        </StackLayout>
                    </Frame>
                </Grid>
            </StackLayout>
        </Grid>
    </controls:CustomFrame>
</ContentView>
