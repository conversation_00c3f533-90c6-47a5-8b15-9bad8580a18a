﻿using DrMaxMuscle.Dependencies;
using Foundation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class DrMuscleSubscription_iOS : IDrMuscleSubcription
    {
        public DrMuscleSubscription_iOS()
        {
            //PurchaseManager.Instance.OnLifetimeAccessPurchased += Instance_OnLifetimeAccessPurchased;
            PurchaseManager.Instance.OnMonthlyAccessPurchased += Instance_OnMonthlyAccessPurchased;
            PurchaseManager.Instance.OnYearlyAccessPurchased += Instance_OnYearlyAccessPurchased;
            PurchaseManager.Instance.OnMealPlanAccessPurchased += Instance_OnMealPlanAccessPurchased;
        }

        public event MonthlyAccessPurchased OnMonthlyAccessPurchased;
        public event YearlyAccessPurchased OnYearlyAccessPurchased;
        public event MealPlanAccessPurchased OnMealPlanAccessPurchased;

        void Instance_OnMonthlyAccessPurchased()
        {
            if (OnMonthlyAccessPurchased != null)
                OnMonthlyAccessPurchased();
        }

        void Instance_OnYearlyAccessPurchased()
        {
            if (OnYearlyAccessPurchased != null)
                OnYearlyAccessPurchased();
        }

        void Instance_OnMealPlanAccessPurchased()
        {
            if (OnMealPlanAccessPurchased != null)
                OnMealPlanAccessPurchased();
        }

        public bool IsActiveSubscriptions()
        {
            return PurchaseManager.Instance.IsActiveSubscription();
        }

        public bool IsActiveMealPlan()
        {
            return PurchaseManager.Instance.IsActiveMealPlan();
        }

        public void Init()
        {
        }

        public void RestorePurchases()
        {
            PurchaseManager.Instance.RestorePurchase();
        }

        public string GetBuildVersion()
        {
            return string.Format("Version {0} - Build {1}", NSBundle.MainBundle.InfoDictionary[new NSString("CFBundleShortVersionString")], NSBundle.MainBundle.InfoDictionary[new NSString("CFBundleVersion")]);
        }

        public bool IsMonthlyAccessPuchased()
        {
            return PurchaseManager.Instance.IsMonthlyAccessSubscribe();
        }

        public bool IsYearlyAccessPuchased()
        {
            return PurchaseManager.Instance.IsYearlyAccessSubscribe();
        }

        public bool IsMealPlanAccessPuchased()
        {
            return PurchaseManager.Instance.IsMealPlanAccessSubscribe();
        }

        public async Task<string> GetMonthlyPrice()
        {
            try
            {
                //MicrosPrice to MicrosPrice
                if (PurchaseManager.Instance.MonthlyAccessProduct.MicrosPrice > 0 && PurchaseManager.Instance.IsValidUserForDiscount)
                {
                    var introPrice = PurchaseManager.Instance.MonthlyAccessProduct.MicrosPrice / 1000000d;
                    var localeComponents = new NSMutableDictionary();
                    localeComponents.SetValueForKey(new NSString(PurchaseManager.Instance.MonthlyAccessProduct.CurrencyCode), new NSString("CurrencyCode"));
                    var locale = NSLocale.LocaleIdentifierFromComponents(localeComponents);

                    var formatter = new NSNumberFormatter()
                    {
                        FormatterBehavior = NSNumberFormatterBehavior.Version_10_4,
                        NumberStyle = NSNumberFormatterStyle.Currency,
                        Locale = PurchaseManager.Instance.SkProductMonthly != null ? PurchaseManager.Instance.SkProductMonthly.PriceLocale : new NSLocale(locale)
                    };
                    //Introductory price Title
                    return string.Format("{0}", formatter.StringFromNumber(introPrice));
                }
                return string.Format("{0}", PurchaseManager.Instance.MonthlyAccessProduct.LocalizedPrice);
            }
            catch (Exception ex)
            {

            }
            return "";
        }

        public async Task<string> GetMonthlyButtonLabel()
        {
            try
            {
                if (PurchaseManager.Instance.MonthlyAccessProduct.AppleExtras.IntroductoryOffer.Price > 0 && PurchaseManager.Instance.IsValidUserForDiscount)
                {
                    var introPrice = PurchaseManager.Instance.MonthlyAccessProduct.AppleExtras.IntroductoryOffer.Price / 1000000d;

                    var localeComponents = new NSMutableDictionary();
                    localeComponents.SetValueForKey(new NSString(PurchaseManager.Instance.MonthlyAccessProduct.CurrencyCode), new NSString("CurrencyCode"));
                    var locale = NSLocale.LocaleIdentifierFromComponents(localeComponents);

                    var formatter = new NSNumberFormatter()
                    {
                        FormatterBehavior = NSNumberFormatterBehavior.Version_10_4,
                        NumberStyle = NSNumberFormatterStyle.Currency,
                        Locale = PurchaseManager.Instance.SkProductMonthly != null ? PurchaseManager.Instance.SkProductMonthly.PriceLocale : new NSLocale(locale)
                    };
                    //Introductory price Title
                    return string.Format("1st month {0}, then {1}/month", formatter.StringFromNumber(introPrice), PurchaseManager.Instance.MonthlyAccessProduct.LocalizedPrice);
                }
                return string.Format("Sign up monthly ({0}/month)", PurchaseManager.Instance.MonthlyAccessProduct.LocalizedPrice);
            }
            catch (Exception ex)
            {

            }
            return "";
        }

        public async Task<string> GetMealPlanLabel()
        {
            try
            {
                return string.Format("{0}/month", PurchaseManager.Instance.MealPlanAddOnProduct.LocalizedPrice);
            }
            catch (Exception ex)
            {

            }
            return "";
        }
        public async Task<string> GetYearlyPrice()
        {
            try
            {

                return string.Format("{0}/year (4 months free)", PurchaseManager.Instance.YearlyAccessProduct.LocalizedPrice);

            }
            catch (Exception ex)
            {
                return "";
            }
        }

        public async Task<string> GetYearlyButtonLabel()
        {
            try
            {
                //if (PurchaseManager.Instance.YearlyAccessProduct.MicrosPrice > 0 && PurchaseManager.Instance.IsValidUserForDiscount)
                //{
                //    var introPrice = PurchaseManager.Instance.YearlyAccessProduct.MicrosPrice / 1000000d;
                //    var localeComponents = new NSMutableDictionary();
                //    localeComponents.SetValueForKey(new NSString(PurchaseManager.Instance.YearlyAccessProduct.CurrencyCode), new NSString("CurrencyCode"));
                //    var locale = NSLocale.LocaleIdentifierFromComponents(localeComponents);

                //    var formatter = new NSNumberFormatter()
                //    {
                //        FormatterBehavior = NSNumberFormatterBehavior.Version_10_4,
                //        NumberStyle = NSNumberFormatterStyle.Currency,
                //        Locale = PurchaseManager.Instance.SkProductMonthly != null ? PurchaseManager.Instance.SkProductMonthly.PriceLocale : new NSLocale(locale)
                //    };
                //    //Introductory price Title
                //    return string.Format("1st year {0}, then {1}/year", formatter.StringFromNumber(introPrice), PurchaseManager.Instance.YearlyAccessProduct.LocalizedPrice);
                //}
                return string.Format("{0}/year (4 months free)", PurchaseManager.Instance.YearlyAccessProduct.LocalizedPrice);
            }
            catch (Exception ex)
            {

            }
            return "";
        }


        public async Task BuyMonthlyAccess()
        {
            PurchaseManager.Instance.BuyMonthlySubscription();
        }

        public async Task BuyYearlyAccess()
        {
            PurchaseManager.Instance.BuyYearlySubscription();
        }

        public async Task BuyMealPlanAccess()
        {
            PurchaseManager.Instance.BuyMealPlanSubscription();
        }
    }
}
