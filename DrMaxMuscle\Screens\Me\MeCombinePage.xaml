﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Me.MeCombinePage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:control="clr-namespace:DrMaxMuscle.Controls"
             xmlns:constnats="clr-namespace:DrMaxMuscle.Constants"
             xmlns:oxy="clr-namespace:OxyPlot.Maui.Skia;assembly=OxyPlot.Maui.Skia"
             
             Title="MeCombinePage">
    <Grid>
        <StackLayout HorizontalOptions="FillAndExpand"
                 Padding="20,0,20,10" x:Name="ContentStack">

            <StackLayout x:Name="ChartStack"
                     VerticalOptions="FillAndExpand">
                <ScrollView VerticalOptions="FillAndExpand">
                    <StackLayout VerticalOptions="FillAndExpand">

                        <Grid IsEnabled="False">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <!--<control:ZoomableScrollview x:Name="plotScroll1"
                                                    Grid.Column="0"
                                                    Orientation="Horizontal">-->
                                <oxy:PlotView  x:Name="plotView"
                                           IsEnabled="false"
                                           IsVisible="true"
                                           VerticalOptions="Start"
                                           HeightRequest="150">
                                </oxy:PlotView>
                            <!--</control:ZoomableScrollview>
                            <control:ZoomableScrollview x:Name="plotScroll2"
                                                    Grid.Row="1"
                                                    
                                                    Orientation="Horizontal">-->
                                <oxy:PlotView x:Name="plotViewVolume"
                                          Grid.Row="1"
                                          IsEnabled="false"
                                          IsVisible="true"
                                          VerticalOptions="Start"
                                          HeightRequest="150">
                                </oxy:PlotView>
                            <!--</control:ZoomableScrollview>-->

                            
                                <oxy:PlotView
                                     Grid.Row="2"
                x:Name="plotView3"
                IsVisible="true"
                VerticalOptions="Start"
                HeightRequest="150">
                                </oxy:PlotView>
                            
                        </Grid>

                        <!--<Button
                            Margin="7,0,7,10"
                            Text="Update body weight"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="Center"
                            Clicked="UpdateBodyweightClicked"
                            Style="{StaticResource buttonStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />-->
                        <Label x:Name="LblProgress"
                           Text=""
                               Margin="0,30,0,0"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                        <Label x:Name="LblSetsProgress"
                           Text=""
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                        <Label Margin="0,0,0,0"
                           x:Name="lblWorkoutsDone"
                           IsVisible="false"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                        <Label x:Name="lblLevel"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                        <Label x:Name="lblLiftedCount"
                           IsVisible="false"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />
                        <StackLayout Orientation="Horizontal" HorizontalOptions="Center">

                            <!--<Image Source="edit_plate_blue.png"
                               HeightRequest="50"
                               Aspect="AspectFit" VerticalOptions="Center" />-->
                            <Label
                            Text="View more stats on the Web"
                            HeightRequest="45"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            Style="{StaticResource OnBoardingLabelStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />
                            <StackLayout.GestureRecognizers>
                                <TapGestureRecognizer Tapped="ViewMoreStats_Clicked" />
                            </StackLayout.GestureRecognizers>
                        </StackLayout>
                        <Label x:Name="lblWorkout"
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                        <!--<Label x:Name="lblProgram"
                           Text=""
                           HorizontalOptions="CenterAndExpand"
                           Style="{StaticResource OnBoardingLabelStyle}" />

                    <StackLayout Orientation="Horizontal" HorizontalOptions="Center">
                        <Label
                            Text="Change program"
                            HeightRequest="45"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            Style="{StaticResource OnBoardingLabelStyle}"
                            TextColor="{x:Static constnats:AppThemeConstants.BlueColor}" />
                        <StackLayout.GestureRecognizers>
                            <TapGestureRecognizer Tapped="ChangeWorkoutClicked" />
                        </StackLayout.GestureRecognizers>
                    </StackLayout>-->

                    </StackLayout>
                </ScrollView>
            </StackLayout>
            <Label x:Name="LblTimeFrame"
               Margin="20,10,20,5"
               Style="{StaticResource BoldLabelStyle}" />
            <StackLayout x:Name="PickerStack"
                     VerticalOptions="End">
                <Border Margin="20,0,20,10"
                        HeightRequest="40" BackgroundColor="{OnPlatform Android='#145477'}">
                    <control:DropDownPicker x:Name="ExericsesPicker" Margin="0" 
                        Image="{OnPlatform Android='white_down_arrow', iOS='black_down_arrow'}"
                        Style="{StaticResource PickerStyle}" >
                    </control:DropDownPicker>
                </Border>
                <Border 
                    HeightRequest="40" BackgroundColor="{OnPlatform Android='#145477'}"
                    Margin="20,0,20,0">
                    <control:DropDownPicker x:Name="DatePicker"
                        Image="{OnPlatform Android='white_down_arrow', iOS='black_down_arrow'}"
                        Style="{StaticResource PickerStyle}" >

                    </control:DropDownPicker>
                </Border>
                
            </StackLayout>
        </StackLayout>
        <StackLayout HorizontalOptions="FillAndExpand" x:Name="NoDataLabel" VerticalOptions="FillAndExpand" Grid.Row="0" BackgroundColor="#D4D4D4" Spacing="10" IsVisible="false">

            <StackLayout VerticalOptions="CenterAndExpand" HorizontalOptions="Center"  BackgroundColor="Transparent" Margin="0,30,0,0"  Spacing="10" Padding="0,45,0,2">

                <Image WidthRequest="150" HeightRequest="150" HorizontalOptions="Center" VerticalOptions="Start" Source="trophy.png" />

                <Label Text="Nothing to chart yet!" Margin="0,30,0,0" HorizontalOptions="Center" FontSize="20" FontAttributes="Bold" TextColor="Black" />
                <Label Text="Save an exercise to see your charts here." HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>






            </StackLayout>
            <Frame
                x:Name="ToolTipButton"
            Padding="0"
            Margin="25,0,25,25"
            IsClippedToBounds="true"
                Style="{StaticResource GradientFrameStyleBlue}"
            CornerRadius="0"
                VerticalOptions="EndAndExpand"
                        HorizontalOptions="FillAndExpand" 
                        HeightRequest="66" >
                <!--effects:TooltipEffect.Text="Tap me"
                               effects:TooltipEffect.BackgroundColor="{x:Static constnats:AppThemeConstants.ReysBlueColor}"
                                effects:TooltipEffect.TextColor="White"
                                effects:TooltipEffect.Position="Top"
                                effects:TooltipEffect.HasTooltip="True"
                                effects:TooltipEffect.HasShowTooltip="False">-->
                

                <t:DrMuscleButton x:Name="ButtonStartWorkout" BackgroundColor="#195377" Text="Start workout" HeightRequest="66" 
                        BorderColor="Transparent"
                                
                        TextColor="White" Style="{StaticResource buttonStyle}" HorizontalOptions="FillAndExpand"
                                >
                </t:DrMuscleButton>
            </Frame>
        </StackLayout>
    </Grid>
</ContentPage>