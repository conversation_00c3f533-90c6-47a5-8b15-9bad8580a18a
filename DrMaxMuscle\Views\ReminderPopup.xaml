﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:PopupPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:constants="clr-namespace:DrMaxMuscle.Constants"
                  CloseWhenBackgroundIsClicked="false"
                   
Opacity="0.9"
BackgroundColor="#333333"
                xmlns:t="clr-namespace:DrMaxMuscle.Layout"
               xmlns:toolkit="clr-namespace:RGPopup.Maui.Pages;assembly=RGPopup.Maui"
             x:Class="DrMaxMuscle.Views.ReminderPopup">
    <toolkit:PopupPage.Content>
        <AbsoluteLayout IgnoreSafeArea="True">
            <StackLayout Padding="0,0,0,0" AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand">
                <Image Source="nav.png" Aspect="Fill" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand"/>
            </StackLayout>
            <StackLayout
         x:Name="TimerView"
         HorizontalOptions="FillAndExpand"
         VerticalOptions="FillAndExpand"
         AbsoluteLayout.LayoutFlags="All" AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
         Padding="20,10,20,0"
         BackgroundColor="Transparent">
                <StackLayout
             VerticalOptions="CenterAndExpand"
             Margin="0,30,0,0">
                    <Label
                 x:Name="LblChooseDays"
                 FontSize="Medium"
                 FontAttributes="Bold"
                 Text="Choose workout days"
                 TextColor="White"
                 HorizontalOptions="Center" />
                    <Label
                 x:Name="LblProgramName"
                 Text="For your program"
                 TextColor="White"
                     HorizontalTextAlignment="Center"
                 HorizontalOptions="Center" />

                    <StackLayout Margin="0,10" Spacing="8" Orientation="Horizontal" HorizontalOptions="Center">

                        <Border
                 WidthRequest="36"
        HeightRequest="36"
        StrokeThickness="2"
        Stroke="#ECFF92"
        BackgroundColor="Transparent"
        Opacity="1"
        HorizontalOptions="Center"
        VerticalOptions="Center"
        x:Name="MondayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblMonday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="M"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="MondayTapped" />
                            </Border.GestureRecognizers>
                        </Border>

                        <Border
                     WidthRequest="36"
                    HeightRequest="36"
                    StrokeThickness="2"
                    Stroke="#ECFF92"
                    BackgroundColor="Transparent"
                    Opacity="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    x:Name="TuesdayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblTuesday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="T"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TuesdayTapped" />
                            </Border.GestureRecognizers>
                        </Border>

                        <Border
                     WidthRequest="36"
                    HeightRequest="36"
                    StrokeThickness="2"
                    Stroke="#ECFF92"
                    BackgroundColor="Transparent"
                    Opacity="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    x:Name="WednesdayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblWednesday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="W"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="WednesdayTapped" />
                            </Border.GestureRecognizers>
                        </Border>

                        <Border
                         WidthRequest="36"
                        HeightRequest="36"
                        StrokeThickness="2"
                        Stroke="#ECFF92"
                        BackgroundColor="Transparent"
                        Opacity="1"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        x:Name="ThursdayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblThursday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="T"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="ThursdayTapped" />
                            </Border.GestureRecognizers>
                        </Border>
                        <Border
                     WidthRequest="36"
        HeightRequest="36"
        StrokeThickness="2"
        Stroke="#ECFF92"
        BackgroundColor="Transparent"
        Opacity="1"
        HorizontalOptions="Center"
        VerticalOptions="Center"
        x:Name="FridayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblFriday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="F"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="FridayTapped" />
                            </Border.GestureRecognizers>
                        </Border>
                        <Border
                     WidthRequest="36"
                    HeightRequest="36"
                    StrokeThickness="2"
                    Stroke="#ECFF92"
                    BackgroundColor="Transparent"
                    Opacity="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    x:Name="SaturdayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblSaturday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="S"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="SaturdayTapped" />
                            </Border.GestureRecognizers>
                        </Border>
                        <Border
                     WidthRequest="36"
                    HeightRequest="36"
                    StrokeThickness="2"
                    Stroke="#ECFF92"
                    BackgroundColor="Transparent"
                    Opacity="1"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    x:Name="SundayCircle">
                            <Border.StrokeShape>
                                <Ellipse />
                            </Border.StrokeShape>
                            <Label
                         x:Name="LblSunday"
                         FontSize="20"
                         TextColor="White"
                         WidthRequest="30"
                         HeightRequest="30"
                         Text="S"
                         FontAttributes="Bold"
                         HorizontalTextAlignment="Center"
                         VerticalTextAlignment="Center"
                         HorizontalOptions="Center"
                         VerticalOptions="Center" />
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="SundayTapped" />
                            </Border.GestureRecognizers>
                        </Border>
                    </StackLayout>
                    <Label
                 x:Name="LblInstruction"
                 Text=""
                 TextColor="White"
                     HorizontalTextAlignment="Center"
                 HorizontalOptions="Center" />

                    <StackLayout
                 Orientation="Vertical"
                 HorizontalOptions="CenterAndExpand"
                 Margin="0,30,0,0">
                        <Label
                 x:Name="LblChooseTimes"
                 FontSize="Medium"
                         FontAttributes="Bold"
                 Text="Choose time"
                 TextColor="White"
                 HorizontalOptions="Center" VerticalOptions="Center" />
                        <Border
                            Margin="0,6,0,0"
                            HeightRequest="40"
                            StrokeThickness="2"
                            Stroke="#ECFF92"
                            Padding="0,0,0,-2"
                            BackgroundColor="Transparent"
                            Opacity="1"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            x:Name="TimeFrame">

                            <!-- Define the shape as a rectangle (Box) -->
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="0" />
                            </Border.StrokeShape>
                            
                            <StackLayout VerticalOptions="FillAndExpand" HorizontalOptions="Center" Orientation="Horizontal" Padding="10,0">
                                <TimePicker HorizontalOptions="Center" TextColor="White" BackgroundColor="Transparent" x:Name="timePicker" Unfocused="Timer_Unfocused" >
                                    <TimePicker.Format>hh:mm tt</TimePicker.Format>
                                </TimePicker>
                                <Image Source="open.png" WidthRequest="25" HeightRequest="25" Aspect="AspectFit" />
                            </StackLayout>
                            <Border.GestureRecognizers>
                                <TapGestureRecognizer Tapped="TimerTapped" />
                            </Border.GestureRecognizers>
                        </Border>
                    </StackLayout>

                </StackLayout>
                <StackLayout
             VerticalOptions="End"
             Orientation="Vertical"
             HorizontalOptions="FillAndExpand"
             Margin="0,0,0,0"
             Padding="25.0">
                    <t:DrMuscleButton
                 ContentLayout="Top,8"
                 x:Name="SkipButton"
                 Text="Skip"
                 BackgroundColor="Transparent"
                 HorizontalOptions="CenterAndExpand"
                 TextColor="#97D2F3"
                 Clicked="ButtonSkip_Clicked"
                 Padding="0,5"
                        Margin="0,0,0,12"/>
                    <t:DrMuscleButton
                 x:Name="SaveButton"
                     HeightRequest="55"
                     CornerRadius="0"
                 Text="Save reminders"
                 HorizontalOptions="FillAndExpand"
                 TextColor="#0C2432"
                 Clicked="ButtonDone_Clicked"
                     BackgroundColor="#ECFF92"
                  />
                </StackLayout>
            </StackLayout>
        </AbsoluteLayout>
    </toolkit:PopupPage.Content>
</toolkit:PopupPage>
