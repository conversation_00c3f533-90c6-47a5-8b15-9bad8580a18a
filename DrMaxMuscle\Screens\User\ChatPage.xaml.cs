using System.Collections.ObjectModel;
using Acr.UserDialogs;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Message;
using DrMaxMuscle.Resx;
using Rollbar;

namespace DrMaxMuscle.Screens.User;

public partial class ChatPage : ContentPage, IActiveAware
{
    public event EventHandler IsActiveChanged;

    bool _isActive;
    public bool IsActive
    {
        get => _isActive;
        set
        {
            if (_isActive != value)
            {
                _isActive = value;
                IsActiveChanged?.Invoke(this, EventArgs.Empty);
            }
        }
    }



    public ObservableCollection<DrMaxMuscle.Helpers.Messages> messageList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();


    public ChatPage()
    {
      
        InitializeComponent();
        RefreshLocalized();
        MessagingCenter.Subscribe<Message.LanguageChangeMessage>(this, "LocalizeUpdated", (obj) =>
        {
            RefreshLocalized();
        });
        lblTitle.Text = "Dr. Muscle AI, Victoria";
        //Live
        //SendBirdClient.Init("91658003-270F-446B-BD61-0043FAA8D641");

        //Test
        //SendBirdClient.Init("05F82C36-1159-4179-8C49-5910C7F51D7D");

    }
    void RefreshLocalized()
    {
        Title = AppResources.GroupChatBeta;
    }

    public  void OnBeforeShow()
    {

        var loadChatMessage = new LoadChatMessage();
        loadChatMessage.IsFromLogin = true;
        MessagingCenter.Send(loadChatMessage, "LoadChatMessage");

    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (!App.IsSidemenuOpen)
        {
            var loadChatMessage = new LoadChatMessage();
            loadChatMessage.IsFromLogin = false;
            MessagingCenter.Send(loadChatMessage, "LoadChatMessage");
        }
        try
        {

            var isAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");
            if (isAdmin)
            {
                var timerToolbarItem = new ToolbarItem("1:1 support", "", Support_Tapped, ToolbarItemOrder.Primary, 0);
                ToolbarItems.Clear();
                ToolbarItems.Add(timerToolbarItem);
                //SupportToolbarItem.IsEnabled = true;
            }
            else
            {

            }
        }
        catch (Exception ex)
        {

        }
    }
    public void MovetoSupport()
    {
        //   Support_Tapped(null, EventArgs.Empty);
    }

    async void Support_Tapped()
    {
        try
        {
            InboxPage page = new InboxPage();
            await Navigation.PushAsync(page);
            //await PagesFactory.PushAsync<InboxPage>();
        }
        catch (Exception ex)
        {

        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        var unLoadChat = new UnLoadChatMessage();
        MessagingCenter.Send(unLoadChat, "UnLoadChatMessage");

    }

    // Uncomment code please
    //protected override bool OnBackButtonPressed()
    //{
    //    if (PopupNavigation.Instance.PopupStack.Count > 0)
    //    {
    //        PopupNavigation.Instance.PopAllAsync();
    //        return true;
    //    }
    //    Device.BeginInvokeOnMainThread(async () =>
    //    {
    //        ConfirmConfig exitPopUp = new ConfirmConfig()
    //        {
    //            Title = AppResources.Exit,
    //            Message = AppResources.AreYouSureYouWantToExit,
    //            AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
    //            OkText = AppResources.Yes,
    //            CancelText = AppResources.No,
    //        };

    //        var result = await UserDialogs.Instance.ConfirmAsync(exitPopUp);
    //        if (result)
    //        {
    //            var kill = DependencyService.Get<IKillAppService>();
    //            kill.ExitApp();
    //        }
    //    });
    //    return true;
    //}
}
