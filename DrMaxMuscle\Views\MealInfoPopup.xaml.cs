using Acr.UserDialogs;
using CommunityToolkit.Maui.Views;
using DrMaxMuscle.Message;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Pages;
using RGPopup.Maui.Services;
using System.Text.RegularExpressions;

namespace DrMaxMuscle.Views;

public partial class MealInfoPopup : PopupPage
{
    public MealInfoPopup()
    {
        InitializeComponent();
    }
    async void BtnSave_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(EditorMealInfo?.Text) || string.IsNullOrWhiteSpace(EditorMealInfo?.Text))
                return;
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                await UserDialogs.Instance.AlertAsync("Please check your internet connection", "Internet error");
                return;
            }
            await MauiProgram.SafeDismissAllPopups();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    await PopupNavigation.Instance.PopAllAsync();
            MessagingCenter.Send<AddedMealInfoMessage>(new AddedMealInfoMessage() { MealInfoStr = EditorMealInfo?.Text }, "AddedMealInfoMessage");

        }
        catch (Exception ex)
        {

        }
    }

    async void BtnCancel_Clicked(System.Object sender, System.EventArgs e)
    {
        try
        {
            await MauiProgram.SafeDismissAllPopups();
            //if (PopupNavigation.Instance.PopupStack?.Count() > 0)
            //    PopupNavigation.Instance.PopAllAsync();

            MessagingCenter.Send<AddedMealInfoMessage>(new AddedMealInfoMessage() { MealInfoStr = EditorMealInfo?.Text, IsCanceled = true }, "AddedMealInfoMessage");

        }
        catch (Exception ex)
        {

        }    
    }
}