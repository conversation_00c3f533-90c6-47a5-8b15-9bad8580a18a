﻿using Android.Content;
using Android.Graphics.Drawables;
using Microsoft.Maui.Controls.Compatibility;
using DrMaxMuscle.Platforms.Android.Renderers;
using Microsoft.Maui.Controls.Compatibility.Platform.Android.AppCompat;
using Microsoft.Maui.Controls.Platform;
using Android.Views;
using Google.Android.Material.BottomNavigation;
using DrMaxMuscle;

//[assembly: ExportRenderer(typeof(MainTabbedPage), typeof(GradientTabbedPageRenderer))]
namespace DrMaxMuscle.Platforms.Android.Renderers
{
    public class GradientTabbedPageRenderer : TabbedPageRenderer
    {
        public GradientTabbedPageRenderer(Context context) : base(context)
        {
        }

        protected override void OnElementChanged(ElementChangedEventArgs<TabbedPage> e)
        {
            base.OnElementChanged(e);
            if (e.NewElement != null)
            {
                SetGradientBackground();
            }
        }

        protected override void OnElementPropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            base.OnElementPropertyChanged(sender, e);
            if (e.PropertyName == TabbedPage.BackgroundColorProperty.PropertyName)
            {
                SetGradientBackground();
            }
        }

        private void SetGradientBackground()
        {
            // Hex color #0C2432
            //int alpha = 255; // Fully opaque
            //int red = 0x0C;  // 12
            //int green = 0x24; // 36
            //int blue = 0x32;  // 50

            //// Combine into a single integer value
            //int colorInt = (alpha << 24) | (red << 16) | (green << 8) | blue;
            //var colors = new int[]
            // {
            //    colorInt,
            //    colorInt
            // };
            int[] colors = {
                unchecked((int)0xFF0C2432),
                unchecked((int)0xFF195276)
            };

            var gradientDrawable = new GradientDrawable(
                GradientDrawable.Orientation.LeftRight, colors);

            gradientDrawable.SetCornerRadius(0f);
            // This assumes that the BottomNavigationView is the second child of the TabbedPageRenderer
            BottomNavigationView bottomNavigationView = null;
            for (int i = 0; i < ChildCount; i++)
            {
                var child = GetChildAt(i);
                if (child is BottomNavigationView view)
                {
                    bottomNavigationView = view;
                    break;
                }
            }

            if (bottomNavigationView != null)
            {
                bottomNavigationView.SetBackground(gradientDrawable);
            }
        }
    }
}
