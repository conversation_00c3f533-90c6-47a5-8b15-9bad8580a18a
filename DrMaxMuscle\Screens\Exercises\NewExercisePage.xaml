<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Screens.Exercises.NewExercisePage"
             Title="NewExercisePage">
    <Grid
    Padding="15"
    RowSpacing="15">
        <Grid.RowDefinitions>
            <RowDefinition
            Height="*" />
            <RowDefinition
            Height="*" />
            <RowDefinition
            Height="*" />
        </Grid.RowDefinitions>

        <!--WORKOUT Frame (row 0)-->
        <Frame
        Padding="0"
        CornerRadius="12"
        Grid.Row="0"
        HasShadow="False"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="settingsbackground.png"
                ErrorPlaceholder="backgroundblack.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="DrMuscleWorkoutsButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>

        <!--SETTINGS Frame (Old) (row 1)-->
        <!--MY WORKOUTS Frame (New) (row 1)-->
        <Frame
        Padding="0"
        CornerRadius="12"
        Grid.Row="1"
        HasShadow="False"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="exercise_background.png"
                ErrorPlaceholder="backgroundblack.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="MyWorkoutButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>

        <!--EXERCISE Frame (row 2)-->
        <Frame
        Padding="0"
        CornerRadius="12"
        Grid.Row="2"
        HasShadow="False"
        IsClippedToBounds="True">
            <ffimageloading:CachedImage
            Source="workoutbackground.png"
                ErrorPlaceholder="backgroundblack.png"
            Aspect="AspectFill" />
            <Frame.GestureRecognizers>
                <TapGestureRecognizer
                Tapped="ExercisesButton_Clicked" />
            </Frame.GestureRecognizers>
        </Frame>

        <!--Button Workout (row 0)-->
        <t:DrMuscleButton
        x:Name="DrMuscleWorkoutsButton"
        Grid.Row="0"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30"/>

        <!--Button MyWorkout (row 1)-->
        <t:DrMuscleButton
        x:Name="MyWorkoutButton"
        Grid.Row="1"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30"/>

        <!--Setting Button (Old) (row 2)-->
        <!--Exercise Button (New) (row 2)-->
        <t:DrMuscleButton
        x:Name="ExercisesButton"
        Grid.Row="2"
        Text="Featured"
        Style="{StaticResource highEmphasisButtonStyle}"
        BorderColor="Transparent"
        HorizontalOptions="Start"
        VerticalOptions="Start"
        HeightRequest="55"
        TextColor="White"
        BackgroundColor="Transparent"
        FontAttributes="None"
        Margin="15,0"
        Padding="0"
        FontSize="30"/>
    </Grid>
</ContentPage>