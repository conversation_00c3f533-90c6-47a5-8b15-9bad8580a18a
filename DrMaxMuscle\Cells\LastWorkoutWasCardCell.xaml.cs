using DrMaxMuscle.Helpers;

namespace DrMaxMuscle.Cells;

public partial class LastWorkoutWasCardCell : ContentView
{
	public LastWorkoutWasCardCell()
	{
		InitializeComponent();
	}
    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();
        var recordModel = (BotModel)this.BindingContext;
        if (recordModel == null)
            return;
        if (recordModel.IsNewRecordAvailable && recordModel.StrengthImage == "startrophy.png")
            WeightProgress2.Margin = new Thickness(10, 11, 10, 10);
    }
}