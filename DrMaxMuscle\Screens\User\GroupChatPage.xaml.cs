using Acr.UserDialogs;
using DrMaxMuscle.Constants;
using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Helpers;
using DrMaxMuscle.Resx;
using DrMaxMuscle.Screens.Subscription;
using DrMaxMuscle.Utility;
using DrMaxMuscle.Views;
using DrMuscleWebApiSharedModel;
using Microsoft.Maui.Networking;
using RGPopup.Maui.Services;
using System.Collections.ObjectModel;

namespace DrMaxMuscle.Screens.User;

public partial class GroupChatPage : ContentPage
{
    bool IsLoading = false;
    bool IsLoadMore = false;
    string supportUrl = "";
    public ObservableCollection<DrMaxMuscle.Helpers.Messages> groupChannelsList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();
    public ObservableCollection<DrMaxMuscle.Helpers.Messages> messageList = new ObservableCollection<DrMaxMuscle.Helpers.Messages>();

    bool IsAdmin = false;
    public GroupChatPage()
    {
        InitializeComponent();
        //Live

        //Test
        //SendBirdClient.Init("05F82C36-1159-4179-8C49-5910C7F51D7D");
        //lstView.ItemsSource = groupChannelsList;
        //lstView.ItemTapped += LstView_ItemTapped;
        TapGestureRecognizer btnSendTapGestureRecognizer = new TapGestureRecognizer();
        btnSendTapGestureRecognizer.Tapped += BtnSendTapGestureRecognizer_Tapped;


        timerToolbarItem = new ToolbarItem("1:1 support", "", SlideTimerAction, ToolbarItemOrder.Primary, 0);

        groupChatItem = new ToolbarItem("Group chat", "", GroupChatAction, ToolbarItemOrder.Primary, 0);

    }

    void NewBindingContextChanged()
    {
        if (App.IsV1User || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>"))
        {
            try
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    ToolbarItems.Clear();
                    ToolbarItems.Add(timerToolbarItem);
                });

            }
            catch (Exception ex)
            {

            }
        }
        else
        {
            try
            {
                Device.BeginInvokeOnMainThread(() =>
                {
                    ToolbarItems.Clear();
                    ToolbarItems.Add(groupChatItem);
                });
            }
            catch (Exception ex)
            {

            }
        }
    }
    public ToolbarItem timerToolbarItem;
    public ToolbarItem groupChatItem;

    public void SlideTimerAction()
    {
        Support_Tapped(null, EventArgs.Empty);
    }

    public async void GroupChatAction()
    {
        try
        {
            await Navigation.PushAsync(new GroupChatPage());
        }
        catch (Exception ex)
        {

        }
        //await PagesFactory.PushAsync<GroupChatPage>();
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
    }


    protected async override void OnAppearing()
    {
        base.OnAppearing();
        IsLoadMore = false;
        if (LocalDBManager.Instance.GetDBSetting("email") == null)
            return;
        IsAdmin = LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>") || LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>");

        groupChannelsList.Clear();
        messageList.Clear();
        CheckIsV1();
        if (messageList.Count == 0)
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {

                UserDialogs.Instance.ShowLoading();
            }
        }

        try
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet)
            {
                ConnectionErrorPopup();
                return;
            }
            Device.BeginInvokeOnMainThread(async () =>
            {
                if (!App.IsV1User)
                {

                    var alert2 = await HelperClass.DisplayCustomPopup("","Welcome to group chat! Sign up to unlock or continue preview.",
                        "Preview","Sign up");
                    alert2.ActionSelected += async (sender,action) => {
                        if(action == PopupAction.OK)
                        {
                        }else{
                            try
                            {
                                SubscriptionPage subscription = new SubscriptionPage();
                                subscription.OnBeforeShow();
                                await Navigation.PushAsync(subscription);
                                //await PagesFactory.PushAsync<SubscriptionPage>();
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                    };

                  

                    // ConfirmConfig alertConfig = new ConfirmConfig()
                    // {
                    //     Title = "",
                    //     Message = "Welcome to group chat! Sign up to unlock or continue preview.",
                    //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                    //     OkText = "Preview",
                    //     CancelText = "Sign up",
                    //     OnAction = async (bool ok) =>
                    //     {
                    //         if (ok)
                    //         {

                    //         }
                    //         else
                    //         {
                    //             SubscriptionPage subscription = new SubscriptionPage();
                    //             subscription.OnBeforeShow();
                    //             await Navigation.PushAsync(subscription);
                    //             //await PagesFactory.PushAsync<SubscriptionPage>();
                    //         }

                    //     }
                    // };

                    // UserDialogs.Instance.Confirm(alertConfig);

                }
                else
                {

                    //UserDialogs.Instance.HideLoading();

                    //var _tipsArray = this.GetTipsArray();

                    //if (Config.ShowTipsNumber >= _tipsArray.Count)
                    //    Config.ShowTipsNumber = 0;

                    //var startworkoutText = "Start workout";
                    //var waitHandle = new EventWaitHandle(false, EventResetMode.AutoReset);
                    //System.Diagnostics.Debug.WriteLine($"Tips: {_tipsArray[Config.ShowTipsNumber]}");
                    //var modalPage = new Views.GeneralPopup("Lamp.png", $"{_tipsArray[Config.ShowTipsNumber][0]}", $"{_tipsArray[Config.ShowTipsNumber][1]}", "Enter chat", null, false, false, _tipsArray[Config.ShowTipsNumber][2], _tipsArray[Config.ShowTipsNumber][3], "false", "false", "false", "false", "false", true);
                    //Config.ShowTipsNumber += 1;
                    //PopupNavigation.Instance.PushAsync(modalPage);


                }
            });
            //SendBirdClient.Connect(LocalDBManager.Instance.GetDBSetting("email").Value, Connect_Handler);
            var list = messageList.Count == 0 && CurrentLog.Instance.GroupChats.Count != 0 ? CurrentLog.Instance.GroupChats : await DrMuscleRestClient.Instance.FetchGroupMessages(new GroupChatModel()
            {
                UpdatedAt = messageList.Count == 0 ? DateTime.UtcNow : messageList.Last().CreatedDate
            });
            UserDialogs.Instance.HideLoading();

            foreach (GroupChatModel msg in list)
            {
                messageList.Add(new DrMaxMuscle.Helpers.Messages()
                {
                    Message = msg.Message,
                    UserId = msg.SenderEmail,
                    Nickname = IsAdmin && !msg.SenderEmail.ToLower().Equals("<EMAIL>") ? $"{msg.SenderName} ({msg.SenderEmail})" : msg.SenderName,
                    CreatedDate = msg.UpdatedAt,
                    ProfileUrl = ""

                });
            }

            Device.BeginInvokeOnMainThread(() =>
            {

                lstChats.ItemsSource = messageList;
                if (messageList.Count < 26)
                {
                    lstChats.ScrollTo(messageList.FirstOrDefault(), ScrollToPosition.Start, animate: false);
                    //lstChats.ScrollToFirst();
                }
                //else
                //  lstChats.ScrollTo(messageList[messages.Count - 2], ScrollToPosition.Start, false);

            });

        }
        catch (Exception ex)
        {

        }
    }
    private bool isPresented = false;
    protected async Task ConnectionErrorPopup()
    {
        if (isPresented)
            return;
        isPresented = true;
        // await UserDialogs.Instance.AlertAsync(new AlertConfig()
        // {
        //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
        //     Message = AppResources.PleaseCheckInternetConnection,
        //     Title = AppResources.ConnectionError,
        //     OkText = "Try again"
        // });

        await HelperClass.DisplayCustomPopupForResult(AppResources.ConnectionError,
                       AppResources.PleaseCheckInternetConnection,"Try again","");
        isPresented = false;
    }
    private async void CheckIsV1()
    {
        NotPurchased.IsVisible = !(await CanGoFurtherWithoughtLoader());
    }

    async void NotPurchased_Clicked(object sender, System.EventArgs e)
    {
        //AlertConfig alertConfig = new AlertConfig()
        //{
        //    Title = "",
        //    //Message = AppResources.GroupChatIsPayingSubscribeOnly,
        //    Message = AppResources.GroupChatIsPayingSubscribeOnly,
        //    OkText = AppResources.Ok,
        //    AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray)
        //};
        //UserDialogs.Instance.Alert(alertConfig);
        try
        {
            if (App.IsFreePlan)
            {
                var alertConfig = await HelperClass.DisplayCustomPopup("You discovered a premium feature!", "Upgrading will unlock group chat�1-on-1 support is always free.",
                    "1-on-1 support", "Upgrade");
                alertConfig.ActionSelected += async (sender, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        CurrentLog.Instance.ChannelUrl = supportUrl;
                        CurrentLog.Instance.RoomId = 0;
                        SupportPage supportPage = new SupportPage();
                        supportPage.OnBeforeShow();
                        await Navigation.PushAsync(supportPage);
                        //await PagesFactory.PushAsync<SupportPage>();
                    }
                    else
                    {
                        SubscriptionPage page = new SubscriptionPage();
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);
                        //await PagesFactory.PushAsync<SubscriptionPage>();
                    }
                };



                // ConfirmConfig alertConfig = new ConfirmConfig()
                // {
                //     Title = "You discovered a premium feature!",
                //     Message = "Upgrading will unlock group chat�1-on-1 support is always free.",
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = "1-on-1 support",
                //     CancelText = "Upgrade",
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             CurrentLog.Instance.ChannelUrl = supportUrl;
                //             CurrentLog.Instance.RoomId = 0;
                //             SupportPage supportPage = new SupportPage();
                //             supportPage.OnBeforeShow();
                //             await Navigation.PushAsync(supportPage);
                //             //await PagesFactory.PushAsync<SupportPage>();
                //         }
                //         else
                //         {
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             await Navigation.PushAsync(page);
                //             //await PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //     }
                // };
                // UserDialogs.Instance.Confirm(alertConfig);
            }
            else
            {

                var alertConfig = await HelperClass.DisplayCustomPopup("", AppResources.GroupChatIsPayingSubscribeOnly,
                      AppResources.FreeOnSupport, AppResources.SignUptoUnlock);

                alertConfig.ActionSelected += async (sender, action) =>
                {
                    if (action == PopupAction.OK)
                    {
                        CurrentLog.Instance.ChannelUrl = supportUrl;
                        SupportPage supportPage = new SupportPage();
                        supportPage.OnBeforeShow();
                        await Navigation.PushAsync(supportPage);
                        //await PagesFactory.PushAsync<SupportPage>();
                    }
                    else
                    {
                        SubscriptionPage page = new SubscriptionPage();
                        page.OnBeforeShow();
                        await Navigation.PushAsync(page);
                        //await PagesFactory.PushAsync<SubscriptionPage>();
                    }
                };



                // ConfirmConfig alertConfig = new ConfirmConfig()
                // {
                //     Title = "",
                //     Message = AppResources.GroupChatIsPayingSubscribeOnly,
                //     AndroidStyleId = DependencyService.Get<IStyles>().GetStyleId(EAlertStyles.AlertDialogCustomGray),
                //     OkText = AppResources.FreeOnSupport,
                //     CancelText = AppResources.SignUptoUnlock,
                //     OnAction = async (bool ok) =>
                //     {
                //         if (ok)
                //         {
                //             CurrentLog.Instance.ChannelUrl = supportUrl;
                //             SupportPage supportPage = new SupportPage();
                //             supportPage.OnBeforeShow();
                //             await Navigation.PushAsync(supportPage);
                //             //await PagesFactory.PushAsync<SupportPage>();
                //         }
                //         else
                //         {
                //             SubscriptionPage page = new SubscriptionPage();
                //             page.OnBeforeShow();
                //             await Navigation.PushAsync(page);
                //             //await PagesFactory.PushAsync<SubscriptionPage>();
                //         }
                //     }
                // };

                // UserDialogs.Instance.Confirm(alertConfig);
            }
        }
        catch (Exception ex)
        {

        }
    }

    async void LoadData()
    {
        try
        {


            if (groupChannelsList.Count == 1)
                return;
            //Device.BeginInvokeOnMainThread(() => {
            //    lstView.ItemsSource = groupChannelsList;
            //});

            DrMaxMuscle.Helpers.Messages messages = new DrMaxMuscle.Helpers.Messages()
            {
                Message = AppResources.TapHereFor11Chat,
                Nickname = AppResources.ChatWithSupport,
                CreatedDate = DateTime.Now,
                ChatType = ChannelType.Group,
                UserId = "<EMAIL>"
            };


            Device.BeginInvokeOnMainThread(() =>
            {
                //if (!groupChannelsList.Contains(messages))
                //groupChannelsList.Add(messages);
                //lstView.HeightRequest = 75;
                Color backColor = AppThemeConstants.RandomColor;

            });



            //ch.OnMessageReceived = (BaseChannel baseChannel, BaseMessage baseMessage) =>
            //{

            //    if (LocalDBManager.Instance.GetDBSetting("email") == null)
            //        return;
            //    var userMessage = (UserMessage)baseMessage;
            //    messageList.Insert(0, new DrMaxMuscle.Helpers.Messages()
            //    {
            //        Message = userMessage.Message,
            //        ProfileUrl = userMessage.Sender.ProfileUrl,
            //        Nickname = userMessage.Sender.Nickname,
            //        UserId = userMessage.Sender.UserId,
            //        CreatedAt = userMessage.CreatedAt
            //    });

            //};

            //mChannelListQuery.Next((List<OpenChannel> channels, SendBirdException ex) =>
            //{
            //    if (ex != null)
            //    {
            //        // Error.
            //        UserDialogs.Instance.HideLoading();
            //        LoadData();
            //        return;
            //    }
            //    if (messageList.Count == 0)
            //    {
            //        openChannel = channels.First();
            //        mPrevMessageListQuery = openChannel.CreatePreviousMessageListQuery();
            //        openChannel.Enter((SendBirdException exception) =>
            //        {
            //            if (exception != null)
            //            {
            //                // Error.
            //                UserDialogs.Instance.HideLoading();
            //                LoadData();
            //                return;
            //            }
            //            GetMessages();
            //        });
            //    }
            //    else
            //    {
            //        openChannel.Enter((SendBirdException exception) =>
            //        {
            //            if (exception != null)
            //            {
            //                // Error.
            //                UserDialogs.Instance.HideLoading();
            //                LoadData();
            //                return;
            //            }
            //        });
            //    }
            //});

        }
        catch (Exception ex)
        {

        }
    }





    async void Support_Tapped(object sender, System.EventArgs e)
    {
        try
        {
            Device.BeginInvokeOnMainThread(async () =>
            {
                if (!LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>"))
                {

                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    SupportPage supportPage = new SupportPage();
                    supportPage.OnBeforeShow();
                    await Navigation.PushAsync(supportPage);
                    //await PagesFactory.PushAsync<SupportPage>();

                }
                else
                {
                    //CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)e.Item).SupportChannelUrl;
                    InboxPage page = new InboxPage();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<InboxPage>();

                }
            });
        }
        catch (Exception ex)
        {

        }
    }

    async void LstView_ItemTapped(object sender, ItemTappedEventArgs e)
    {
        try
        {
            if (!LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>"))
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    CurrentLog.Instance.ChannelUrl = supportUrl;
                    SupportPage page = new SupportPage();
                    page.OnBeforeShow();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<SupportPage>();
                }
            }
            else
            {
                if (((DrMaxMuscle.Helpers.Messages)e.Item).ChatType == ChannelType.Group)
                {
                    //CurrentLog.Instance.ChannelUrl = ((DrMaxMuscle.Helpers.Messages)e.Item).SupportChannelUrl;
                    InboxPage page = new InboxPage();
                    await Navigation.PushAsync(page);
                    //await PagesFactory.PushAsync<InboxPage>();
                }
            }
        }
        catch (Exception ex)
        {

        }
    }


    private void GetMessages()
    {
        if (LocalDBManager.Instance.GetDBSetting("email") == null)
        {
            messageList.Clear();
            return;
        }
        IsLoading = true;

        //mPrevMessageListQuery.Load(25, true, (List<BaseMessage> messages, SendBirdException e) =>
        //{
        //    UserDialogs.Instance.HideLoading();


        //    if (e != null)
        //    {
        //        // Error.
        //        IsLoadMore = false;
        //        return;
        //    }
        //    if (messages.Count == 0)
        //        return;

        //    foreach (UserMessage msg in messages)
        //    {
        //        messageList.Add(new DrMaxMuscle.Helpers.Messages()
        //        {
        //            Message = msg.Message,
        //            UserId = msg.Sender.UserId,
        //            Nickname = msg.Sender.Nickname,
        //            ProfileUrl = msg.Sender.ProfileUrl,
        //            CreatedAt = msg.CreatedAt
        //        });
        //    }

        //    Device.BeginInvokeOnMainThread(() =>
        //    {

        //        lstChats.ItemsSource = messageList;
        //        if (messageList.Count < 26)
        //        {
        //            lstChats.ScrollToFirst();
        //        }
        //        //else
        //        //  lstChats.ScrollTo(messageList[messages.Count - 2], ScrollToPosition.Start, false);

        //    });
        //    IsLoading = false;
        //    IsLoadMore = false;
        //});

    }

    private async void BtnSendTapGestureRecognizer_Tapped(object sender, EventArgs ea)
    {

        if (string.IsNullOrEmpty(chatInput.MessageText.Trim()))
            return;

        chatInput.MessageText = "";
    }

    private void TxtMsg_TextChanged(object sender, TextChangedEventArgs e)
    {
        //btnSend.IsEnabled = e.NewTextValue.Length > 0 && CrossConnectivity.Current.IsConnected;
    }

    void Handle_ItemAppearing(object sender, ItemVisibilityEventArgs e)
    {
        var itemTypeObject = e.Item as DrMaxMuscle.Helpers.Messages;
        try
        {
            if (messageList.Count == 0)
                return;
            if (IsLoading == true)
                return;
            if (IsLoadMore)
                return;
            if (messageList.Last() == itemTypeObject)
            {
                IsLoadMore = true;

                GetMessages();

                //Now you are at bottom of list. Add more items to the ObservableCollection.
            }

        }
        catch (Exception ex)
        {

        }
    }

    void Handle_ItemDisappearing(object sender, ItemVisibilityEventArgs e)
    {

    }

    public void OnListTapped(object sender, ItemTappedEventArgs e)
    {
        chatInput.UnFocusEntry();
    }

    public async Task<bool> CanGoFurtherWithoughtLoader()
    {
        if (LocalDBManager.Instance.GetDBSetting("creation_date") == null)
            return false;

        BooleanModel isV1User = await DrMuscleRestClient.Instance.IsV1UserWithoutLoader();

        if (isV1User != null)
        {

            if (isV1User.Result)
                return true;
            var _drMuscleSubcription = (IDrMuscleSubcription)MauiProgram.ServiceProvider.GetService(typeof(IDrMuscleSubcription));
            if (_drMuscleSubcription.IsActiveSubscriptions())
                return true;
        }
        return false;
    }

}
