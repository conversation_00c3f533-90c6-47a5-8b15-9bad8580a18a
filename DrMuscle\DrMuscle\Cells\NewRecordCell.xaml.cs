﻿using System;
using System.Threading.Tasks;
using DrMuscle.Helpers;
using DrMuscle.Message;
using DrMuscle.Utility;
using ImageFromXamarinUI;
using Xamarin.Forms;
using Xamarin.Forms.Xaml;

namespace DrMuscle.Cells
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class NewRecordCell : ViewCell
    {
        public NewRecordCell()
        {
            InitializeComponent();
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();
            try
            {
                var botModel = (BotModel)this.BindingContext;
                if (string.IsNullOrEmpty(botModel?.Question))
                {
                    //LblAnswer.IsVisible = false;
                    LblAnswer.Text = "Congratulations!";
                }

            }
            catch (Exception ex)
            {

            }
        }

        async void BtnShareApp_Clicked(System.Object sender, System.EventArgs e)
        {
            //get the image
            var ImageStream = await SLWorkoutStats.CaptureImageAsync();
            HelperClass.ShareImage(ImageStream, "workout_summary", "share_workout_summary");
        }

        async void BtnAIChat_Clicked(System.Object sender, System.EventArgs e)
        {
            ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).CurrentPage = ((MainTabbedPage)(global::DrMuscle.App.Current.MainPage).Navigation.NavigationStack[0]).Children[2];
            await Task.Delay(300);
            Xamarin.Forms.MessagingCenter.Send<HelpWithGoalChatMessage>(new HelpWithGoalChatMessage(), "AnalyzeMyCurrentStatsAndGiveMeYourTopRecommendation");
        }
    }
}
