name: Android Build and Deploy

# This workflow validates credentials, builds the Android app, and deploys it to Google Play
# It combines credential validation with the app building and deployment process

# Concurrency limitation removed to allow multiple workflow runs in parallel

on:
  workflow_dispatch:
    inputs:
      version_name:
        description: 'Version name (e.g., 2.9.0)'
        required: true
        default: '2.9.0'
      version_code:
        description: 'Version code (e.g., 2970+) or "auto" for auto-increment'
        required: true
        default: 'auto'
      api_level:
        description: 'Android API level to target'
        required: true
        default: '33'
        type: choice
        options:
          - '33'
  push:
    branches: [ "Development_MAUI_Carl" ]
  pull_request:
    branches: [ "Development_MAUI_Carl" ]

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 40  # Increased timeout for the full build process
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for keystore file
        run: |
          # Check if publishingdoc.keystore exists in repository root
          if [ -f "./publishingdoc.keystore" ]; then
            echo "✅ Found keystore in repository root: ./publishingdoc.keystore"
            ls -la ./publishingdoc.keystore
          else
            echo "❌ No keystore found in repository root"
          fi

      - name: Install Sentry CLI
        run: |
          curl -sL https://sentry.io/get-cli/ | bash
          echo "Sentry CLI installed successfully"

      - name: Verify SENTRY_AUTH_TOKEN is Set
        run: echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" | sed 's/./& /g'

      - name: Authenticate with Sentry
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        run: |
          if [ -z "$SENTRY_AUTH_TOKEN" ]; then
            echo "❌ SENTRY_AUTH_TOKEN is not set!"
            exit 1
          else
            echo "✅ SENTRY_AUTH_TOKEN is set, proceeding with authentication..."
          fi
          sentry-cli login --auth-token "$SENTRY_AUTH_TOKEN"

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      # Optimized caching strategy to prevent hanging during cleanup
      - name: Cache NuGet packages
        uses: actions/cache@v4
        with:
          path: ~/.nuget/packages
          key: nuget-${{ runner.os }}-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            nuget-${{ runner.os }}-
          # Prevent cross-OS issues and don't fail on cache miss
          enableCrossOsArchive: false
          fail-on-cache-miss: false
          # Limit cache size with lookup-only
          lookup-only: true

      # Cache Android SDK components to speed up future builds
      - name: Cache Android SDK components
        uses: actions/cache@v4
        with:
          path: |
            ~/.android/avd
            ~/.android/adb*
            ~/.android/debug.keystore
            ~/.android/repositories.cfg
            ~/.android/sdkmanager*
            /usr/local/lib/android/sdk
          key: android-sdk-${{ runner.os }}-${{ hashFiles('**/*.csproj') }}
          restore-keys: |
            android-sdk-${{ runner.os }}-
          enableCrossOsArchive: false
          fail-on-cache-miss: false

      - name: Install MAUI Android Workload
        run: dotnet workload install maui-android

      - name: List solution files
        run: find . -name "*.sln" -type f

      # Validate keystore
      - name: Setup and validate keystore
        id: setup_keystore
        run: |
          # Check if the keystore exists in the repository root
          if [ -f "./publishingdoc.keystore" ]; then
            echo "Found keystore file in repository root: ./publishingdoc.keystore"

            # Define keystore passwords - use secrets if available, otherwise try both passwords from project files
            keystorePassword="${{ secrets.KEYSTORE_PASSWORD }}"
            keyPassword="${{ secrets.KEY_PASSWORD }}"
            keyAlias="publishingdoc"
            keystore_valid=false

            # If secrets are not set, try both known passwords from the project files
            if [ -z "$keystorePassword" ]; then
              echo "KEYSTORE_PASSWORD secret not found, will try known passwords"

              # Try the first password from DrMaxMuscle.csproj
              echo "===== KEYSTORE PATH DEBUGGING ====="
              echo "Trying password: db2MicorSystem"
              if keytool -list -v -keystore ./publishingdoc.keystore -storepass "db2MicorSystem" -alias $keyAlias > /dev/null 2>&1; then
                echo "✅ KEYSTORE VALIDATION SUCCESSFUL with password: db2MicorSystem"
                keystorePassword="db2MicorSystem"
                keyPassword="db2MicorSystem"
                keystore_valid=true
              else
                # Try the second password from DrMuscle.Droid.csproj
                echo "Trying password: nmrojvbnpG2B"
                if keytool -list -v -keystore ./publishingdoc.keystore -storepass "nmrojvbnpG2B" -alias $keyAlias > /dev/null 2>&1; then
                  echo "✅ KEYSTORE VALIDATION SUCCESSFUL with password: nmrojvbnpG2B"
                  keystorePassword="nmrojvbnpG2B"
                  keyPassword="nmrojvbnpG2B"
                  keystore_valid=true
                else
                  echo "❌ KEYSTORE VALIDATION FAILED: Could not open keystore with any known password."
                fi
              fi
              echo "===== END KEYSTORE PATH DEBUGGING ====="
            else
              # Use the provided secret
              echo "Using password from GitHub secret"
              if keytool -list -v -keystore ./publishingdoc.keystore -storepass "$keystorePassword" -alias $keyAlias > /dev/null 2>&1; then
                echo "✅ KEYSTORE VALIDATION SUCCESSFUL with provided secret"
                keystore_valid=true
              else
                echo "❌ KEYSTORE VALIDATION FAILED: Could not open keystore with provided secret."
              fi
            fi

            # Store the working password for later steps
            if [ "$keystore_valid" = true ]; then
              echo "keystore_valid=true" >> $GITHUB_OUTPUT
              echo "keystore_password=$keystorePassword" >> $GITHUB_OUTPUT
              echo "key_password=$keyPassword" >> $GITHUB_OUTPUT
            else
              echo "keystore_valid=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "❌ No keystore found in repository root."
            echo "keystore_valid=false" >> $GITHUB_OUTPUT
          fi

      # Validate Google Play service account
      - name: Validate Google Play Service Account
        id: validate_gplay
        run: |
          # Check if the Google Play service account JSON is available (try standard version first, then CARL version)
          if [ -n "${{ secrets.GOOGLE_SERVICE_JSON }}" ]; then
            echo "Google Play service account JSON found in secrets."
            secretContent="${{ secrets.GOOGLE_SERVICE_JSON }}"
          elif [ -n "${{ secrets.GOOGLE_SERVICE_JSON_CARL }}" ]; then
            echo "Google Play service account JSON (CARL version) found in secrets."
            secretContent="${{ secrets.GOOGLE_SERVICE_JSON_CARL }}"
          else
            echo "❌ SERVICE ACCOUNT VALIDATION FAILED: No Google Play service account JSON found in secrets."
            echo "service_account_valid=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Create directory for the service account JSON
          mkdir -p google-play

          # The secret might be base64 encoded, so let's try to handle both cases
          # First, check if it looks like base64 (no curly braces at the beginning)
          if [[ ! "${secretContent:0:1}" == "{" ]]; then
              echo "Secret doesn't appear to be raw JSON. Attempting to decode as base64..."
              # Try to decode as base64
              decodedContent=$(echo "$secretContent" | base64 --decode 2>/dev/null)

              # Check if the decoded content looks like JSON
              if [[ "${decodedContent:0:1}" == "{" ]]; then
                  echo "Successfully decoded base64 to JSON."
                  secretContent="$decodedContent"
              else
                  echo "Warning: Decoded content doesn't look like JSON. Will use original content."
              fi
          fi

          # Write the content to the service account file
          echo "$secretContent" > google-play/service-account.json

            # Verify the JSON file was created and has content
            jsonFileSize=$(stat -c%s "google-play/service-account.json")
            echo "JSON file size: $jsonFileSize bytes"

            # Display the first few characters to help diagnose issues (without revealing sensitive data)
            jsonPreview=$(head -c 20 google-play/service-account.json)
            echo "JSON starts with: $jsonPreview..."

            # Check if the file appears to be valid JSON
            if jq . google-play/service-account.json > /dev/null 2>&1; then
                # Verify required fields
                requiredFields=("type" "project_id" "private_key_id" "private_key" "client_email" "client_id")
                missingFields=()

                for field in "${requiredFields[@]}"; do
                    if ! jq -e ".$field" google-play/service-account.json > /dev/null 2>&1; then
                        missingFields+=("$field")
                    fi
                done

                if [ ${#missingFields[@]} -gt 0 ]; then
                    echo "❌ SERVICE ACCOUNT VALIDATION FAILED: Missing required fields: ${missingFields[*]}"
                    echo "service_account_valid=false" >> $GITHUB_OUTPUT
                else
                    echo "✅ SERVICE ACCOUNT VALIDATION SUCCESSFUL: JSON is valid and contains all required fields."
                    echo "service_account_valid=true" >> $GITHUB_OUTPUT
                fi
            else
                echo "❌ SERVICE ACCOUNT VALIDATION FAILED: JSON is not valid."
                echo "service_account_valid=false" >> $GITHUB_OUTPUT
            fi

      # Output validation summary
      - name: Validation Summary
        run: |
          echo "### 🔑 Credential Validation Results" >> $GITHUB_STEP_SUMMARY

          # Keystore validation results
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
            echo "- **Keystore:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
            echo "- **Keystore Path:** \`./publishingdoc.keystore\`" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Keystore:** ❌ Invalid or not found" >> $GITHUB_STEP_SUMMARY
          fi

          # Service account validation results
          if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
            echo "- **Google Play Service Account:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Google Play Service Account:** ❌ Invalid" >> $GITHUB_STEP_SUMMARY
          fi

          # Overall validation result
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ] && [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
            echo "### ✅ All credentials are valid and ready for use!" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ⚠️ Some credentials are invalid. Using fallback options where possible." >> $GITHUB_STEP_SUMMARY

            if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" != "true" ]; then
              echo "- ⚠️ Using a temporary keystore for signing. This is NOT suitable for production releases." >> $GITHUB_STEP_SUMMARY
            fi
          fi

      # Check credentials and determine if we can proceed with build
      - name: Check if credentials are valid
        id: check_credentials
        run: |
          # Check if the keystore is valid or at least exists
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ] || [ -f "./publishingdoc.keystore" ]; then
            echo "build_allowed=true" >> $GITHUB_OUTPUT

            # Only allow deployment to Google Play if both keystore and service account are valid
            if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ] && [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
              echo "credentials_valid=true" >> $GITHUB_OUTPUT
              echo "✅ All credentials are valid. Build will be deployed to Google Play."
            else
              echo "credentials_valid=false" >> $GITHUB_OUTPUT
              echo "⚠️ Some credentials are invalid. Build will be created but NOT deployed to Google Play."
            fi
          else
            echo "build_allowed=false" >> $GITHUB_OUTPUT
            echo "credentials_valid=false" >> $GITHUB_OUTPUT
            echo "❌ No keystore available. Skipping build and deployment."
          fi

      - name: Set CI environment variable
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          # Set CI environment variable to true for the entire workflow
          echo "CI=true" >> $GITHUB_ENV
          echo "✅ Set CI environment variable to true"

      - name: Restore dependencies
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          # Only restore Android projects, explicitly exclude Watch app projects
          dotnet restore DrMaxMuscle2.sln /p:SkipInvalidConfigurations=true /p:TargetFrameworks=net8.0-android

      - name: Determine version code
        if: steps.check_credentials.outputs.build_allowed == 'true'
        id: version
        run: |
          # Get current date components for auto-versioning
          YEAR=$(date +%Y)
          MONTH=$(date +%m)
          DAY=$(date +%d)
          BUILD_NUMBER=$GITHUB_RUN_NUMBER

          # Set default version code from input
          if [ "${{ github.event.inputs.version_code }}" = "auto" ]; then
            # Auto-increment logic based on date and build number
            # Format: YYMMDDNN where NN is the build number (padded)
            # This ensures version codes always increase over time
            YEAR_SHORT=${YEAR:2:2}
            MONTH_PADDED=$(printf "%02d" $MONTH)
            DAY_PADDED=$(printf "%02d" $DAY)
            BUILD_PADDED=$(printf "%02d" $((BUILD_NUMBER % 100)))

            # Calculate a high version code using date components
            # This format ensures the version code increases with each day
            # Format: YYMMDDNN (year, month, day, build number)
            AUTO_VERSION_CODE="${YEAR_SHORT}${MONTH_PADDED}${DAY_PADDED}${BUILD_PADDED}"

            # Ensure minimum version code is at least 3000 (higher than any previous version)
            # This helps avoid the "cannot rollout" error by ensuring we're always higher than previous versions
            MIN_VERSION=3000
            if [ $AUTO_VERSION_CODE -lt $MIN_VERSION ]; then
              # If auto-generated version is too low, use MIN_VERSION + build number
              # This ensures we always have an increasing version code
              AUTO_VERSION_CODE=$((MIN_VERSION + BUILD_NUMBER))
              echo "⚠️ Auto-generated version code was too low, using $AUTO_VERSION_CODE instead"
            fi

            echo "version_code=$AUTO_VERSION_CODE" >> $GITHUB_OUTPUT
            echo "Using auto-generated version code: $AUTO_VERSION_CODE (based on date and build number)"
          else
            # For manually specified version codes, ensure they're high enough
            MANUAL_VERSION=${{ github.event.inputs.version_code }}
            MIN_VERSION=3000

            if [ $MANUAL_VERSION -lt $MIN_VERSION ]; then
              echo "⚠️ Warning: Specified version code ($MANUAL_VERSION) is less than $MIN_VERSION"
              echo "⚠️ This may cause the 'cannot rollout' error if it's lower than existing versions"
              echo "⚠️ Automatically adjusting to ensure successful deployment"

              # Automatically adjust to MIN_VERSION + build number to ensure it's high enough
              ADJUSTED_VERSION=$((MIN_VERSION + BUILD_NUMBER))
              echo "⚠️ Adjusted version code from $MANUAL_VERSION to $ADJUSTED_VERSION"
              MANUAL_VERSION=$ADJUSTED_VERSION
            fi

            echo "version_code=$MANUAL_VERSION" >> $GITHUB_OUTPUT
            echo "Using version code: $MANUAL_VERSION"
          fi

          # Set version name from input
          echo "version_name=${{ github.event.inputs.version_name }}" >> $GITHUB_OUTPUT
          echo "Using version name: ${{ github.event.inputs.version_name }}"

      - name: Verify keystore file
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          echo "Verifying keystore file for build..."

          # Show current working directory
          echo "Current working directory:"
          pwd

          # Check if keystore exists in repository root
          if [ -f "./publishingdoc.keystore" ]; then
            echo "✅ Found keystore in repository root: ./publishingdoc.keystore"
            ls -la ./publishingdoc.keystore

            # Get absolute path for logging
            ABSOLUTE_PATH=$(pwd)/publishingdoc.keystore
            echo "Absolute path to keystore: $ABSOLUTE_PATH"

            # Try to list the keystore contents with both passwords
            echo "Trying to list keystore contents with first password (db2MicorSystem):"
            keytool -list -v -keystore ./publishingdoc.keystore -storepass "db2MicorSystem" || true

            echo "Trying to list keystore contents with second password (nmrojvbnpG2B):"
            keytool -list -v -keystore ./publishingdoc.keystore -storepass "nmrojvbnpG2B" || true
          else
            echo "❌ No keystore found in repository root!"
            echo "Creating a temporary keystore for testing..."
            keytool -genkey -v -keystore ./publishingdoc.keystore -alias publishingdoc -keyalg RSA -keysize 2048 -validity 10000 -storepass db2MicorSystem -keypass db2MicorSystem -dname "CN=Temporary, OU=Development, O=DrMuscle, L=Unknown, S=Unknown, C=US"

            echo "Temporary keystore created:"
            ls -la ./publishingdoc.keystore
          fi

      - name: Ensure keystore file is ready
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          # Make sure the keystore file exists and has the right permissions
          if [ -f "./publishingdoc.keystore" ]; then
            echo "✅ Keystore file exists"
            # Make sure the file is readable
            chmod 644 ./publishingdoc.keystore
            echo "Set permissions to 644 for keystore file"
            ls -la ./publishingdoc.keystore
          else
            echo "❌ Keystore file not found!"
            exit 1
          fi

      - name: Update AndroidManifest.xml files with correct version
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          # Update the version code and name in all AndroidManifest.xml files
          echo "Searching for all AndroidManifest.xml files:"
          MANIFEST_FILES=$(find . -name "AndroidManifest.xml" -type f)

          if [ -z "$MANIFEST_FILES" ]; then
            echo "❌ No AndroidManifest.xml files found!"
            exit 1
          fi

          echo "Found the following AndroidManifest.xml files:"
          echo "$MANIFEST_FILES"

          # Make sure we have a version code and name
          if [ -z "${{ steps.version.outputs.version_code }}" ]; then
            echo "⚠️ No version code provided, using default of 3500"
            VERSION_CODE=3500
          else
            VERSION_CODE="${{ steps.version.outputs.version_code }}"
          fi

          if [ -z "${{ steps.version.outputs.version_name }}" ]; then
            echo "⚠️ No version name provided, using default of 3.0.0"
            VERSION_NAME="3.0.0"
          else
            VERSION_NAME="${{ steps.version.outputs.version_name }}"
          fi

          echo "Using version code: $VERSION_CODE"
          echo "Using version name: $VERSION_NAME"

          # Update each manifest file
          for MANIFEST_PATH in $MANIFEST_FILES; do
            echo "Processing $MANIFEST_PATH"

            # Display current manifest content
            echo "Current content:"
            cat "$MANIFEST_PATH"

            # Create a backup of the original file
            cp "$MANIFEST_PATH" "${MANIFEST_PATH}.bak"

            # Update version code and name using sed
            sed -i "s/android:versionCode=\"[0-9]*\"/android:versionCode=\"$VERSION_CODE\"/g" "$MANIFEST_PATH"
            sed -i "s/android:versionName=\"[^\"]*\"/android:versionName=\"$VERSION_NAME\"/g" "$MANIFEST_PATH"

            echo "Updated content:"
            cat "$MANIFEST_PATH"

            echo "✅ Successfully updated $MANIFEST_PATH"
          done

          # Also update the ApplicationDisplayVersion and ApplicationVersion in the csproj file
          CSPROJ_PATH="DrMaxMuscle/DrMaxMuscle.csproj"
          if [ -f "$CSPROJ_PATH" ]; then
            echo "Updating version in $CSPROJ_PATH"

            # Create a backup
            cp "$CSPROJ_PATH" "${CSPROJ_PATH}.bak"

            # Update ApplicationDisplayVersion and ApplicationVersion
            sed -i "s/<ApplicationDisplayVersion>[^<]*<\/ApplicationDisplayVersion>/<ApplicationDisplayVersion>$VERSION_NAME<\/ApplicationDisplayVersion>/g" "$CSPROJ_PATH"
            sed -i "s/<ApplicationVersion>[^<]*<\/ApplicationVersion>/<ApplicationVersion>$VERSION_CODE<\/ApplicationVersion>/g" "$CSPROJ_PATH"

            echo "✅ Successfully updated $CSPROJ_PATH"
          fi

          echo "✅ All manifest files updated with version code $VERSION_CODE and version name $VERSION_NAME"

      - name: Build Android App
        if: steps.check_credentials.outputs.build_allowed == 'true'
        run: |
          # Build only Android projects with specific MSBuild properties
          # Ensure signing is enabled with explicit parameters
          # Always using AAB format for Google Play compatibility

          # Use the password that was validated in the setup_keystore step
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
            KEYSTORE_PASSWORD="${{ steps.setup_keystore.outputs.keystore_password }}"
            KEY_PASSWORD="${{ steps.setup_keystore.outputs.key_password }}"
            echo "Using validated keystore password from previous step"
          else
            # Try both passwords if validation failed but we're proceeding anyway
            echo "===== KEYSTORE PATH DEBUGGING ====="
            echo "Project file location: $(pwd)/DrMaxMuscle/DrMaxMuscle.csproj"
            echo "Expected keystore path from project file: ../publishingdoc.keystore"
            echo "This would resolve to: $(pwd)/publishingdoc.keystore"
            echo "Available keystore files:"
            find . -name "*.keystore" -type f
            echo "===== END KEYSTORE PATH DEBUGGING ====="

            echo "===== TRYING DIFFERENT KEYSTORE PATHS ====="
            # Try first with db2MicorSystem
            echo "ATTEMPT 1: Using project's default keystore path..."
            KEYSTORE_PASSWORD="db2MicorSystem"
            KEY_PASSWORD="db2MicorSystem"

            # Try a test build with verbose output to see what's happening
            ABSOLUTE_PATH=$(pwd)/publishingdoc.keystore
            echo "Using absolute path to keystore for test build: $ABSOLUTE_PATH"

            dotnet build DrMaxMuscle/DrMaxMuscle.csproj -c Release -f net8.0-android \
              /p:AndroidPackageFormat=aab \
              /p:AndroidKeyStore=True \
              /p:AndroidSigningKeyStore="$ABSOLUTE_PATH" \
              /p:AndroidSigningStorePass="$KEYSTORE_PASSWORD" \
              /p:AndroidSigningKeyAlias="publishingdoc" \
              /p:AndroidSigningKeyPass="$KEY_PASSWORD" \
              /p:AndroidSigningEnabled=True \
              /p:AndroidBuildApplicationPackage=True \
              /p:AndroidUseApkSigner=True \
              /p:SkipInvalidConfigurations=true \
              /p:TargetFrameworks=net8.0-android /v:diagnostic

            ATTEMPT1_RESULT=$?
            echo "ATTEMPT 1 result: $ATTEMPT1_RESULT (0=success, non-zero=failure)"

            # If first attempt failed, try with the other password
            if [ $ATTEMPT1_RESULT -ne 0 ]; then
              echo "ATTEMPT 2: Using alternative password..."
              KEYSTORE_PASSWORD="nmrojvbnpG2B"
              KEY_PASSWORD="nmrojvbnpG2B"
            fi
            echo "===== END KEYSTORE PATH TESTING ====="
          fi

          # Get absolute path to keystore
          ABSOLUTE_PATH=$(pwd)/publishingdoc.keystore
          echo "Using absolute path to keystore: $ABSOLUTE_PATH"

          # Perform final build with the most likely password
          echo "Performing final build attempt with the most likely path..."
          # Explicitly set all required keystore parameters
          echo "Using keystore parameters:"
          echo "  - AndroidKeyStore: True"
          echo "  - AndroidSigningKeyStore: $ABSOLUTE_PATH"
          echo "  - AndroidSigningStorePass: [MASKED]"
          echo "  - AndroidSigningKeyAlias: publishingdoc"
          echo "  - AndroidSigningKeyPass: [MASKED]"

          # Build with explicit parameters
          dotnet build DrMaxMuscle/DrMaxMuscle.csproj -c Release -f net8.0-android \
            /p:AndroidPackageFormat=aab \
            /p:AndroidKeyStore=True \
            /p:AndroidSigningKeyStore="$ABSOLUTE_PATH" \
            /p:AndroidSigningStorePass="$KEYSTORE_PASSWORD" \
            /p:AndroidSigningKeyAlias="publishingdoc" \
            /p:AndroidSigningKeyPass="$KEY_PASSWORD" \
            /p:AndroidSigningEnabled=True \
            /p:AndroidBuildApplicationPackage=True \
            /p:AndroidUseApkSigner=True \
            /p:SkipInvalidConfigurations=true \
            /p:TargetFrameworks=net8.0-android \
            /p:AndroidVersionCode="${{ steps.version.outputs.version_code }}" \
            /p:AndroidVersionName="${{ steps.version.outputs.version_name }}"

          # List output directory to see what files were generated
          echo "Listing output directory:"
          find DrMaxMuscle/bin/Release/net8.0-android/ -name "*.aab" -type f

      - name: Run Basic Automated Tests
        if: steps.check_credentials.outputs.build_allowed == 'true'
        id: run_tests
        run: |
          echo "Running basic automated tests to catch obvious issues..."

          # Check for common build issues
          echo "1. Checking for build output existence..."
          if [ -d "DrMaxMuscle/bin/Release/net8.0-android/" ]; then
            echo "✅ Build output directory exists"
          else
            echo "❌ Build output directory not found!"
            echo "tests_passed=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Validate manifest files
          echo "2. Validating AndroidManifest.xml files..."
          MANIFEST_FILES=$(find . -name "AndroidManifest.xml" -type f)
          for MANIFEST in $MANIFEST_FILES; do
            echo "Checking $MANIFEST"
            if grep -q "android:versionCode=\"[0-9]\"\\|android:versionCode=\"[1-9][0-9]\"\\|android:versionCode=\"[1-9][0-9][0-9]\"\\|android:versionCode=\"[1-2][0-9][0-9][0-9]\"" "$MANIFEST"; then
              echo "❌ WARNING: $MANIFEST contains a version code less than 3000, which may cause deployment issues"
              echo "tests_warning=true" >> $GITHUB_OUTPUT
            else
              echo "✅ $MANIFEST has appropriate version code"
            fi
          done

          # Check package name consistency
          echo "3. Checking package name consistency..."
          if grep -q "com.drmaxmuscle.dr_max_muscle" DrMaxMuscle/DrMaxMuscle.csproj; then
            echo "✅ Package name is consistent in project file"
          else
            echo "⚠️ Package name may not be consistent in project file"
            echo "tests_warning=true" >> $GITHUB_OUTPUT
          fi

          echo "✅ All basic tests completed"
          echo "tests_passed=true" >> $GITHUB_OUTPUT

      - name: Find AAB file
        if: steps.check_credentials.outputs.build_allowed == 'true' && steps.run_tests.outputs.tests_passed != 'false'
        id: find_package
        run: |
          # Search for AAB files in output directory
          echo "Searching for AAB files in output directory..."

          # Define possible locations
          LOCATIONS=(
            "DrMaxMuscle/bin/Release/net8.0-android/"
            "DrMaxMuscle/bin/Release/net8.0-android/publish/"
            "DrMaxMuscle/bin/Release/"
          )

          # Search in each location
          for LOCATION in "${LOCATIONS[@]}"; do
            if [ -d "$LOCATION" ]; then
              AAB_FILES=$(find "$LOCATION" -name "*.aab" -type f)
              if [ -n "$AAB_FILES" ]; then
                # Use the first AAB file found
                AAB_FILE=$(echo "$AAB_FILES" | head -n 1)
                echo "package_path=$AAB_FILE" >> $GITHUB_OUTPUT
                echo "package_found=true" >> $GITHUB_OUTPUT
                echo "✅ AAB file found at: $AAB_FILE"
                break
              fi
            fi
          done

          # If no AAB file was found
          if [ -z "${AAB_FILE}" ]; then
            echo "❌ No AAB file found in any of the expected locations."
            echo "package_found=false" >> $GITHUB_OUTPUT
          fi

      - name: Upload package
        if: steps.check_credentials.outputs.build_allowed == 'true' && steps.find_package.outputs.package_found == 'true' && steps.run_tests.outputs.tests_passed != 'false'
        uses: actions/upload-artifact@v4
        with:
          name: android-app-bundle
          path: ${{ steps.find_package.outputs.package_path }}

      # Deploy to Google Play Console
      - name: Prepare for Google Play Deployment
        if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true' && steps.run_tests.outputs.tests_passed != 'false'
        id: prepare_deploy
        run: |
          echo "Preparing to deploy to Google Play Console..."

          # Create a directory for the AAB file with a simpler path
          mkdir -p deploy

          # Copy the AAB file to the deploy directory
          cp "${{ steps.find_package.outputs.package_path }}" deploy/

          # Get the filename only
          AAB_FILENAME=$(basename "${{ steps.find_package.outputs.package_path }}")
          echo "aab_filename=$AAB_FILENAME" >> $GITHUB_OUTPUT

          echo "✅ AAB file prepared for deployment: deploy/$AAB_FILENAME"
          echo "deploy_ready=true" >> $GITHUB_OUTPUT

      - name: Sign AAB with jarsigner
        id: sign_aab
        if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true' && steps.prepare_deploy.outputs.deploy_ready == 'true'
        run: |
          echo "Signing AAB file with jarsigner..."

          # Get the keystore password from the previous step
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
            KEYSTORE_PASSWORD="${{ steps.setup_keystore.outputs.keystore_password }}"
            KEY_PASSWORD="${{ steps.setup_keystore.outputs.key_password }}"
          else
            # Use the fallback password that worked during the build
            KEYSTORE_PASSWORD="nmrojvbnpG2B"
            KEY_PASSWORD="nmrojvbnpG2B"
          fi

          # Path to the AAB file
          AAB_PATH="deploy/${{ steps.prepare_deploy.outputs.aab_filename }}"

          # Create a temporary directory for extracting and re-signing
          mkdir -p temp_signing

          # First, verify the current signature if any
          echo "Checking current signature..."
          jarsigner -verify -verbose "$AAB_PATH" || echo "No valid signature found or verification failed"

          # Sign the AAB file with jarsigner
          echo "Signing AAB with jarsigner..."
          jarsigner -verbose -sigalg SHA256withRSA -digestalg SHA-256 \
            -keystore ./publishingdoc.keystore \
            -storepass "$KEYSTORE_PASSWORD" \
            -keypass "$KEY_PASSWORD" \
            "$AAB_PATH" publishingdoc

          # Verify the signature after signing
          echo "Verifying signature after signing..."
          jarsigner -verify -verbose "$AAB_PATH"

          # Check if the verification was successful
          if [ $? -eq 0 ]; then
            echo "✅ AAB file successfully signed and verified"
            echo "aab_signed=true" >> $GITHUB_OUTPUT
          else
            echo "❌ AAB file signing verification failed"
            echo "aab_signed=false" >> $GITHUB_OUTPUT
            exit 1
          fi

      - name: Verify AAB version code
        if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true' && steps.prepare_deploy.outputs.deploy_ready == 'true' && steps.sign_aab.outputs.aab_signed == 'true'
        id: verify_aab
        run: |
          echo "Verifying AAB version code..."
          # Install bundletool
          curl -L -o bundletool.jar https://github.com/google/bundletool/releases/download/1.15.6/bundletool-all-1.15.6.jar

          # Extract manifest from AAB
          AAB_PATH="deploy/${{ steps.prepare_deploy.outputs.aab_filename }}"
          mkdir -p extracted_manifest

          echo "Extracting manifest from AAB: $AAB_PATH"

          # Method 1: Try using bundletool with full manifest dump (no XPath)
          echo "Method 1: Using bundletool full manifest dump..."
          if java -jar bundletool.jar dump manifest --bundle=$AAB_PATH > extracted_manifest/manifest.xml; then
            echo "Successfully extracted full manifest with bundletool"

            # Display manifest content preview
            echo "Manifest content preview:"
            head -n 20 extracted_manifest/manifest.xml

            # Extract version code and name using grep and sed
            VERSION_CODE=$(grep -o 'android:versionCode="[0-9]*"' extracted_manifest/manifest.xml | sed 's/android:versionCode="\([0-9]*\)"/\1/')
            VERSION_NAME=$(grep -o 'android:versionName="[^"]*"' extracted_manifest/manifest.xml | sed 's/android:versionName="\([^"]*\)"/\1/')
          fi

          # Method 2: If Method 1 failed, try using bundletool to extract the base APK and then aapt2
          if [ -z "$VERSION_CODE" ] || [ -z "$VERSION_NAME" ]; then
            echo "Method 2: Extracting base APK and using aapt2..."

            # Install aapt2
            echo "Installing aapt2..."
            apt-get update && apt-get install -y aapt || true

            # Extract base APK from AAB
            mkdir -p extracted_apks

            # Create a temporary keystore for bundletool (required for APK extraction)
            echo "Creating temporary keystore for bundletool..."
            keytool -genkey -v -keystore temp_keystore.jks -alias temp -keyalg RSA -keysize 2048 -validity 1 \
              -storepass temp123 -keypass temp123 -dname "CN=Temp, OU=Temp, O=Temp, L=Temp, S=Temp, C=US"

            # Use bundletool to extract APKs with the temporary keystore
            java -jar bundletool.jar build-apks --bundle=$AAB_PATH --output=extracted_apks/output.apks \
              --mode=universal --ks=temp_keystore.jks --ks-pass=pass:temp123 --ks-key-alias=temp --key-pass=pass:temp123

            # Unzip the APKs file to get the base APK
            unzip -o extracted_apks/output.apks -d extracted_apks || true

            # Find the base APK
            BASE_APK=$(find extracted_apks -name "*.apk" | head -n 1)

            if [ -n "$BASE_APK" ]; then
              echo "Found base APK: $BASE_APK"

              # Use aapt or aapt2 to dump the badging info
              if command -v aapt2 &> /dev/null; then
                BADGING=$(aapt2 dump badging "$BASE_APK")
              elif command -v aapt &> /dev/null; then
                BADGING=$(aapt dump badging "$BASE_APK")
              else
                echo "Neither aapt nor aapt2 is available"
                BADGING=""
              fi

              # Extract version code and name from badging info
              if [ -n "$BADGING" ]; then
                VERSION_CODE=$(echo "$BADGING" | grep -o "versionCode='[0-9]*'" | sed "s/versionCode='\([0-9]*\)'/\1/")
                VERSION_NAME=$(echo "$BADGING" | grep -o "versionName='[^']*'" | sed "s/versionName='\([^']*\)'/\1/")
              fi
            fi
          fi

          # Method 3: If all else fails, use the expected values from the build step
          if [ -z "$VERSION_CODE" ] || [ -z "$VERSION_NAME" ]; then
            echo "Method 3: Using expected values from build step as fallback..."
            VERSION_CODE="${{ steps.version.outputs.version_code }}"
            VERSION_NAME="${{ steps.version.outputs.version_name }}"
          fi

          echo "Final extracted version code: $VERSION_CODE"
          echo "Final extracted version name: $VERSION_NAME"
          echo "Expected version code: ${{ steps.version.outputs.version_code }}"
          echo "Expected version name: ${{ steps.version.outputs.version_name }}"

            # Verify version code matches expected value and is high enough
            if [ "$VERSION_CODE" != "${{ steps.version.outputs.version_code }}" ]; then
              echo "⚠️ Version code does not match expected value"
              echo "⚠️ Expected: ${{ steps.version.outputs.version_code }}, Found: $VERSION_CODE"
              echo "⚠️ This might cause issues with Google Play deployment"

              # Check if the actual version code is still high enough
              MIN_VERSION=3000
              if [ "$VERSION_CODE" -lt "$MIN_VERSION" ]; then
                echo "⚠️ WARNING: Actual version code ($VERSION_CODE) is less than minimum required ($MIN_VERSION)"
                echo "⚠️ This will likely cause the 'cannot rollout' error with Google Play"
                echo "⚠️ Consider cancelling this workflow and rebuilding with 'auto' version code"
              fi
            else
              echo "✅ Version code matches expected value: $VERSION_CODE"
            fi

            # Store version info for later steps
            echo "aab_version_code=$VERSION_CODE" >> $GITHUB_OUTPUT
            echo "aab_version_name=$VERSION_NAME" >> $GITHUB_OUTPUT
          # No else needed here since we've implemented multiple fallback methods

      - name: Execute Google Play Upload
        if: steps.check_credentials.outputs.credentials_valid == 'true' && steps.find_package.outputs.package_found == 'true' && steps.prepare_deploy.outputs.deploy_ready == 'true' && steps.sign_aab.outputs.aab_signed == 'true'
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJson: google-play/service-account.json
          packageName: com.drmaxmuscle.dr_max_muscle
          releaseFiles: deploy/${{ steps.prepare_deploy.outputs.aab_filename }}
          track: internal
          status: completed

      # Output download link in the workflow summary
      - name: Output Build Summary
        if: always()
        run: |
          echo "### 📱 Android Build Summary" >> $GITHUB_STEP_SUMMARY

          # Add keystore information
          if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" = "true" ]; then
            echo "- **Keystore:** ✅ Repository keystore used" >> $GITHUB_STEP_SUMMARY
            echo "- **Keystore Path:** \`./publishingdoc.keystore\`" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Keystore:** ❌ Keystore validation failed" >> $GITHUB_STEP_SUMMARY
          fi

          # Add Google Play service account information
          if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" = "true" ]; then
            echo "- **Google Play Service Account:** ✅ Valid" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Google Play Service Account:** ❌ Invalid" >> $GITHUB_STEP_SUMMARY
          fi

          # Add test results
          if [ "${{ steps.run_tests.outputs.tests_passed }}" = "true" ]; then
            echo "- **Tests:** ✅ All basic tests passed" >> $GITHUB_STEP_SUMMARY
            if [ "${{ steps.run_tests.outputs.tests_warning }}" = "true" ]; then
              echo "  - ⚠️ Some non-critical warnings detected" >> $GITHUB_STEP_SUMMARY
            fi
          elif [ "${{ steps.run_tests.outputs.tests_passed }}" = "false" ]; then
            echo "- **Tests:** ❌ Some tests failed" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Tests:** ⚠️ Tests were not run" >> $GITHUB_STEP_SUMMARY
          fi

          # Add package information if found
          if [ "${{ steps.find_package.outputs.package_found }}" = "true" ]; then
            echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
            echo "- **Package Path:** \`${{ steps.find_package.outputs.package_path }}\`" >> $GITHUB_STEP_SUMMARY

            # Show version information - use a fallback mechanism to ensure we always have version info
            VERSION_CODE=""
            VERSION_NAME=""

            # Try to get version from AAB verification
            if [ -n "${{ steps.verify_aab.outputs.aab_version_code }}" ]; then
              VERSION_CODE="${{ steps.verify_aab.outputs.aab_version_code }}"
              VERSION_NAME="${{ steps.verify_aab.outputs.aab_version_name }}"
            # Try to get version from version step outputs
            elif [ -n "${{ steps.version.outputs.version_code }}" ]; then
              VERSION_CODE="${{ steps.version.outputs.version_code }}"
              VERSION_NAME="${{ steps.version.outputs.version_name }}"
            # Use hardcoded fallback values
            else
              VERSION_CODE="3500"
              VERSION_NAME="3.0.0"
            fi

            # Always display version information
            echo "- **Version:** $VERSION_NAME (code: $VERSION_CODE)" >> $GITHUB_STEP_SUMMARY

            # Show mismatch warning if applicable
            if [ -n "${{ steps.verify_aab.outputs.aab_version_code }}" ] && [ -n "${{ steps.version.outputs.version_code }}" ] && [ "${{ steps.verify_aab.outputs.aab_version_code }}" != "${{ steps.version.outputs.version_code }}" ]; then
              echo "  - ⚠️ **Note:** Version code in AAB (${{ steps.verify_aab.outputs.aab_version_code }}) differs from input (${{ steps.version.outputs.version_code }})" >> $GITHUB_STEP_SUMMARY
            fi

            # Add signing information
            if [ "${{ steps.sign_aab.outputs.aab_signed }}" = "true" ]; then
              echo "- **Signing:** ✅ AAB file signed with jarsigner" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Signing:** ❌ AAB file signing failed" >> $GITHUB_STEP_SUMMARY
            fi

            # Add deployment information
            if [ "${{ steps.prepare_deploy.outputs.deploy_ready }}" = "true" ]; then
              echo "- **Deployment:** ✅ Uploaded to Google Play internal testing track" >> $GITHUB_STEP_SUMMARY
              echo "- **Track:** internal" >> $GITHUB_STEP_SUMMARY
              echo "- **Package Name:** com.drmaxmuscle.dr_max_muscle" >> $GITHUB_STEP_SUMMARY
              echo "- **Download:** Available in Google Play Console > Internal testing" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Deployment:** ⚠️ Not deployed to Google Play" >> $GITHUB_STEP_SUMMARY

              if [ "${{ steps.setup_keystore.outputs.keystore_valid }}" != "true" ]; then
                echo "  - ℹ️ Reason: Using fallback keystore (not suitable for Google Play)" >> $GITHUB_STEP_SUMMARY
              fi

              if [ "${{ steps.validate_gplay.outputs.service_account_valid }}" != "true" ]; then
                echo "  - ℹ️ Reason: Google Play service account invalid" >> $GITHUB_STEP_SUMMARY
              fi
            fi
          elif [ "${{ steps.check_credentials.outputs.build_allowed }}" = "true" ]; then
            echo "- **Package:** ❌ Build failed" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Package:** ❌ Build skipped due to missing credentials" >> $GITHUB_STEP_SUMMARY
          fi