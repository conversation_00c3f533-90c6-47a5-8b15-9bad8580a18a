<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:converters="clr-namespace:DrMaxMuscle.Convertors"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
             x:Class="DrMaxMuscle.Cells.SetsCell">
    <Grid BackgroundColor="Transparent">
        <Frame
            Padding="15,10,15,0"
            Margin="4,0,4,0"
            IsClippedToBounds="true"
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand"
            Style="{StaticResource GradientFrameStyleBlue}"
            CornerRadius="0">

        </Frame>
        <StackLayout VerticalOptions="FillAndExpand" Spacing="1" Padding="15,10,15,0"
                         Margin="4,0,4,0">
            <StackLayout.Resources>
                <converters:BoolInverter
                        x:Key="BoolInverterConverter" />
            </StackLayout.Resources>
            <ffimageloading:CachedImage x:Name="videoPlayer" Source="{Binding VideoUrl}" ErrorPlaceholder="backgroundblack.png" HeightRequest="200" Aspect="AspectFit"  DownsampleToViewSize="True" HorizontalOptions="FillAndExpand" BackgroundColor="White" IsVisible="{Binding IsVideoUrlAvailable}">
                <ffimageloading:CachedImage.GestureRecognizers>
                    <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_1"/>
                </ffimageloading:CachedImage.GestureRecognizers>
            </ffimageloading:CachedImage>
            <Grid IsVisible="{Binding IsHeaderCell}" ColumnSpacing="0" Padding="0,10,0,11">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="25"  />
                    <ColumnDefinition Width="60"  />
                    <ColumnDefinition Width="0.77*" />
                    <ColumnDefinition Width="25" />
                    <ColumnDefinition Width="0.77*" />

                </Grid.ColumnDefinitions>
                <Label Text="SET" Grid.Column="1" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand" HorizontalTextAlignment="Center"/>
                <Label Text="REPS" x:Name="repsTypeLabel"  Grid.Column="2" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand"  HorizontalTextAlignment="Center"/>
                <Label Text="LBS" x:Name="massUnitLabel" Grid.Column="4" FontSize="18" TextColor="#FFFFFF" FontAttributes="Bold" HorizontalOptions="FillAndExpand"  HorizontalTextAlignment="Center"/>
            </Grid>


            <Grid
                        
                        IsClippedToBounds="True"
                        ColumnSpacing="0"
                        RowSpacing="0"
                        VerticalOptions="FillAndExpand"
                        HorizontalOptions="FillAndExpand">

                <Grid.ColumnDefinitions>
                    <ColumnDefinition
                                Width="25" />
                    <ColumnDefinition
                                Width="60" />
                    <ColumnDefinition
                                Width="0.77*" />
                    <ColumnDefinition
                                Width="25" />
                    <ColumnDefinition
                                Width="0.77*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition
                                Height="*" />

                </Grid.RowDefinitions>

                <ffimageloading:CachedImage
                            Source="done2.png"
                            Margin="0,5,0,5"
                            HeightRequest="20"
                            WidthRequest="20"
                            Aspect="AspectFit"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            IsVisible="{Binding IsFinished}" />
                <ffimageloading:CachedImage
                            Source="deleteset.png"
                            Margin="0,5,0,5"
                            HeightRequest="20"
                            WidthRequest="20"
                            Aspect="AspectFit"
                    ErrorPlaceholder="backgroundblack.png"
                            Grid.Row="0"
                            Grid.Column="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                </ffimageloading:CachedImage>
                <t:DrMuscleButton
                            Margin="0"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="Transparent"
                            Clicked="DeleteSetTapGestureRecognizer_Tapped"
                            IsVisible="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}" >
                </t:DrMuscleButton>
                <Label
                            Text="{Binding SetNo}"
                            Grid.Row="0"
                            Grid.Column="1"
                            VerticalOptions="Center"
                            VerticalTextAlignment="Center"
                            HorizontalOptions="FillAndExpand"
                            HorizontalTextAlignment="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF" />
                <!--Reps for normal exercise-->
                <Grid Grid.Column="2" Grid.Row="0">
                    <Grid.Triggers>
                        <DataTrigger
                                    TargetType="Grid"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                            <Setter
                                        Property="IsVisible"
                                        Value="False" />
                        </DataTrigger>

                    </Grid.Triggers>

                    <t:WorkoutEntry
                                Margin="10,0" 
                                Text="{Binding Reps}"
                                x:Name="RepsEntry"  
                                HorizontalTextAlignment="Center"
                                VerticalOptions="Center"
                                Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                                FontSize="21"
                                MaxLength="4"
                                TextChanged="RepsEntry_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                TextColor="#AAFFFFFF" >
                        <!--<t:WorkoutEntry.Triggers>
                                <MultiTrigger
                                    TargetType="t:WorkoutEntry">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </MultiTrigger>
                            </t:WorkoutEntry.Triggers>-->
                    </t:WorkoutEntry>

                    <Label

                            
                                        Text="Max"
                                HorizontalTextAlignment="Center"
                                VerticalOptions="Center"
                            HorizontalOptions="FillAndExpand"
                                FontSize="21"
                                        IsVisible="false"
                                TextColor="#AAFFFFFF" 
                                        >
                        <!--<Label.Triggers>
                               
                                <MultiTrigger
                                    TargetType="Label">
                                    <MultiTrigger.Conditions>
                                        <BindingCondition
                                            Binding="{Binding IsMaxChallenge}"
                                            Value="True" />
                                        <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                                    </MultiTrigger.Conditions>
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </MultiTrigger>
                            </Label.Triggers>-->
                    </Label>
                </Grid>
                <!--Reps for cardio-->
                <Grid Grid.Column="2" Grid.Row="0" IsVisible="false" Margin="10,3,10,4">
                    <Grid.Triggers>
                        <DataTrigger
                                    TargetType="Grid"
                                    Binding="{Binding BodypartId}"
                                    Value="12">
                            <Setter
                                        Property="IsVisible"
                                        Value="True" />
                        </DataTrigger>

                    </Grid.Triggers>
                    <t:WorkoutEntry
                            Text="{Binding RepsCardio}"
                            x:Name="RepsCardioEntry"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            MaxLength="6"
                            TextChanged="RepsEntry_TextChanged"
                            BackgroundColor="{Binding BackColor}"
                            TextColor="#AAFFFFFF">
                        <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter
                                        Property="IsVisible"
                                        Value="false" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                    </t:WorkoutEntry>

                    <t:WorkoutEntry
                            Text="Max"
                             x:Name="RepsCardioMax"
                            HorizontalTextAlignment="Center"
                            VerticalOptions="Center"
                            Keyboard="Numeric"
                            IsVisible="false"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            TextChanged="RepsEntry_TextChanged"
                            BackgroundColor="{Binding BackColor}"
                            TextColor="#AAFFFFFF">
                        <!--<t:WorkoutEntry.Triggers>
                                <DataTrigger
                                    TargetType="t:WorkoutEntry"
                                    Binding="{Binding IsMaxChallenge}"
                                    Value="true">
                                    <Setter Property="IsReadOnly" Value="True" />
                                    <Setter
                                        Property="IsVisible"
                                        Value="true" />
                                </DataTrigger>
                            </t:WorkoutEntry.Triggers>-->
                    </t:WorkoutEntry>
                    <Label
                            HorizontalTextAlignment="Center"
                            VerticalOptions="FillAndExpand"
                            HorizontalOptions="FillAndExpand"
                            FontSize="21"
                            BackgroundColor="Transparent"
                            >
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Tapped="TapGestureRecognizer_Tapped_2" />
                        </Label.GestureRecognizers>
                    </Label>
                </Grid>
                <Label
                            Text="*"
                            Grid.Row="0"
                            Margin="0,2,0,0"
                            Grid.Column="3"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            FontSize="21"
                            TextColor="#AAFFFFFF" />


                <t:WorkoutEntry
                                x:Name="WeightEntry"
                                Grid.Row="0"
                            Grid.Column="4"
                            Margin="10,3,10,4"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                                Text="{Binding WeightSingal}"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="Center"
                                TextChanged="WeightEntry_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                FontSize="21"
                                TextColor="#AAFFFFFF">
                    <t:WorkoutEntry.Triggers>
                        <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding IsBodyweight}"
                                                Value="true">
                            <Setter Property="IsReadOnly" Value="True" />
                        </DataTrigger>
                        <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding BodypartId}"
                                                Value="12">
                            <Setter Property="IsVisible" Value="False" />
                        </DataTrigger>
                    </t:WorkoutEntry.Triggers>
                </t:WorkoutEntry>
                <t:WorkoutEntry
                                x:Name="SpeedEntryNormal"
                                Grid.Row="0"
                            Grid.Column="4"
                            Margin="10,3,10,4"
                            Keyboard="Numeric"
                            VerticalTextAlignment="Center"
                            VerticalOptions="Center"
                            IsVisible="false"
                                Text="{Binding Speed}"
                                HorizontalOptions="FillAndExpand"
                                HorizontalTextAlignment="Center"
                                TextChanged="SpeedEntryNormal_TextChanged"
                                BackgroundColor="{Binding BackColor}"
                                IsReadOnly="{Binding IsFinished, Converter={StaticResource BoolInverterConverter}}"
                                FontSize="21"
                                TextColor="#AAFFFFFF">
                    <t:WorkoutEntry.Triggers>
                        <DataTrigger
                                                TargetType="t:WorkoutEntry"
                                                Binding="{Binding BodypartId}"
                                                Value="12">
                            <Setter Property="IsVisible" Value="True" />
                        </DataTrigger>
                    </t:WorkoutEntry.Triggers>
                </t:WorkoutEntry>

            </Grid>
            <Frame Padding="10"  
                          
                            Margin="0,20,0,5" HorizontalOptions="FillAndExpand" HasShadow="False" BackgroundColor="{Binding BackColor}" CornerRadius="6" IsVisible="false">
                <Frame.Triggers>
                    <MultiTrigger
                                TargetType="Frame">
                        <MultiTrigger.Conditions>
                            <BindingCondition
                                        Binding="{Binding IsLastSet}"
                                        Value="True" />
                            <BindingCondition
                                        Binding="{Binding IsFinished}"
                                        Value="True" />
                            <BindingCondition
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="False" />
                        </MultiTrigger.Conditions>
                        <Setter
                                    Property="IsVisible"
                                    Value="true" />
                    </MultiTrigger>

                </Frame.Triggers>
                <Label
                           
                            
                            
                            Text="All sets done—congrats!"
                            HorizontalOptions="Center"
                            HorizontalTextAlignment="Center"
                            FontAttributes="Italic"
                            FontSize="21"
                            TextColor="White">

                </Label>
            </Frame>
            <Frame
                            x:Name="FinishExercise"
                            
                            Margin="3,10,3,0"
                            IsVisible="false"
                            IsClippedToBounds="true"
                            CornerRadius="6"
                            HorizontalOptions="FillAndExpand"
                            Style="{StaticResource GradientFrameStyleGreen}"
                            HeightRequest="66">
                <t:DrMuscleButton
                                Text="Finish exercise"
                                TextColor="#0C2432"
                                Clicked="FinishedExercise_Clicked"
                                CommandParameter="{Binding .}"
                                BackgroundColor="Transparent"
                                FontSize="21"
                                FontAttributes="Bold">
                    <t:DrMuscleButton.Triggers>

                        <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsFirstSide}"
                                        Value="true">
                            <Setter
                                            Property="Text"
                                            Value="Finish side 1" />
                        </DataTrigger>
                        <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsFirstSide}"
                                        Value="false">
                            <Setter
                                            Property="Text"
                                            Value="Finish exercise" />
                        </DataTrigger>
                        <DataTrigger
                                        TargetType="t:DrMuscleButton"
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="true">
                            <Setter
                                            Property="Text"
                                            Value="Save" />
                        </DataTrigger>
                    </t:DrMuscleButton.Triggers>
                </t:DrMuscleButton>
                <Frame.Triggers>
                    <MultiTrigger
                                    TargetType="Frame">
                        <MultiTrigger.Conditions>
                            <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                            <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                        </MultiTrigger.Conditions>
                        <Setter
                                        Property="IsVisible"
                                        Value="true" />
                    </MultiTrigger>
                    <DataTrigger
                                        TargetType="Frame"
                                        Binding="{Binding IsExerciseFinished}"
                                        Value="true">
                        <Setter Property="Margin" Value="3,10,3,20" />
                    </DataTrigger>
                </Frame.Triggers>
            </Frame>
            <Grid
                            
                            
                            ColumnSpacing="8"
                            Margin="0,10,0,20"
                            HorizontalOptions="FillAndExpand"
                             HeightRequest="60" IsVisible="False">
                <Grid.Triggers>
                    <MultiTrigger
                                    TargetType="Grid">
                        <MultiTrigger.Conditions>
                            <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                            <BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="False" />
                        </MultiTrigger.Conditions>
                        <Setter
                                        Property="IsVisible"
                                        Value="true" />
                    </MultiTrigger>
                    <MultiTrigger
                                    TargetType="Grid">
                        <MultiTrigger.Conditions>
                            <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                            <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />
                        </MultiTrigger.Conditions>
                        <Setter
                                        Property="Margin"
                                        Value="0,10,0,20" />
                    </MultiTrigger>
                </Grid.Triggers>
                <t:DrMuscleButton
                            Grid.Column="0"
                            IsVisible="true"
                            Text="Add set"
                            TextColor="White"
                            Clicked="AddSet_Clicked"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                            BorderWidth="1"
                            CornerRadius="6"
                            Margin="3,0,3,0">
                    <t:DrMuscleButton.Triggers>
                        <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="True" />

                            </MultiTrigger.Conditions>
                            <Setter
                                        Property="Grid.ColumnSpan"
                                        Value="2" />


                        </MultiTrigger>

                    </t:DrMuscleButton.Triggers>
                </t:DrMuscleButton>
                <t:DrMuscleButton
                            Grid.Column="1"
                            IsVisible="false"
                            Text="Skip exercise"
                            Margin="3,0,3,0"
                            TextColor="White"
                            Clicked="SkipExercise_Clicked"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                            BorderWidth="1"
                            CornerRadius="6"
                            >
                    <t:DrMuscleButton.Triggers>
                        <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="False" />
                                <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter
                                        Property="IsVisible"
                                        Value="true" />
                            <Setter Property="FlexLayout.Grow" Value="1" />
                        </MultiTrigger>

                    </t:DrMuscleButton.Triggers>

                </t:DrMuscleButton>
                <t:DrMuscleButton
                            Grid.Column="1"
                            IsVisible="false"
                            Text="Finish exercise"
                            TextColor="White"
                            Clicked="UnFinishedExercise_Clicked1"
                            CommandParameter="{Binding .}"
                            BackgroundColor="Transparent"
                            HeightRequest="60"
                            BorderColor="#ECFF92"
                             Margin="3,0,3,0"
                            BorderWidth="1"
                            CornerRadius="6">
                    <t:DrMuscleButton.Triggers>
                        <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="false" />
                                <BindingCondition
                                            Binding="{Binding IsExerciseFinished}"
                                            Value="True" />

                            </MultiTrigger.Conditions>
                            <Setter
                                        Property="Text"
                                        Value="Save" />
                        </MultiTrigger>
                        <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSide}"
                                            Value="False" />
                            </MultiTrigger.Conditions>
                            <Setter
                                        Property="IsVisible"
                                        Value="true" />
                            <Setter
                                        Property="Text"
                                        Value="Finish exercise" />
                            <Setter Property="FlexLayout.Grow" Value="1" />

                        </MultiTrigger>
                        <MultiTrigger
                                    TargetType="t:DrMuscleButton">
                            <MultiTrigger.Conditions>
                                <BindingCondition
                                            Binding="{Binding IsLastSet}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSetFinished}"
                                            Value="True" />
                                <BindingCondition
                                            Binding="{Binding IsFinished}"
                                            Value="False" />
                                <BindingCondition
                                            Binding="{Binding IsFirstSide}"
                                            Value="True" />
                            </MultiTrigger.Conditions>
                            <Setter
                                        Property="IsVisible"
                                        Value="true" />
                            <Setter Property="FlexLayout.Grow" Value="1" />
                            <Setter
                                        Property="Text"
                                        Value="Finish side 1" />

                        </MultiTrigger>
                    </t:DrMuscleButton.Triggers>

                </t:DrMuscleButton>


            </Grid>

        </StackLayout>
    </Grid>
</ContentView>
