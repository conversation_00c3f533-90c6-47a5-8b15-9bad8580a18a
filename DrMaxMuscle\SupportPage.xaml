﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.SupportPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
                xmlns:local="clr-namespace:DrMaxMuscle.Cells"
                xmlns:controls="clr-namespace:DrMaxMuscle.Controls"
             Title="SupportPage">
    <ContentPage.Resources>
        <ResourceDictionary>
            <local:ChatDataTemplateSelector
        x:Key="MessageTemplateSelector">
            </local:ChatDataTemplateSelector>
        </ResourceDictionary>
    </ContentPage.Resources>
    <ContentPage.Content>
        <Grid
    x:Name="mainGrid"
    BackgroundColor="#f4f4f4"
    AbsoluteLayout.LayoutFlags="All"
    AbsoluteLayout.LayoutBounds="0, 0, 1, 1"
    HorizontalOptions="FillAndExpand"
    VerticalOptions="FillAndExpand"
    RowSpacing="1"
    Padding="2,2,2,0">
            <Grid.RowDefinitions>
                <RowDefinition
            Height="*" />
                <RowDefinition
            Height="1" />
                <RowDefinition
            Height="auto" />
            </Grid.RowDefinitions>
            <StackLayout
        HorizontalOptions="FillAndExpand"
        Orientation="Vertical"
        Grid.Row="0">
                <StackLayout x:Name="StackInfo" IsVisible="true" Padding="8,5,0,2" Spacing="2">
                    <!--<Label x:Name="LblEmail" FontAttributes="Bold" HorizontalOptions="FillAndExpand" TextColor="White" FontSize="Default" />
        <BoxView x:Name="BoxBorder" IsVisible="false" HeightRequest="0.5" BackgroundColor="LightGray" />-->
                </StackLayout>
                <CollectionView
                    Rotation="180"
                BackgroundColor="Transparent"
                ItemTemplate="{StaticResource MessageTemplateSelector}"
                ItemsSource="{Binding messageList}"
                Margin="0"
                FlowDirection="RightToLeft"
                x:Name="lstChats"
                VerticalOptions="FillAndExpand"
                SelectionMode="None"
    >

                </CollectionView>
                <!--<controls:ExtendedListView
        Rotation="180"
        BackgroundColor="Transparent"
        ItemTemplate="{StaticResource MessageTemplateSelector}"
        ItemsSource="{Binding messageList}"
        Margin="0"
        ItemTapped="OnListTapped"
        FlowDirection="RightToLeft"
        HasUnevenRows="True"
        x:Name="lstChats"
        VerticalOptions="FillAndExpand"
        SeparatorColor="Transparent"
        ItemAppearing="Handle_ItemAppearing"
        ItemDisappearing="Handle_ItemDisappearing">
                </controls:ExtendedListView>-->
            </StackLayout>
            <BoxView
        IsVisible="false"
        HorizontalOptions="FillAndExpand"
        HeightRequest="1"
        BackgroundColor="LightGray"
        Grid.Row="1" />
            <controls:ChatInputBarView
        Grid.Row="2"
        Margin="{OnPlatform Android='5,9,5,20',iOS='0,11,0,10'}"
        Tapped="BtnSendTapGestureRecognizer_Tapped"
        x:Name="chatInput" />
        </Grid>
    </ContentPage.Content>
</ContentPage>