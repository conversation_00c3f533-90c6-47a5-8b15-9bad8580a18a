namespace DrMaxMuscle.Cells;

public partial class LoadingPlaceholder : ContentView
{
	public LoadingPlaceholder()
	{
		InitializeComponent();
        //StartSkeletonAnimation();
    }
    private async void StartSkeletonAnimation()
    {
        try
        {
            while (true)
            {
                await profile.FadeTo(0.5, 200);
                await title.FadeTo(0.5, 200);
                await line1.FadeTo(0.5, 200);

                await line2.FadeTo(0.5, 200);
                await line3.FadeTo(0.5, 200);
                await btn1.FadeTo(0.5, 200);
                await btn2.FadeTo(0.5, 200);

                await profile.FadeTo(1, 200);
                await title.FadeTo(1, 200);
                await line1.FadeTo(1, 200);
                await line2.FadeTo(1, 200);
                await line3.FadeTo(1, 200);
                await btn1.FadeTo(1, 200);
                await btn2.FadeTo(1, 200);
            }
        }
        catch (Exception ex)
        {

        }
    }
}