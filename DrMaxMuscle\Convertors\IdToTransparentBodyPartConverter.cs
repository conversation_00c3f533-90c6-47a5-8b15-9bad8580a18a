﻿using DrMaxMuscle.Constants;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle.Convertors
{
    public class IdToTransparentBodyPartConverter : IValueConverter
    {

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value == null)
                    return "undefined_transparent.png";
                var content = (long)value;
                if (content == 14)
                    return "lower_back_transparent.png";
                if (content == 13)//
                    return "flexibility_transparent.png";
                return $"{AppThemeConstants.GetBodyPartName(content)}_transparent.png";
            }
            catch (Exception ex)
            {

            }
            return "";


        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value;
        }
    }
}
