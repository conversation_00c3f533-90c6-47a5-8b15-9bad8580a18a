<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="DrMaxMuscle.Screens.Workouts.PinLockPage"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             Title="PinLockPage">
    <StackLayout
    VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" BackgroundColor="White">
        <StackLayout
        Spacing="10"
        VerticalOptions="CenterAndExpand">
            <Label
            x:Name="LblUnlockCode" Style="{StaticResource LabelStyle}" FontSize="Medium" HorizontalOptions="CenterAndExpand"/>
            <Grid HorizontalOptions="CenterAndExpand" ColumnSpacing="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50" />
                    <ColumnDefinition Width="50" />
                    <!--<ColumnDefinition Width="50" />-->
                </Grid.ColumnDefinitions>
                <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Grid.Row="0" Grid.Column="0" Spacing="1">
                    <Label x:Name="Lbl0" Style="{StaticResource PasscodeLabelStyle}" />
                    <Label x:Name="Lbl1" Style="{StaticResource PasscodeLabelStyle}" />
                    <Label x:Name="Lbl2" Style="{StaticResource PasscodeLabelStyle}" />
                </StackLayout>
                <StackLayout Orientation="Horizontal" HorizontalOptions="Center" Grid.Row="0" Grid.Column="1" Spacing="1">
                    <Label x:Name="Lbl3" Style="{StaticResource PasscodeLabelStyle}" />
                    <Label x:Name="Lbl4" Style="{StaticResource PasscodeLabelStyle}" />
                    <Label x:Name="Lbl5" Style="{StaticResource PasscodeLabelStyle}" />
                </StackLayout>
                <!--<StackLayout Orientation="Horizontal" HorizontalOptions="Center" Grid.Row="0" Grid.Column="2" Spacing="1">
                <Label x:Name="Lbl6" Style="{StaticResource PasscodeLabelStyle}" />
                <Label x:Name="Lbl7" Style="{StaticResource PasscodeLabelStyle}" />
                <Label x:Name="Lbl8" Style="{StaticResource PasscodeLabelStyle}" />
            </StackLayout>-->
            </Grid>
            <Label
            x:Name="LblInvalidCode" IsVisible="false" TextColor="Red" Style="{StaticResource LabelStyle}" FontSize="Medium" HorizontalOptions="CenterAndExpand" />
        </StackLayout>
        <t:DrMuscleButton x:Name="ConfirmCode" Style="{StaticResource buttonStyle}" Clicked="UnlockProgram_Clicked" VerticalOptions="End" Margin="20" TextColor="#195377" BorderColor="#195377" />
        <Grid HorizontalOptions="FillAndExpand" VerticalOptions="EndAndExpand" ColumnSpacing="1" RowSpacing="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width=".33*"/> 
                <ColumnDefinition Width=".33*"/> 
                <ColumnDefinition Width=".33*"/> 
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Button Grid.Row="0" Grid.Column="0" Text="1" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="0" Grid.Column="1" Text="2" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="0" Grid.Column="2" Text="3" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="1" Grid.Column="0" Text="4" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="1" Grid.Column="1" Text="5" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="1" Grid.Column="2" Text="6" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="2" Grid.Column="0" Text="7" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="2" Grid.Column="1" Text="8" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="2" Grid.Column="2" Text="9" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button Grid.Row="3" Grid.Column="1" Text="0" Style="{StaticResource PasscodeButtonStyle}" Clicked="BtnDigit_Clicked" />
            <Button x:Name="BtnErase" Grid.Row="3" Grid.Column="2" Text="" Style="{StaticResource PasscodeButtonStyle}" Clicked="Eraser_Clicked" />
        </Grid>
    </StackLayout>

</ContentPage>