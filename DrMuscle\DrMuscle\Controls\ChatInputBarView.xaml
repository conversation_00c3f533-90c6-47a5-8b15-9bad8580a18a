﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://xamarin.com/schemas/2014/forms" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" xmlns:controls="clr-namespace:DrMuscle.Controls" xmlns:app="clr-namespace:DrMuscle.Constants" x:Class="DrMuscle.Controls.ChatInputBarView">
    <Grid RowSpacing="0" ColumnSpacing="8" x:Name="frameGrid" Margin="{OnPlatform Android='2,0' , iOS= '8,0'}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="55" />
        </Grid.ColumnDefinitions>

        <Frame CornerRadius="10" IsClippedToBounds="True" Margin="0" HasShadow="False" Padding="0,5" BackgroundColor="{OnPlatform Android='#EAEAEA',iOS='#E2E2E2'}">
            <controls:ExtendedEditorControl 
                x:Name="chatTextInput"
                IsTextPredictionEnabled="True"
                BackgroundColor="#E2E2E2"
                IsSpellCheckEnabled="True" 
                Focused="chatTextInput_Focused"
                Unfocused="chatTextInput_Unfocused"
                Text="{Binding MessageText,Mode=TwoWay}"
                Margin="{OnPlatform Android='5,1',iOS='5,5,5,1'}"
                TextColor="Black" 
                IsExpandable="true"
                HorizontalOptions="FillAndExpand"
                PlaceholderColor="#616161"
                Placeholder="Message" 
                Grid.Row="0" 
                Grid.Column="0"
                TextChanged="chatTextInput_TextChanged" />
        </Frame>
        <Frame
            HasShadow="False"
            Padding="8,8,10,8"
            Margin="0"
            HeightRequest="40"
            VerticalOptions="End"
            x:Name="frmSendMessage"
            CornerRadius="10"
            BackgroundColor="#224968"
            Grid.Row="0" 
            Grid.Column="1" >
            <Image Source="SendMesageIcon" HeightRequest="10" WidthRequest="10"  Aspect="AspectFit"/>
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="Handle_Completed" />
            </Frame.GestureRecognizers>
        </Frame>
        <!--<Label Text="Send" FontAttributes="Bold" FontSize="15" HorizontalOptions="Center" Grid.Row="0" Grid.Column="1" TextColor="{x:Static app:AppThemeConstants.BlueColor}" x:Name="BtnSend" VerticalOptions="Center" VerticalTextAlignment="Center">
            <Label.GestureRecognizers>
                <TapGestureRecognizer Tapped="Handle_Completed" />
            </Label.GestureRecognizers>
        </Label>-->
    </Grid>
</ContentView>