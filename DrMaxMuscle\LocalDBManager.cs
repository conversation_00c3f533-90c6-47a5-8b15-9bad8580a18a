﻿using DrMaxMuscle.Dependencies;
#if ANDROID
using DrMaxMuscle.Platforms.Android.Dependencies;
#elif IOS
using DrMaxMuscle.Platforms.iOS.Dependencies;
#endif
using SQLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DrMaxMuscle
{
    public class LocalDBManager
    {
        //private SQLiteConnection database;
        //private static LocalDBManager instance;
        //public static LocalDBManager Instance
        //{
        //    get
        //    {
        //        if (instance == null)
        //            instance = new LocalDBManager();
        //        return instance;
        //    }
        //}
        //private LocalDBManager()
        //{
        //    database = DependencyService.Get<ISQLite>().GetConnection();
        //    InitDatabase();
        //}

        //        private SQLite.SQLiteConnection database;
        //        private static LocalDBManager instance;
        //        public static LocalDBManager Instance
        //        {
        //            get
        //            {
        //                if (instance == null)
        //                {
        //                    // Manual initialization fallback
        //#if ANDROID
        //                        instance = new LocalDBManager(new SQLite_Android());
        //#elif IOS
        //                        instance = new LocalDBManager(new SQLite_iOS());
        //                        // Add other platforms as needed
        //#else
        //                        throw new NotImplementedException("Platform-specific SQLite not implemented");
        //#endif
        //                }
        //                return instance;
        //            }
        //        }


        private SQLiteConnection database;
        private static LocalDBManager instance;
        public static LocalDBManager Instance
        {
            get
            {
                if (instance == null)
                    instance = new LocalDBManager();
                return instance;
            }
        }
        private LocalDBManager()
        {
            var sqlite = (ISQLite)MauiProgram.ServiceProvider.GetService(typeof(ISQLite));
            database = sqlite?.GetConnection();
            InitDatabase();
        }

        private void InitDatabase()
        {
            if (!TableExists<DBSetting>())
            {
                database.CreateTable<DBSetting>();
            }

            if (!TableExists<DBReco>())
            {
                database.CreateTable<DBReco>();
            }

            if (!TableExists<DBExercise1RM>())
            {
                database.CreateTable<DBExercise1RM>();
            }
        }
        private bool TableExists<T>()
        {
            const string cmdText = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
            var cmd = database.CreateCommand(cmdText, typeof(T).Name);
            return cmd.ExecuteScalar<string>() != null;
        }

        public DBSetting GetDBSetting(string key)
        {
            return database.Table<DBSetting>().FirstOrDefault(s => s.Key == key);
        }

        public DBReco GetDBReco(string key)
        {
            return database.Table<DBReco>().FirstOrDefault(s => s.Key == key);
        }

        public DBExercise1RM GetDBExercise1RMExist()
        {
            return database.Table<DBExercise1RM>().FirstOrDefault();
        }

        public DBExercise1RM GetDBExercise1RM(long exerciceId)
        {
            return database.Table<DBExercise1RM>().FirstOrDefault(s => s.ExerciseId == exerciceId);
        }

        public List<DBExercise1RM> GetDBAllExercise1RM()
        {
            return database.Table<DBExercise1RM>().ToList();
        }

        public void SetDBExerciceIRM(long exerciseId, string last1RM)
        {
            SetDBExerciceIRM(new DBExercise1RM() { ExerciseId = exerciseId, Last1RM = last1RM });
        }

        public void SetDBSetting(string key, string value)
        {
            SetDBSetting(new DBSetting() { Key = key, Value = value });
        }

        public void SetDBExerciceIRM(DBExercise1RM setting)
        {
            try
            {

            if (database.Table<DBExercise1RM>().Count(x => x.ExerciseId == setting.ExerciseId) == 0)
                database.Insert(setting);
            else
            {
                try
                {
                    database.Delete(setting);
                    database.Insert(setting);
                }
                catch (Exception ex)
                {

                }

            }

            }
            catch (Exception ex)
            {

            }
        }

        public void SetDBSetting(DBSetting setting)
        {
            try
            {

            if (database.Table<DBSetting>().Count(x => x.Key == setting.Key) == 0)
                database.Insert(setting);
            else
            {
                database.Update(setting);

            }

            }
            catch (Exception ex)
            {

            }
        }

        public void SetDBReco(string key, string value)
        {
            try
            {
                SetDBReco(new DBReco() { Key = key, Value = value });
            }
            catch (Exception ex)
            {

            }

        }
        public void SetDBReco(DBReco reco)
        {
            try
            {
                if (database.Table<DBReco>().Count(x => x.Key == reco.Key) == 0)
                database.Insert(reco);
            else
                database.Update(reco);
        }
            catch (Exception ex)
            {

            }
}

        internal void ResetReco()
        {
            if (database != null)
            {
                if (!TableExists<DBReco>())
                    database.DropTable<DBReco>();
            }
            InitDatabase();
        }

        internal void Reset1RMExercise()
        {
            if (database != null)
            {
                if (!TableExists<DBExercise1RM>())
                    database.DropTable<DBExercise1RM>();
            }
            InitDatabase();
        }

        internal void Reset()
        {
            bool onBoardingSeen = GetDBSetting("onboarding_seen") != null;
            string lastCalories = GetDBSetting("LastMealCaloriesValue") != null ? GetDBSetting("LastMealCaloriesValue").Value : "";
            string recommendedWorkoutId = GetDBSetting("recommendedWorkoutId") != null ? GetDBSetting("recommendedWorkoutId").Value : "";
            string recommendedWorkoutLabel = GetDBSetting("recommendedWorkoutLabel") != null ? GetDBSetting("recommendedWorkoutLabel").Value : "";
            string recommendedProgramId = GetDBSetting("recommendedProgramId") != null ? GetDBSetting("recommendedProgramId").Value : "";
            string recommendedProgramLabel = GetDBSetting("recommendedProgramLabel") != null ? GetDBSetting("recommendedProgramLabel").Value : "";
            string recommendedRemainingWorkout = GetDBSetting("recommendedRemainingWorkout") != null ? GetDBSetting("recommendedRemainingWorkout").Value : "";
            string environment = GetDBSetting("Environment") != null ? GetDBSetting("Environment").Value : "Production";
            string language = GetDBSetting("AppLanguage") != null ? GetDBSetting("AppLanguage").Value : "en";
            string appBackground = "DrMuscleLogo.png";
            database.DropTable<DBSetting>();
            if (!TableExists<DBReco>())
                database.DropTable<DBReco>();
            if (!TableExists<DBExercise1RM>())
                database.DropTable<DBExercise1RM>();
            InitDatabase();
            if (onBoardingSeen)
            {
                SetDBSetting("onboarding_seen", "true");
                //SetDBSetting("recommendedWorkoutId", recommendedWorkoutId == "" ? null : recommendedWorkoutId);
                SetDBSetting("recommendedWorkoutLabel", recommendedWorkoutLabel);
                SetDBSetting("recommendedProgramId", recommendedProgramId);
                SetDBSetting("recommendedProgramLabel", recommendedProgramLabel);
                SetDBSetting("recommendedRemainingWorkout", recommendedRemainingWorkout);

                SetDBSetting("AppLanguage", language);
                SetDBSetting("BackgroundImage", appBackground);
            }
            if (!string.IsNullOrEmpty(lastCalories))
            {
                SetDBSetting("LastMealCaloriesValue", lastCalories);
            }
            LocalDBManager.Instance.SetDBSetting("onboarding_features", "true");
            SetDBSetting("Environment", environment);
            var kgString = "25_20_True|20_20_True|15_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True|0.5_20_True";
            LocalDBManager.Instance.SetDBSetting("PlatesKg", kgString);

            var lbString = "45_20_True|35_20_True|25_20_True|10_20_True|5_20_True|2.5_20_True|1.25_20_True";
            LocalDBManager.Instance.SetDBSetting("PlatesLb", lbString);
            LocalDBManager.Instance.SetDBSetting("timer_remaining", "60");
            //if (!string.IsNullOrEmpty(firstname))
            //    SetDBSetting("firstname", firstname);
            //instance = new LocalDBManager();
        }
        public List<DBSetting> GetAllDBSettings(string keyStart, string keyEnd)
        {
            return database.Table<DBSetting>()
                .Where(s => s.Key.StartsWith(keyStart) && s.Key.EndsWith(keyEnd))
                .ToList();
        }
        public void DeleteDBSetting(string key)
        {
            var setting = database.Table<DBSetting>().FirstOrDefault(s => s.Key == key);
            if (setting != null)
            {
                database.Delete(setting);
            }
        }
    }
}