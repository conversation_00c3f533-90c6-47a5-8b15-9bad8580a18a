﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:DrMaxMuscle"
             xmlns:t="clr-namespace:DrMaxMuscle.Layout"
             x:Class="DrMaxMuscle.Screens.Workouts.AddWorkoutToWorkoutOrderPage"
             xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
             Title="AddWorkoutToWorkoutOrderPage">
    <Grid>
    <StackLayout Grid.Row="0" HorizontalOptions="FillAndExpand" x:Name="stkWorkout" VerticalOptions="FillAndExpand" Padding="20,0,20,0">
      <StackLayout VerticalOptions="FillAndExpand">
        <StackLayout BackgroundColor="Transparent" Padding="0" VerticalOptions="FillAndExpand">
          <t:DrMuscleListView x:Name="WorkoutListView" BackgroundColor="Transparent" VerticalOptions="FillAndExpand" SeparatorColor="#264457" SeparatorVisibility="Default" ios:ListView.SeparatorStyle="FullWidth" ios:ListView.GroupHeaderStyle="Grouped">
            <ListView.ItemTemplate>
              <DataTemplate>
                <ViewCell>
                  <StackLayout Orientation="Horizontal">
                    <StackLayout Orientation="Horizontal" HorizontalOptions="StartAndExpand">
                      <Label Text="{Binding Label}" HorizontalOptions="StartAndExpand" VerticalTextAlignment="Center" Style="{StaticResource LabelStyle}"></Label>
                    </StackLayout>
                    <StackLayout Orientation="Horizontal" HorizontalOptions="EndAndExpand">
                      <Switch IsToggled="{Binding IsSelected}" VerticalOptions="Center" HorizontalOptions="End" Toggled="Handle_Toggled"></Switch>
                    </StackLayout>
                  </StackLayout>
                </ViewCell>
              </DataTemplate>
            </ListView.ItemTemplate>
          </t:DrMuscleListView>
        </StackLayout>
      </StackLayout>
      <StackLayout Orientation="Horizontal" VerticalOptions="End" Padding="0,0,0,20">
                <t:DrMuscleButton x:Name="NextButton" TextTransform="Uppercase" BorderColor="#195377" HorizontalOptions="FillAndExpand" Style="{StaticResource buttonStyle}"></t:DrMuscleButton>
      </StackLayout>
    </StackLayout>
      <StackLayout Grid.Row="0" VerticalOptions="CenterAndExpand" HorizontalOptions="FillAndExpand" x:Name="EmptyWorkouts" IsVisible="false"  Spacing="10" Padding="0,25,0,2">

                <Image WidthRequest="70" HeightRequest="70" HorizontalOptions="Center" VerticalOptions="Start" Source="lists.png" />


                    <Label Text="No custom workout yet" Margin="0,5,0,0" HorizontalOptions="Center" FontSize="20" FontAttributes="Bold" TextColor="Black" />
                    <Label Text="Tap &quot;+&quot; to create your custom workout" HorizontalOptions="Center"  HorizontalTextAlignment="Center" FontSize="17" TextColor="#505050" FontAttributes="Bold"/>

            </StackLayout>
  </Grid>
</ContentPage>
