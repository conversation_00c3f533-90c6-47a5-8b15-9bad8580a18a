﻿<?xml version="1.0" encoding="UTF-8"?>
<ViewCell
    xmlns="http://xamarin.com/schemas/2014/forms"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="DrMuscle.Cells.QuestionCell">
    <Frame
        Margin="10,10,40,5"
        CornerRadius="12"
        x:Name="FrmContainer"
        Padding="20,12,20,12"
        HorizontalOptions="Start"
        BorderColor="#ffffff"
        OutlineColor="#ffffff"
        HasShadow="False"
        Opacity="0"
        BackgroundColor="#ffffff">
        <Label
            x:Name="LblAnswer"
            FontSize="17"
            LineHeight="{OnPlatform Android='1.3',iOS='1.2'}" 
            TextColor="#26262B"
            Text="{Binding Question}"
            HorizontalOptions="Start"
            Margin="4,0" />
    </Frame>
</ViewCell>
