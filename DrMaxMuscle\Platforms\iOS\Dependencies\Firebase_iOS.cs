﻿using DrMaxMuscle.Dependencies;
using DrMaxMuscle.Platforms.iOS.Dependencies;
using Foundation;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Firebase.Analytics;

//[assembly: Dependency(typeof(Firebase_iOS))]
namespace DrMaxMuscle.Platforms.iOS.Dependencies
{
    public class Firebase_iOS : IFirebase
    {
        public void LogEvent(string key, string val)
        {
            if (string.IsNullOrEmpty(val))
                val = "none";
            NSString[] keys = { new NSString(key) };
            NSObject[] values = { new NSString(val) };
            
            var parameters = NSDictionary<NSString, NSObject>.FromObjectsAndKeys(keys, values, keys.Length);
            try
            {

                if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
                { }
                else if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.ToLower().Equals("<EMAIL>"))
                {

                }
                else
                    Analytics.LogEvent(key, parameters);

            }
            catch (Exception ex)
            {

            }
        }

        public void SetScreenName(string val)
        {
            try
            {

                if (LocalDBManager.Instance.GetDBSetting("email") != null && LocalDBManager.Instance.GetDBSetting("email").Value.Contains("yopmail") || (LocalDBManager.Instance.GetDBSetting("Environment") != null && LocalDBManager.Instance.GetDBSetting("Environment").Value != "Production"))
                { }
                else
                    Analytics.LogEvent(val, new NSDictionary<NSString, NSObject>() { });

            }
            catch (Exception ex)
            {

            }

        }

        public void SetUserId(string name)
        {
            // Uncomment code please
            Analytics.SetUserId(name);
        }

    }
}
